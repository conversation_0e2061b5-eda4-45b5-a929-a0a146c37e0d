# 🔧 CORS Error Fix Guide

## ✅ CORS Configuration Updated!

I've updated the CORS configuration in your Express backend to be more permissive and handle all the common CORS issues.

## 🔧 Changes Made

### 1. Enhanced CORS Configuration (`nibret-express-api/src/app.js`)
- ✅ **Multiple allowed origins**: localhost:5173, localhost:3000, 127.0.0.1 variants
- ✅ **Preflight handling**: Explicit OPTIONS request handling
- ✅ **Credentials support**: Enabled for authentication
- ✅ **All HTTP methods**: GET, POST, PUT, DELETE, PATCH, OPTIONS
- ✅ **Required headers**: Content-Type, Authorization, X-Requested-With
- ✅ **Development mode**: Allows all origins in development

### 2. Updated Vite Configuration (`nibret-home-finder-ethiopia/vite.config.ts`)
- ✅ **Correct port**: Changed from 8080 to 5173
- ✅ **CORS enabled**: Added `cors: true` to server config

## 🚀 Restart Both Servers

After the CORS configuration changes, you need to restart both servers:

### Terminal 1 - Restart Backend:
```bash
cd nibret-express-api

# Stop the current server (Ctrl+C)
# Then restart:
npm run dev
```

### Terminal 2 - Restart Frontend:
```bash
cd nibret-home-finder-ethiopia

# Stop the current server (Ctrl+C)  
# Then restart:
npm run dev
```

## 🧪 Test CORS Configuration

### Option 1: Test with Script
```bash
cd nibret-express-api
node test-cors.js
```

### Option 2: Test in Browser
1. Open browser console (F12)
2. Go to `http://localhost:5173`
3. Try to login or search
4. Check for CORS errors in console

### Option 3: Test with curl
```bash
# Test preflight request
curl -X OPTIONS http://localhost:3000/accounts/login/ \
  -H "Origin: http://localhost:5173" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type" \
  -v

# Test actual request
curl -X POST http://localhost:3000/accounts/login/ \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:5173" \
  -d '{"username":"**********","password":"nibretadmin"}' \
  -v
```

## 🔍 Common CORS Issues & Solutions

### Issue 1: "Access to fetch at 'http://localhost:3000' from origin 'http://localhost:5173' has been blocked by CORS policy"

**Solution:**
- ✅ Already fixed with updated CORS config
- Restart both servers
- Clear browser cache

### Issue 2: "CORS policy: Request header field authorization is not allowed"

**Solution:**
- ✅ Already fixed - Authorization header is now allowed
- Make sure you restarted the backend server

### Issue 3: "CORS policy: The request client is not a secure context"

**Solution:**
- Use `http://localhost:5173` instead of `http://127.0.0.1:5173`
- Or add both to allowed origins (already done)

### Issue 4: "CORS policy: Credentials flag is 'true', but the 'Access-Control-Allow-Credentials' header is absent"

**Solution:**
- ✅ Already fixed - credentials are enabled in CORS config

## 🛠️ Additional Troubleshooting

### Clear Browser Data
```bash
# Chrome/Edge: 
# 1. F12 -> Application tab -> Storage -> Clear storage
# 2. Or Ctrl+Shift+Delete -> Clear browsing data

# Firefox:
# 1. F12 -> Storage tab -> Clear all
# 2. Or Ctrl+Shift+Delete
```

### Check Network Tab
1. Open browser DevTools (F12)
2. Go to Network tab
3. Try to make a request
4. Look for:
   - ✅ OPTIONS request (preflight) - should return 200
   - ✅ Actual request - should return expected response
   - ❌ Any red/failed requests

### Verify Server URLs
- **Backend**: `http://localhost:3000/health` should return JSON
- **Frontend**: `http://localhost:5173` should load the app

## 📊 Expected CORS Headers

When working correctly, you should see these headers in the response:

```
Access-Control-Allow-Origin: http://localhost:5173
Access-Control-Allow-Credentials: true
Access-Control-Allow-Methods: GET,POST,PUT,DELETE,PATCH,OPTIONS
Access-Control-Allow-Headers: Content-Type,Authorization,X-Requested-With
```

## 🔧 If CORS Still Doesn't Work

### Temporary Development Fix
If you're still having issues, you can temporarily disable CORS checking in your browser:

**Chrome/Edge:**
```bash
# Close all browser instances first, then:
google-chrome --disable-web-security --user-data-dir="/tmp/chrome_dev_session"
```

**Note:** Only use this for development, never for production!

### Alternative: Use Browser Extension
Install a CORS browser extension like "CORS Unblock" for temporary development.

## ✅ Success Indicators

You'll know CORS is working when:
- ✅ No CORS errors in browser console
- ✅ API requests complete successfully
- ✅ Login/registration works
- ✅ Properties load from backend
- ✅ Network tab shows successful requests

## 🚨 Production Notes

For production deployment:
1. **Update CORS origins** to your actual domain
2. **Remove development allowances** (like allowing all origins)
3. **Use HTTPS** for both frontend and backend
4. **Set proper environment variables**

Example production CORS config:
```javascript
const corsOptions = {
  origin: ['https://yourdomain.com', 'https://www.yourdomain.com'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization']
};
```

## 📞 Still Having Issues?

If CORS errors persist:
1. **Check server logs** for any error messages
2. **Verify both servers are running** on correct ports
3. **Test API directly** with curl or Postman
4. **Check firewall/antivirus** settings
5. **Try a different browser** or incognito mode

The CORS configuration is now much more robust and should handle all common scenarios! 🎉
