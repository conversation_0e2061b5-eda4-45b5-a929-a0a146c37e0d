# 🚀 Quick Start Guide - Nibret Real Estate Platform

## ✅ Frontend Configuration Updated!

The frontend is now configured to use `http://localhost:3000` as the backend API URL.

## 🏃‍♂️ Start the Application

### 1. Start the Express Backend

```bash
# Navigate to backend directory
cd nibret-express-api

# Install dependencies (if not done already)
npm install

# Start MongoDB (choose one option):

# Option A: Local MongoDB
brew services start mongodb-community

# Option B: Docker MongoDB
docker run -d --name mongodb -p 27017:27017 mongo:latest

# Option C: MongoDB Atlas (update .env with your connection string)

# Seed sample data (optional but recommended)
npm run seed

# Start the backend server
npm run dev
```

You should see:
```
🚀 Nibret Express API server running on port 3000
📊 Environment: development
🌐 Health check: http://localhost:3000/health
📦 MongoDB Connected: localhost:27017
```

### 2. Start the Frontend

```bash
# Open a new terminal and navigate to frontend directory
cd nibret-home-finder-ethiopia

# Install dependencies (if not done already)
npm install

# Start the frontend development server
npm run dev
```

You should see:
```
  VITE v5.x.x  ready in xxx ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
```

## 🧪 Test the Authentication System

### 1. Visit the Application
Open your browser and go to: `http://localhost:5173`

### 2. Test Guest Experience
- Browse the homepage (you'll see limited properties with hidden addresses)
- Try to search for properties → Auth modal should appear
- Click on a property → Auth modal should appear
- Try to use filters → Auth modal should appear

### 3. Test Authentication
**Login with Admin Credentials:**
- Username: `**********`
- Password: `nibretadmin`

**Or Register a New Account:**
- Fill out the registration form
- Use any valid email and phone number

### 4. Test Authenticated Experience
After logging in:
- ✅ See all properties with full addresses
- ✅ Use search functionality
- ✅ Use advanced filters
- ✅ Click on properties to view details
- ✅ See user dropdown in navbar
- ✅ Access admin panel (if admin user)

### 5. Test API Directly (Optional)

```bash
# Test health endpoint
curl http://localhost:3000/health

# Test admin login
curl -X POST http://localhost:3000/accounts/login/ \
  -H "Content-Type: application/json" \
  -d '{"username":"**********","password":"nibretadmin"}'

# Test properties endpoint
curl -X POST http://localhost:3000/properties/list \
  -H "Content-Type: application/json" \
  -d '{"username":"**********","password":"nibretadmin"}'
```

## 🔧 Configuration Details

### Backend (Express API)
- **URL**: `http://localhost:3000`
- **Database**: MongoDB on `localhost:27017`
- **Admin Credentials**: `**********` / `nibretadmin`

### Frontend (React/Vite)
- **URL**: `http://localhost:5173`
- **API Base URL**: `http://localhost:3000` (configured in `.env`)

## 🎯 Key Features to Test

### Authentication Protection
- [x] Homepage shows limited properties for guests
- [x] Addresses are hidden for non-authenticated users
- [x] Search requires authentication
- [x] Property clicks show auth modal for guests
- [x] Full access after login

### User Management
- [x] Registration with validation
- [x] Login with JWT tokens
- [x] User dropdown in navbar
- [x] Logout functionality
- [x] Persistent authentication across page refreshes

### Property Features
- [x] Property listing with search/filter
- [x] Property details view
- [x] Map integration
- [x] Admin property management

### Admin Features
- [x] Admin panel access
- [x] User management
- [x] Property management
- [x] CRM functionality

## 🐛 Troubleshooting

### Backend Issues
```bash
# Check if MongoDB is running
ps aux | grep mongod

# Check backend logs
cd nibret-express-api
npm run dev
# Look for any error messages
```

### Frontend Issues
```bash
# Check frontend logs
cd nibret-home-finder-ethiopia
npm run dev
# Look for any error messages in terminal or browser console
```

### Common Issues
1. **Port 3000 already in use**: Kill the process using `lsof -i :3000` and `kill -9 <PID>`
2. **MongoDB connection failed**: Make sure MongoDB is running
3. **CORS errors**: Backend is configured for `localhost:5173`
4. **Authentication not persisting**: Check browser localStorage for tokens

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ Backend starts without errors and connects to MongoDB
- ✅ Frontend loads at `http://localhost:5173`
- ✅ Guest users see limited content with auth prompts
- ✅ Login/registration works and grants full access
- ✅ User dropdown appears in navbar after login
- ✅ Admin users can access admin panel
- ✅ Properties load from the backend API

## 📞 Need Help?

If you encounter any issues:
1. Check the terminal logs for both frontend and backend
2. Check browser console for JavaScript errors
3. Verify MongoDB is running and accessible
4. Test API endpoints directly with curl
5. Clear browser localStorage if authentication seems stuck

Happy coding! 🚀
