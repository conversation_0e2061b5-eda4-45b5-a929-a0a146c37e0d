# Nibret Real Estate Platform - Deployment Guide

## 🌐 Environment Configuration

### Frontend Environment Variables

#### Development (.env.local)
```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=10000

# Cloudinary Configuration
VITE_CLOUDINARY_CLOUD_NAME=your-cloud-name
VITE_CLOUDINARY_API_KEY=your-api-key

# Environment
VITE_NODE_ENV=development
```

#### Production (.env.production)
```bash
# API Configuration - Production
VITE_API_BASE_URL=https://api.nibret.com
VITE_API_TIMEOUT=15000

# Cloudinary Configuration - Production
VITE_CLOUDINARY_CLOUD_NAME=nibret-production
VITE_CLOUDINARY_API_KEY=your-production-api-key

# Environment
VITE_NODE_ENV=production
```

### Backend Environment Variables

#### Development (.env)
```bash
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/nibret_db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# CORS Configuration
ALLOWED_ORIGINS=*
```

#### Production (.env.production)
```bash
# Server Configuration - Production
PORT=3000
NODE_ENV=production

# Database Configuration - Production
MONGODB_URI=mongodb+srv://username:<EMAIL>/nibret_production

# JWT Configuration - Production
JWT_SECRET=your-super-secure-production-jwt-secret-here
JWT_EXPIRES_IN=24h

# Cloudinary Configuration - Production
CLOUDINARY_CLOUD_NAME=nibret-production
CLOUDINARY_API_KEY=your-production-api-key
CLOUDINARY_API_SECRET=your-production-api-secret

# CORS Configuration - Production
ALLOWED_ORIGINS=https://nibret.com,https://www.nibret.com,https://app.nibret.com
```

## 🚀 Deployment Steps

### 1. Backend Deployment

#### Option A: Manual Deployment
```bash
# 1. Clone repository on server
git clone https://github.com/your-username/nibret.git
cd nibret/nibret-api

# 2. Create production environment file
cp .env.example .env.production
# Edit .env.production with your production values

# 3. Run deployment script
chmod +x scripts/deploy-production.sh
./scripts/deploy-production.sh
```

#### Option B: Using PM2
```bash
# Install PM2 globally
npm install -g pm2

# Deploy with PM2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### 2. Frontend Deployment

#### Option A: Static Hosting (Netlify, Vercel, etc.)
```bash
# 1. Build for production
cd nibret-home-finder-ethiopia
chmod +x scripts/build-production.sh
./scripts/build-production.sh

# 2. Deploy dist folder to your hosting service
```

#### Option B: Manual Server Deployment
```bash
# 1. Build the application
npm run build

# 2. Upload dist folder to your web server
# 3. Configure web server (nginx example below)
```

### 3. Web Server Configuration (Nginx)

```nginx
# Frontend (SPA)
server {
    listen 80;
    server_name nibret.com www.nibret.com;
    
    root /var/www/nibret/dist;
    index index.html;
    
    # Handle SPA routing
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Cache static assets
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# Backend API
server {
    listen 80;
    server_name api.nibret.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔧 Configuration Checklist

### Before Deployment

#### Backend
- [ ] Set up MongoDB database (local or cloud)
- [ ] Configure Cloudinary account
- [ ] Generate secure JWT secret
- [ ] Set up email service (optional)
- [ ] Configure CORS origins for production
- [ ] Set up SSL certificates

#### Frontend
- [ ] Update API base URL to production
- [ ] Configure Cloudinary for production
- [ ] Test build process
- [ ] Set up domain and DNS

### After Deployment

#### Backend
- [ ] Test API health endpoint
- [ ] Verify database connection
- [ ] Test authentication endpoints
- [ ] Test property CRUD operations
- [ ] Test image upload functionality
- [ ] Monitor logs for errors

#### Frontend
- [ ] Test all pages load correctly
- [ ] Verify API integration works
- [ ] Test authentication flow
- [ ] Test property upload (admin only)
- [ ] Test property search and filtering
- [ ] Test responsive design

## 🔍 Monitoring and Maintenance

### Health Checks
```bash
# Backend health check
curl https://api.nibret.com/health

# Frontend check
curl https://nibret.com

# Database connection check
curl https://api.nibret.com/properties/list -X POST \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'
```

### Log Monitoring
```bash
# PM2 logs
pm2 logs nibret-api

# Application logs
tail -f logs/production.log

# Error logs
tail -f logs/error.log
```

### Performance Monitoring
- Set up monitoring for API response times
- Monitor database performance
- Track frontend loading times
- Monitor Cloudinary usage

## 🔒 Security Considerations

### Production Security
- Use HTTPS for all communications
- Implement rate limiting
- Use strong JWT secrets
- Regularly update dependencies
- Monitor for security vulnerabilities
- Implement proper CORS policies
- Use environment variables for all secrets

### Database Security
- Use MongoDB Atlas with proper authentication
- Implement database backups
- Use connection string with authentication
- Limit database access to specific IPs

## 📊 Environment URLs

### Development
- Frontend: http://localhost:5173
- Backend: http://localhost:3000
- API Health: http://localhost:3000/health

### Production
- Frontend: https://nibret.com
- Backend: https://api.nibret.com
- API Health: https://api.nibret.com/health

## 🆘 Troubleshooting

### Common Issues

#### CORS Errors
- Check ALLOWED_ORIGINS in backend .env
- Verify frontend API_BASE_URL points to correct backend

#### Database Connection Issues
- Verify MONGODB_URI is correct
- Check network connectivity
- Ensure database user has proper permissions

#### Image Upload Issues
- Verify Cloudinary credentials
- Check file size limits
- Ensure proper CORS configuration

#### Authentication Issues
- Verify JWT_SECRET is set
- Check token expiration settings
- Ensure proper CORS for credentials
