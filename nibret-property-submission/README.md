# Nibret Property Submission Form

A standalone React application for property owners to submit their properties to the Nibret Real Estate Platform. This form provides a step-by-step guided process for users to list their properties for sale or rent.

## Features

- **Step-by-Step Form**: 8-step guided process for easy property submission
- **Real-time Validation**: Form validation with helpful error messages
- **Progress Tracking**: Visual progress bar and step indicators
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **API Integration**: Connects to existing Nibret API backend
- **Currency Support**: ETB and USD pricing options
- **Image Upload**: Support for property photos
- **Contact Management**: Collects owner contact information

## Form Steps

1. **Property Type & Purpose** - Property type and listing purpose (sale/rent)
2. **Basic Information** - Title, description, and pricing
3. **Property Details** - Bedrooms, bathrooms, square footage, etc.
4. **Location** - Address, city, and region
5. **Features & Amenities** - Property features and amenities
6. **Photos** - Property image uploads
7. **Contact Information** - Owner contact details
8. **Review & Submit** - Final review and submission

## Prerequisites

- Node.js 18+ and npm
- Running Nibret API backend (nibret-api)
- MongoDB database

## Installation

1. **Clone or create the project directory:**
   ```bash
   cd nibret-property-submission
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure environment variables:**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   VITE_API_URL=http://localhost:3000
   VITE_ADMIN_USERNAME=0965789832
   VITE_ADMIN_PASSWORD=nibretadmin
   ```

4. **Start the development server:**
   ```bash
   npm run dev
   ```

5. **Open your browser:**
   Navigate to `http://localhost:3001`

## API Integration

This application connects to the existing Nibret API backend:

- **Endpoint**: `POST /properties`
- **Authentication**: Uses admin credentials for property submission
- **Data Format**: Matches the existing Property model schema

### Required Backend Setup

Ensure the Nibret API backend is running with:
- MongoDB connection
- Property model with currency field support
- CORS enabled for the submission form domain

## Project Structure

```
nibret-property-submission/
├── src/
│   ├── components/
│   │   ├── ui/                 # Reusable UI components
│   │   ├── PropertySubmissionForm.tsx
│   │   └── SuccessPage.tsx
│   ├── services/
│   │   └── api.ts             # API service layer
│   ├── types/
│   │   └── property.ts        # TypeScript interfaces
│   ├── lib/
│   │   └── utils.ts           # Utility functions
│   ├── App.tsx
│   ├── main.tsx
│   └── index.css
├── public/
├── package.json
└── README.md
```

## Configuration

### Environment Variables

- `VITE_API_URL`: Backend API URL (default: http://localhost:3000)
- `VITE_ADMIN_USERNAME`: Admin username for API authentication
- `VITE_ADMIN_PASSWORD`: Admin password for API authentication

### Customization

1. **Branding**: Update colors in `tailwind.config.js` and CSS files
2. **Form Steps**: Modify `FORM_STEPS` in `src/types/property.ts`
3. **Validation**: Update schema in `PropertySubmissionForm.tsx`
4. **API Integration**: Modify `src/services/api.ts` for different backends

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Adding New Form Steps

1. Add step definition to `FORM_STEPS` in `src/types/property.ts`
2. Create step component in `src/components/steps/`
3. Add step rendering logic to `PropertySubmissionForm.tsx`
4. Update validation schema if needed

## Deployment

### Production Build

```bash
npm run build
```

The built files will be in the `dist/` directory.

### Environment Setup

For production deployment:

1. Update `VITE_API_URL` to production API URL
2. Configure proper admin credentials
3. Set up HTTPS for secure form submission
4. Configure CORS on the backend for the form domain

### Hosting Options

- **Vercel**: Connect GitHub repo for automatic deployments
- **Netlify**: Drag and drop `dist/` folder or connect repo
- **AWS S3 + CloudFront**: Upload build files to S3 bucket
- **Traditional Web Server**: Serve files from `dist/` directory

## Security Considerations

- Admin credentials are used for API authentication
- Form data is validated on both client and server
- HTTPS should be used in production
- Consider rate limiting on the backend
- Implement CAPTCHA for spam prevention if needed

## Integration with Main Platform

This form can be:

1. **Embedded**: As an iframe in the main Nibret website
2. **Standalone**: Hosted on a subdomain (e.g., submit.nibret.com)
3. **Integrated**: Incorporated into the main platform codebase

## Support

For technical support or questions:

- Email: <EMAIL>
- Phone: +251 91 123 4567

## License

This project is part of the Nibret Real Estate Platform.
