import fs from 'fs';

async function fetchAllProperties() {
  console.log('🔍 Fetching all properties from Nibret API...');
  
  try {
    // Fetch first batch
    const response1 = await fetch('https://api.nibret.com/properties/');
    const data1 = await response1.json();
    
    let allProperties = data1.results || [];
    console.log(`✅ Fetched first ${allProperties.length} properties`);
    
    // Fetch second batch if there's a next URL
    if (data1.next) {
      const response2 = await fetch(data1.next);
      const data2 = await response2.json();
      const moreProperties = data2.results || [];
      allProperties = allProperties.concat(moreProperties);
      console.log(`✅ Fetched additional ${moreProperties.length} properties`);
    }
    
    console.log(`📊 Total properties fetched: ${allProperties.length}`);
    
    // Save to JSON file
    fs.writeFileSync('properties-data.json', JSON.stringify(allProperties, null, 2));
    console.log('💾 Saved all properties to properties-data.json');
    
    // Display summary
    console.log('\n📋 PROPERTY SUMMARY:');
    const summary = {};
    allProperties.forEach(prop => {
      const type = prop.type || 'Unknown';
      summary[type] = (summary[type] || 0) + 1;
    });
    
    Object.entries(summary).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} properties`);
    });
    
    return allProperties;
    
  } catch (error) {
    console.error('❌ Error fetching properties:', error.message);
    throw error;
  }
}

// Run if called directly
fetchAllProperties();
