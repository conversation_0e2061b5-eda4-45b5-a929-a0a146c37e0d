import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const LOCAL_API_URL = 'http://localhost:3000';
const ADMIN_CREDENTIALS = {
  username: '0965789832',
  password: 'nibretadmin'
};

// Default contact info for all properties
const DEFAULT_CONTACT = {
  name: 'Nibret Admin',
  phone: '+251965789832',
  email: '<EMAIL>',
  agent_name: 'Nibret Real Estate'
};

// Load property data from JSON file
function loadPropertiesData() {
  try {
    const dataPath = path.join(__dirname, 'properties-data.json');
    const rawData = fs.readFileSync(dataPath, 'utf8');
    return JSON.parse(rawData);
  } catch (error) {
    console.error('❌ Error loading properties data:', error.message);
    return [];
  }
}

// Test backend connection
async function testConnection() {
  try {
    const response = await fetch(`${LOCAL_API_URL}/properties/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(ADMIN_CREDENTIALS),
    });

    if (!response.ok) {
      throw new Error(`Backend connection failed: ${response.status}`);
    }

    const data = await response.json();
    console.log(`✅ Backend connected. Found ${Array.isArray(data) ? data.length : 0} existing properties`);
    return true;
  } catch (error) {
    console.error('❌ Backend connection error:', error.message);
    throw error;
  }
}

// Map property data to your backend format
function mapPropertyData(property) {
  // Determine property type
  let propertyType = 'house';
  if (property.type.toLowerCase().includes('apartment')) {
    propertyType = 'apartment';
  } else if (property.type.toLowerCase().includes('villa')) {
    propertyType = 'villa';
  } else if (property.type.toLowerCase().includes('office')) {
    propertyType = 'other';
  }

  // Determine status and listing type
  let status = property.rental ? 'for_rent' : 'for_sale';
  let listing_type = property.rental ? 'rent' : 'sale';

  // Extract images
  const images = property.pictures.map(pic => pic.image_url);

  // Generate features based on description
  const features = [];
  const desc = property.description.toLowerCase();
  if (desc.includes('elevator')) features.push('Elevator');
  if (desc.includes('generator')) features.push('Backup Generator');
  if (desc.includes('security')) features.push('Security System');
  if (desc.includes('gym')) features.push('Gym/Fitness Center');
  if (desc.includes('sauna')) features.push('Sauna');
  if (desc.includes('steam')) features.push('Steam Bath');
  if (desc.includes('parking')) features.push('Parking');
  if (desc.includes('furnished')) features.push('Furnished');
  if (desc.includes('kitchen')) features.push('Modern Kitchen');
  if (desc.includes('laundry')) features.push('Laundry Room');

  return {
    // Authentication
    username: '0965789832',
    password: 'nibretadmin',

    // Basic Information
    title: `${property.name} - ${property.location.name}`,
    description: property.description,
    price: property.price,
    currency: property.currency,

    // Property Details
    beds: property.bedroom || 0,
    baths: property.bathroom || 0,
    sqft: Math.max(property.area || 100, 1), // Ensure minimum 1 sqft
    yearBuilt: 2020, // Default year since not provided in source data

    // Location (simplified - backend only accepts address)
    address: `${property.location.name}, Addis Ababa`,

    // Property Type & Status
    propertyType: propertyType,
    status: status,
    listing_type: listing_type,

    // Features & Images
    features: features,
    images: images
  };
}

// Upload single property
async function uploadProperty(propertyData, index) {
  try {
    console.log(`📤 Uploading property ${index + 1}: ${propertyData.title}`);

    const response = await fetch(`${LOCAL_API_URL}/properties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(propertyData),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Upload failed (${response.status}): ${errorData}`);
    }

    const result = await response.json();
    console.log(`✅ Successfully uploaded: ${propertyData.title}`);
    return result;
  } catch (error) {
    console.error(`❌ Failed to upload ${propertyData.title}:`, error.message);
    return null;
  }
}

// Main upload function
async function uploadAllProperties() {
  console.log('🚀 Starting property upload process...\n');

  try {
    // Step 1: Load property data
    console.log('📂 Loading property data...');
    const PROPERTIES_DATA = loadPropertiesData();

    if (PROPERTIES_DATA.length === 0) {
      throw new Error('No property data found');
    }

    console.log(`✅ Loaded ${PROPERTIES_DATA.length} properties\n`);

    // Step 2: Test backend connection
    console.log('🔗 Testing backend connection...');
    await testConnection();
    console.log('');

    // Step 3: Upload properties
    console.log(`📋 Starting upload of ${PROPERTIES_DATA.length} properties\n`);

    const results = {
      successful: 0,
      failed: 0,
      errors: []
    };

    for (let i = 0; i < PROPERTIES_DATA.length; i++) {
      const property = PROPERTIES_DATA[i];
      const mappedData = mapPropertyData(property);

      const result = await uploadProperty(mappedData, i);

      if (result) {
        results.successful++;
      } else {
        results.failed++;
        results.errors.push(property.name);
      }

      // Add delay between uploads to avoid overwhelming the server
      if (i < PROPERTIES_DATA.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // Step 4: Summary
    console.log('\n📊 UPLOAD SUMMARY:');
    console.log(`✅ Successful uploads: ${results.successful}`);
    console.log(`❌ Failed uploads: ${results.failed}`);

    if (results.errors.length > 0) {
      console.log(`\n❌ Failed properties:`);
      results.errors.forEach(name => console.log(`   - ${name}`));
    }

    console.log('\n🎉 Upload process completed!');

  } catch (error) {
    console.error('💥 Upload process failed:', error.message);
    process.exit(1);
  }
}

// Run the upload
uploadAllProperties();
