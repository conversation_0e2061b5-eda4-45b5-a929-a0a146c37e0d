@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for the property submission form */
.step-indicator {
  @apply flex items-center justify-center w-8 h-8 md:w-10 md:h-10 rounded-full text-sm font-medium transition-all duration-200;
}

.step-indicator.active {
  @apply bg-nibret-blue text-white shadow-lg;
}

.step-indicator.completed {
  @apply bg-nibret-gold text-white shadow-lg;
}

.step-indicator.inactive {
  @apply bg-gray-200 text-gray-500;
}

.form-section {
  @apply space-y-4 md:space-y-6 p-4 md:p-6 bg-white rounded-lg shadow-sm border;
}

.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply h-full bg-gradient-to-r from-nibret-blue to-nibret-gold transition-all duration-500 ease-out;
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  .container {
    @apply px-3;
  }

  .step-transition {
    @apply px-2;
  }

  /* Improve touch targets on mobile */
  .step-indicator {
    @apply min-w-[2.5rem] min-h-[2.5rem];
  }

  /* Better spacing for mobile forms */
  .form-section {
    @apply space-y-4;
  }
}

/* Responsive text sizing */
@media (max-width: 640px) {
  h1 {
    @apply text-xl;
  }

  h2 {
    @apply text-lg;
  }

  h3 {
    @apply text-base;
  }
}
