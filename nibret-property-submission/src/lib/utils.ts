import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: number, currency: 'ETB' | 'USD' = 'ETB'): string {
  if (currency === 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(price);
  } else {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      maximumFractionDigits: 0
    }).format(price);
  }
}

export function validatePhoneNumber(phone: string): boolean {
  // Ethiopian phone number validation
  const phoneRegex = /^(\+251|0)?[79]\d{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

export function formatPhoneNumber(phone: string): string {
  // Format Ethiopian phone number
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.startsWith('251')) {
    return `+${cleaned}`;
  } else if (cleaned.startsWith('0')) {
    return `+251${cleaned.slice(1)}`;
  } else if (cleaned.length === 9) {
    return `+251${cleaned}`;
  }
  return phone;
}
