/* Additional app-specific styles */
.nibret-gradient {
  background: linear-gradient(135deg, #1A365D 0%, #2D5A87 50%, #D4AF37 100%);
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.step-transition {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.success-animation {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
