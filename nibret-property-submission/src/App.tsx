import { FC } from 'react'
import { <PERSON><PERSON>er<PERSON>outer as Router, Routes, Route } from 'react-router-dom'
import PropertySubmissionForm from './components/PropertySubmissionForm'
import SuccessPage from './components/SuccessPage'
import { Toaster } from './components/ui/toaster'
import { LanguageProvider } from './context/LanguageContext'
import './App.css'

const App: FC = () => {
  return (
    <LanguageProvider>
      <Router>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
          <Routes>
            <Route path="/" element={<PropertySubmissionForm />} />
            <Route path="/success" element={<SuccessPage />} />
          </Routes>
          <Toaster />
        </div>
      </Router>
    </LanguageProvider>
  )
};

export default App
