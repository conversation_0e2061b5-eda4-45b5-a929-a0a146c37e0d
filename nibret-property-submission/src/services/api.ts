import { PropertySubmission } from '../types/property';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

// Default admin credentials for property submission
const ADMIN_CREDENTIALS = {
  username: import.meta.env.VITE_ADMIN_USERNAME || '0965789832',
  password: import.meta.env.VITE_ADMIN_PASSWORD || 'nibretadmin'
};

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class PropertySubmissionAPI {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || data.message || 'An error occurred'
        };
      }

      return {
        success: true,
        data: data.data || data
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error occurred'
      };
    }
  }

  async submitProperty(propertyData: PropertySubmission): Promise<ApiResponse<any>> {
    // Transform the submission data to match the API format
    const apiPropertyData = {
      // Include admin credentials for authentication
      username: ADMIN_CREDENTIALS.username,
      password: ADMIN_CREDENTIALS.password,
      
      // Property information
      title: propertyData.title,
      description: propertyData.description,
      price: propertyData.price,
      currency: propertyData.currency,
      beds: propertyData.beds,
      baths: propertyData.baths,
      sqft: propertyData.sqft,
      address: `${propertyData.address}, ${propertyData.city}, ${propertyData.region}`,
      lat: propertyData.lat || 9.0320, // Default to Addis Ababa coordinates
      lng: propertyData.lng || 38.7469,
      propertyType: propertyData.propertyType,
      status: propertyData.status,
      listing_type: propertyData.listing_type,
      images: propertyData.images,
      yearBuilt: propertyData.yearBuilt,
      lotSize: propertyData.lotSize,
      features: propertyData.features,
      contact_info: {
        phone: propertyData.contact_info.phone,
        email: propertyData.contact_info.email,
        agent_name: propertyData.contact_info.name
      },
      
      // Set as published by default for user submissions
      publish_status: 'published'
    };

    return this.makeRequest<any>('/properties', {
      method: 'POST',
      body: JSON.stringify(apiPropertyData)
    });
  }

  async uploadImage(file: File): Promise<ApiResponse<{ url: string }>> {
    // For now, we'll use a placeholder image service
    // In production, you would integrate with Cloudinary or similar service
    
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => {
        // Create a data URL for the image
        const dataUrl = reader.result as string;
        
        // In a real implementation, you would upload to Cloudinary here
        // For now, we'll return a placeholder URL
        resolve({
          success: true,
          data: {
            url: dataUrl // This would be the Cloudinary URL in production
          }
        });
      };
      reader.readAsDataURL(file);
    });
  }

  async testConnection(): Promise<ApiResponse<any>> {
    return this.makeRequest<any>('/properties/list', {
      method: 'POST',
      body: JSON.stringify({
        username: ADMIN_CREDENTIALS.username,
        password: ADMIN_CREDENTIALS.password
      })
    });
  }
}

export const propertyAPI = new PropertySubmissionAPI();

// Utility function to validate image files
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Please upload only JPEG, PNG, or WebP images'
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'Image size must be less than 5MB'
    };
  }

  return { valid: true };
}

// Utility function to get coordinates from address (placeholder)
export async function getCoordinatesFromAddress(_address: string): Promise<{ lat: number; lng: number } | null> {
  // In production, you would use a geocoding service like Google Maps API
  // For now, return default Addis Ababa coordinates
  return {
    lat: 9.0320 + (Math.random() - 0.5) * 0.1, // Add some randomness
    lng: 38.7469 + (Math.random() - 0.5) * 0.1
  };
}
