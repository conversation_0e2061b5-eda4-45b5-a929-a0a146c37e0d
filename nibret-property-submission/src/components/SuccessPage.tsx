import React from 'react';
import { useNavigate } from 'react-router-dom';
import { CheckCircle, Home, ArrowLeft, Mail, Phone } from 'lucide-react';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { useLanguage } from '../context/LanguageContext';
import LanguageSwitcher from './LanguageSwitcher';

const SuccessPage: React.FC = () => {
  const navigate = useNavigate();
  const { translate } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* Language Switcher */}
        <div className="flex justify-end mb-4">
          <LanguageSwitcher />
        </div>

        <Card className="shadow-xl border-0 success-animation">
          <CardHeader className="text-center bg-gradient-to-r from-green-500 to-green-600 text-white rounded-t-lg">
            <div className="flex justify-center mb-4">
              <div className="bg-white p-4 rounded-full">
                <CheckCircle className="w-12 h-12 text-green-500" />
              </div>
            </div>
            <CardTitle className="text-xl md:text-2xl font-bold">
              {translate('Property Submitted Successfully!', 'ንብረት በተሳካ ሁኔታ ተላክቷል!')}
            </CardTitle>
            <CardDescription className="text-green-100 text-sm md:text-base">
              {translate('Thank you for choosing Nibret Real Estate Platform', 'የኒብረት ሪል እስቴት መድረክን ስለመረጡ እናመሰግናለን')}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="p-8 text-center">
            <div className="space-y-6">
              <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                <h3 className="text-lg font-semibold text-green-800 mb-2">
                  {translate('What happens next?', 'ቀጥሎ ምን ይከሰታል?')}
                </h3>
                <div className="text-left space-y-3 text-green-700 text-sm md:text-base">
                  <div className="flex items-start">
                    <div className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                      1
                    </div>
                    <div>
                      <strong>{translate('Review Process:', 'የግምገማ ሂደት:')}</strong> {translate(
                        'Our team will review your property submission within 24-48 hours.',
                        'ቡድናችን የንብረት ማስገባትዎን በ24-48 ሰዓት ውስጥ ይገመግማል።'
                      )}
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                      2
                    </div>
                    <div>
                      <strong>{translate('Verification:', 'ማረጋገጥ:')}</strong> {translate(
                        'We may contact you to verify property details and schedule a photo session if needed.',
                        'የንብረት ዝርዝሮችን ለማረጋገጥ እና አስፈላጊ ከሆነ የፎቶ ክፍለ ጊዜ ለመርሐግብር ሊያገኙዎት እንችላለን።'
                      )}
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                      3
                    </div>
                    <div>
                      <strong>{translate('Go Live:', 'ቀጥታ ስርጭት:')}</strong> {translate(
                        'Once approved, your property will be published on our platform and promoted to potential buyers/renters.',
                        'ከተፈቀደ በኋላ ንብረትዎ በመድረካችን ላይ ይታተማል እና ለሊሆኑ የሚችሉ ገዢዎች/ተከራዮች ይስተዋወቃል።'
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                <h3 className="text-lg font-semibold text-blue-800 mb-3">
                  {translate('Contact Information', 'የመገናኛ መረጃ')}
                </h3>
                <div className="space-y-2 text-blue-700 text-sm md:text-base">
                  <div className="flex items-center justify-center">
                    <Mail className="w-4 h-4 mr-2" />
                    <a href="mailto:<EMAIL>" className="hover:underline">
                      <EMAIL>
                    </a>
                  </div>
                  <div className="flex items-center justify-center">
                    <Phone className="w-4 h-4 mr-2" />
                    <a href="tel:+251911234567" className="hover:underline">
                      +251 91 123 4567
                    </a>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <p className="text-yellow-800 text-sm">
                  <strong>{translate('Important:', 'አስፈላጊ:')}</strong> {translate(
                    'Please keep your contact information up to date. We\'ll reach out to you using the details you provided.',
                    'እባክዎ የመገናኛ መረጃዎን ወቅታዊ ያድርጉ። የሰጡንን ዝርዝሮች ተጠቅመን እናገኝዎታለን።'
                  )}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                <Button
                  onClick={() => navigate('/')}
                  variant="outline"
                  className="flex items-center justify-center"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  {translate('Submit Another Property', 'ሌላ ንብረት አስገባ')}
                </Button>

                <Button
                  onClick={() => window.open('https://nibret.com', '_blank')}
                  variant="nibret"
                  className="flex items-center justify-center"
                >
                  <Home className="w-4 h-4 mr-2" />
                  {translate('Visit Nibret.com', 'Nibret.com ይጎብኙ')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <div className="mt-8 text-center">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h4 className="font-semibold text-gray-800 mb-3">
              {translate('Why Choose Nibret Real Estate?', 'ለምን የኒብረት ሪል እስቴትን መምረጥ?')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
              <div className="flex flex-col items-center">
                <div className="bg-nibret-blue p-2 rounded-full mb-2">
                  <Home className="w-5 h-5 text-white" />
                </div>
                <strong>{translate('Wide Reach', 'ሰፊ ተደራሽነት')}</strong>
                <span>{translate('Thousands of active buyers and renters', 'በሺዎች የሚቆጠሩ ንቁ ገዢዎች እና ተከራዮች')}</span>
              </div>
              <div className="flex flex-col items-center">
                <div className="bg-nibret-gold p-2 rounded-full mb-2">
                  <CheckCircle className="w-5 h-5 text-white" />
                </div>
                <strong>{translate('Verified Listings', 'የተረጋገጡ ዝርዝሮች')}</strong>
                <span>{translate('All properties are verified for quality', 'ሁሉም ንብረቶች ለጥራት ተረጋግጠዋል')}</span>
              </div>
              <div className="flex flex-col items-center">
                <div className="bg-green-500 p-2 rounded-full mb-2">
                  <Phone className="w-5 h-5 text-white" />
                </div>
                <strong>{translate('24/7 Support', '24/7 ድጋፍ')}</strong>
                <span>{translate('Dedicated support throughout the process', 'በሂደቱ ሁሉ የተወሰነ ድጋፍ')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuccessPage;
