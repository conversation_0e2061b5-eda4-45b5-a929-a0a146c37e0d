import React from 'react';
import { Button } from './ui/button';
import { Languages } from 'lucide-react';
import { useLanguage } from '../context/LanguageContext';

const LanguageSwitcher: React.FC = () => {
  const { language, setLanguage, translate } = useLanguage();

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'am' : 'en');
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleLanguage}
      className="flex items-center gap-2 bg-white/90 hover:bg-white border-nibret-blue/20"
    >
      <Languages className="w-4 h-4" />
      <span className="font-medium">
        {language === 'en' ? 'አማ' : 'EN'}
      </span>
      <span className="text-xs text-gray-500">
        {translate('Switch to Amharic', 'Switch to English')}
      </span>
    </Button>
  );
};

export default LanguageSwitcher;
