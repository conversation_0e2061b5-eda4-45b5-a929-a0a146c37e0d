import React, { useCallback, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { useDropzone } from 'react-dropzone';
import { Camera, Upload, X, Image as ImageIcon, AlertCircle } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent } from '../ui/card';
import { useLanguage } from '../../context/LanguageContext';
import { validateImageFile } from '../../services/api';

interface Step6Props {
  form: UseFormReturn<any>;
}

const Step6Photos: React.FC<Step6Props> = ({ form }) => {
  const { translate } = useLanguage();
  const { watch, setValue } = form;
  const [uploadError, setUploadError] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  
  const images = watch('images') || [];

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setUploadError('');
    setIsUploading(true);

    try {
      const newImages: string[] = [];
      
      for (const file of acceptedFiles) {
        // Validate file
        const validation = validateImageFile(file);
        if (!validation.valid) {
          setUploadError(validation.error || 'Invalid file');
          continue;
        }

        // Convert to data URL for preview (in production, upload to Cloudinary)
        const reader = new FileReader();
        const dataUrl = await new Promise<string>((resolve) => {
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(file);
        });
        
        newImages.push(dataUrl);
      }

      if (newImages.length > 0) {
        setValue('images', [...images, ...newImages]);
      }
    } catch (error) {
      setUploadError(translate('Failed to upload images', 'ምስሎችን መስቀል አልተሳካም'));
    } finally {
      setIsUploading(false);
    }
  }, [images, setValue, translate]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    maxFiles: 10,
    maxSize: 5 * 1024 * 1024, // 5MB
  });

  const removeImage = (index: number) => {
    const newImages = images.filter((_: string, i: number) => i !== index);
    setValue('images', newImages);
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    setValue('images', newImages);
  };

  return (
    <div className="space-y-6">
      {/* Introduction */}
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-nibret-blue mb-2">
          {translate('Add photos of your property', 'የንብረትዎን ፎቶዎች ያክሉ')}
        </h3>
        <p className="text-gray-600 text-sm">
          {translate('High-quality photos help attract more potential buyers and renters', 'ከፍተኛ ጥራት ያላቸው ፎቶዎች ብዙ ሊሆኑ የሚችሉ ገዢዎችን እና ተከራዮችን ለመሳብ ይረዳሉ')}
        </p>
      </div>

      {/* Upload Area */}
      <Card className="border-2 border-dashed border-gray-300 hover:border-nibret-blue transition-colors">
        <CardContent className="p-8">
          <div
            {...getRootProps()}
            className={`text-center cursor-pointer transition-all ${
              isDragActive ? 'bg-nibret-blue/5' : 'hover:bg-gray-50'
            }`}
          >
            <input {...getInputProps()} />
            <div className="space-y-4">
              <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center ${
                isDragActive ? 'bg-nibret-blue text-white' : 'bg-gray-100 text-gray-600'
              }`}>
                {isUploading ? (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-nibret-blue"></div>
                ) : (
                  <Upload className="w-8 h-8" />
                )}
              </div>
              
              <div>
                <h4 className="text-lg font-medium text-gray-800 mb-2">
                  {isDragActive
                    ? translate('Drop images here', 'ምስሎችን እዚህ ይጣሉ')
                    : translate('Upload Property Photos', 'የንብረት ፎቶዎችን ይስቀሉ')
                  }
                </h4>
                <p className="text-sm text-gray-600 mb-4">
                  {translate(
                    'Drag and drop images here, or click to select files',
                    'ምስሎችን እዚህ ይጎትቱ እና ይጣሉ፣ ወይም ፋይሎችን ለመምረጥ ይጫኑ'
                  )}
                </p>
                <Button type="button" variant="outline" className="mx-auto">
                  <Camera className="w-4 h-4 mr-2" />
                  {translate('Choose Photos', 'ፎቶዎችን ይምረጡ')}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upload Guidelines */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h4 className="font-medium text-blue-800 mb-2">
          {translate('📸 Photo Guidelines', '📸 የፎቶ መመሪያዎች')}
        </h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• {translate('Upload up to 10 high-quality photos', 'እስከ 10 ከፍተኛ ጥራት ያላቸው ፎቶዎችን ይስቀሉ')}</li>
          <li>• {translate('Supported formats: JPEG, PNG, WebP', 'የሚደገፉ ቅርጸቶች: JPEG, PNG, WebP')}</li>
          <li>• {translate('Maximum file size: 5MB per image', 'ከፍተኛ ፋይል መጠን: በአንድ ምስል 5MB')}</li>
          <li>• {translate('Include exterior, interior, and key feature photos', 'የውጭ፣ የውስጥ እና ቁልፍ ባህሪያት ፎቶዎችን ያካትቱ')}</li>
          <li>• {translate('First photo will be used as the main listing image', 'የመጀመሪያው ፎቶ እንደ ዋና የዝርዝር ምስል ይጠቅማል')}</li>
        </ul>
      </div>

      {/* Error Display */}
      {uploadError && (
        <div className="bg-red-50 p-4 rounded-lg border border-red-200 flex items-center">
          <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
          <span className="text-red-700 text-sm">{uploadError}</span>
        </div>
      )}

      {/* Image Preview Grid */}
      {images.length > 0 && (
        <div>
          <h4 className="font-medium text-gray-800 mb-4 flex items-center">
            <ImageIcon className="w-4 h-4 mr-2" />
            {translate('Uploaded Photos', 'የተስቀሉ ፎቶዎች')} ({images.length})
          </h4>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image: string, index: number) => (
              <div key={index} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 border-2 border-gray-200">
                  <img
                    src={image}
                    alt={`Property photo ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Image Controls */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all rounded-lg flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2">
                    {index > 0 && (
                      <Button
                        type="button"
                        size="sm"
                        variant="secondary"
                        onClick={() => moveImage(index, index - 1)}
                        className="text-xs"
                      >
                        ←
                      </Button>
                    )}
                    {index < images.length - 1 && (
                      <Button
                        type="button"
                        size="sm"
                        variant="secondary"
                        onClick={() => moveImage(index, index + 1)}
                        className="text-xs"
                      >
                        →
                      </Button>
                    )}
                  </div>
                </div>

                {/* Remove Button */}
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>

                {/* Main Image Indicator */}
                {index === 0 && (
                  <div className="absolute bottom-2 left-2 bg-nibret-blue text-white text-xs px-2 py-1 rounded">
                    {translate('Main', 'ዋና')}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Reorder Instructions */}
          <p className="text-xs text-gray-500 mt-3 text-center">
            {translate(
              'Hover over photos to reorder them. First photo will be the main listing image.',
              'ፎቶዎችን ለመደርደር ላይ ይንሳፈፉ። የመጀመሪያው ፎቶ ዋናው የዝርዝር ምስል ይሆናል።'
            )}
          </p>
        </div>
      )}

      {/* Photo Tips */}
      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
        <h4 className="font-medium text-green-800 mb-2">
          {translate('💡 Photography Tips', '💡 የፎቶግራፊ ምክሮች')}
        </h4>
        <ul className="text-sm text-green-700 space-y-1">
          <li>• {translate('Take photos during daytime for better lighting', 'ለተሻለ ብርሃን በቀን ጊዜ ፎቶዎችን ይንሱ')}</li>
          <li>• {translate('Clean and declutter rooms before photographing', 'ከመንሳት በፊት ክፍሎችን ያጽዱ እና ያስተካክሉ')}</li>
          <li>• {translate('Show different angles and rooms of the property', 'የንብረቱን የተለያዩ ማዕዘኖች እና ክፍሎች ያሳዩ')}</li>
          <li>• {translate('Include photos of special features and amenities', 'የልዩ ባህሪያት እና አገልግሎቶች ፎቶዎችን ያካትቱ')}</li>
        </ul>
      </div>

      {/* Mobile Helper */}
      <div className="block md:hidden text-center">
        <p className="text-xs text-gray-500">
          {translate('Tap the upload area to select photos from your device', 'ከመሳሪያዎ ፎቶዎችን ለመምረጥ የስቀላ አካባቢውን ይንኩ')}
        </p>
      </div>
    </div>
  );
};

export default Step6Photos;
