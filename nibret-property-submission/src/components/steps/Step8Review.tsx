import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  Edit,
  MapPin,
  Home,
  User,
  Camera,
  CheckCircle,
  AlertTriangle,
  FileText
} from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Textarea } from '../ui/textarea';
import { useLanguage } from '../../context/LanguageContext';
import { formatPrice } from '../../lib/utils';

interface Step8Props {
  form: UseFormReturn<any>;
  onEditStep: (step: number) => void;
}

const Step8Review: React.FC<Step8Props> = ({ form, onEditStep }) => {
  const { translate } = useLanguage();
  const { watch, register } = form;
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  
  const formData = watch();
  const {
    propertyType,
    status,
    listing_type,
    title,
    description,
    price,
    currency,
    beds,
    baths,
    sqft,
    yearBuilt,
    lotSize,
    address,
    city,
    region,
    features,
    images,
    contact_info
  } = formData;

  const propertyTypeTranslations: { [key: string]: { en: string; am: string } } = {
    'house': { en: 'House', am: 'ቤት' },
    'apartment': { en: 'Apartment', am: 'አፓርትመንት' },
    'condo': { en: 'Condominium', am: 'ኮንዶሚኒየም' },
    'villa': { en: 'Villa', am: 'ቪላ' },
    'townhouse': { en: 'Townhouse', am: 'ታውን ሃውስ' },
    'studio': { en: 'Studio', am: 'ስቱዲዮ' },
    'other': { en: 'Other', am: 'ሌላ' }
  };

  const statusTranslations: { [key: string]: { en: string; am: string } } = {
    'for_sale': { en: 'For Sale', am: 'ለሽያጭ' },
    'for_rent': { en: 'For Rent', am: 'ለኪራይ' }
  };

  const listingTypeTranslations: { [key: string]: { en: string; am: string } } = {
    'sale': { en: 'Sale Only', am: 'ሽያጭ ብቻ' },
    'rent': { en: 'Rent Only', am: 'ኪራይ ብቻ' },
    'both': { en: 'Sale or Rent', am: 'ሽያጭ ወይም ኪራይ' }
  };

  const reviewSections = [
    {
      title: translate('Property Type & Purpose', 'የንብረት አይነት እና ዓላማ'),
      step: 1,
      icon: <Home className="w-5 h-5" />,
      content: (
        <div className="space-y-2">
          <p><strong>{translate('Type:', 'አይነት:')}</strong> {translate(
            propertyTypeTranslations[propertyType]?.en || propertyType,
            propertyTypeTranslations[propertyType]?.am || propertyType
          )}</p>
          <p><strong>{translate('Status:', 'ሁኔታ:')}</strong> {translate(
            statusTranslations[status]?.en || status,
            statusTranslations[status]?.am || status
          )}</p>
          <p><strong>{translate('Listing Type:', 'የዝርዝር አይነት:')}</strong> {translate(
            listingTypeTranslations[listing_type]?.en || listing_type,
            listingTypeTranslations[listing_type]?.am || listing_type
          )}</p>
        </div>
      )
    },
    {
      title: translate('Basic Information', 'መሰረታዊ መረጃ'),
      step: 2,
      icon: <FileText className="w-5 h-5" />,
      content: (
        <div className="space-y-2">
          <p><strong>{translate('Title:', 'ርዕስ:')}</strong> {title}</p>
          <p><strong>{translate('Description:', 'መግለጫ:')}</strong> {description?.substring(0, 100)}{description?.length > 100 ? '...' : ''}</p>
          <p><strong>{translate('Price:', 'ዋጋ:')}</strong> {formatPrice(price, currency)}</p>
        </div>
      )
    },
    {
      title: translate('Property Details', 'የንብረት ዝርዝሮች'),
      step: 3,
      icon: <Home className="w-5 h-5" />,
      content: (
        <div className="space-y-2">
          <p><strong>{translate('Bedrooms:', 'መኝታ ክፍሎች:')}</strong> {beds}</p>
          <p><strong>{translate('Bathrooms:', 'መታጠቢያ ክፍሎች:')}</strong> {baths}</p>
          <p><strong>{translate('Square Footage:', 'ስኩዌር ጫማ:')}</strong> {sqft} sqft</p>
          {yearBuilt && <p><strong>{translate('Year Built:', 'የተገነባበት ዓመት:')}</strong> {yearBuilt}</p>}
          {lotSize && <p><strong>{translate('Lot Size:', 'የመሬት መጠን:')}</strong> {lotSize} sqft</p>}
        </div>
      )
    },
    {
      title: translate('Location', 'አካባቢ'),
      step: 4,
      icon: <MapPin className="w-5 h-5" />,
      content: (
        <div className="space-y-2">
          <p><strong>{translate('Address:', 'አድራሻ:')}</strong> {address}</p>
          <p><strong>{translate('City:', 'ከተማ:')}</strong> {city}</p>
          <p><strong>{translate('Region:', 'ክልል:')}</strong> {region}</p>
        </div>
      )
    },
    {
      title: translate('Features & Amenities', 'ባህሪያት እና አገልግሎቶች'),
      step: 5,
      icon: <CheckCircle className="w-5 h-5" />,
      content: (
        <div>
          {features && features.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {features.map((feature: string, index: number) => (
                <span key={index} className="bg-gray-100 px-2 py-1 rounded text-sm">
                  {feature}
                </span>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 italic">
              {translate('No features selected', 'ምንም ባህሪያት አልተመረጡም')}
            </p>
          )}
        </div>
      )
    },
    {
      title: translate('Photos', 'ፎቶዎች'),
      step: 6,
      icon: <Camera className="w-5 h-5" />,
      content: (
        <div>
          {images && images.length > 0 ? (
            <div>
              <p className="mb-3">
                <strong>{images.length}</strong> {translate('photos uploaded', 'ፎቶዎች ተስቅለዋል')}
              </p>
              <div className="grid grid-cols-4 gap-2">
                {images.slice(0, 4).map((image: string, index: number) => (
                  <div key={index} className="aspect-square rounded overflow-hidden bg-gray-100">
                    <img src={image} alt={`Preview ${index + 1}`} className="w-full h-full object-cover" />
                  </div>
                ))}
                {images.length > 4 && (
                  <div className="aspect-square rounded bg-gray-100 flex items-center justify-center text-sm text-gray-600">
                    +{images.length - 4} {translate('more', 'ተጨማሪ')}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <p className="text-gray-500 italic">
              {translate('No photos uploaded', 'ምንም ፎቶዎች አልተስቀሉም')}
            </p>
          )}
        </div>
      )
    },
    {
      title: translate('Contact Information', 'የመገናኛ መረጃ'),
      step: 7,
      icon: <User className="w-5 h-5" />,
      content: (
        <div className="space-y-2">
          <p><strong>{translate('Name:', 'ስም:')}</strong> {contact_info?.name}</p>
          <p><strong>{translate('Phone:', 'ስልክ:')}</strong> {contact_info?.phone}</p>
          <p><strong>{translate('Email:', 'ኢሜይል:')}</strong> {contact_info?.email}</p>
          {contact_info?.agent_name && (
            <p><strong>{translate('Agent:', 'ወኪል:')}</strong> {contact_info.agent_name}</p>
          )}
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Introduction */}
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-nibret-blue mb-2">
          {translate('Review Your Property Listing', 'የንብረት ዝርዝርዎን ይገምግሙ')}
        </h3>
        <p className="text-gray-600 text-sm">
          {translate('Please review all information before submitting your property listing', 'የንብረት ዝርዝርዎን ከማስገባትዎ በፊት ሁሉንም መረጃ እባክዎ ይገምግሙ')}
        </p>
      </div>

      {/* Review Sections */}
      <div className="space-y-4">
        {reviewSections.map((section, index) => (
          <Card key={index} className="border border-gray-200">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-base">
                <div className="flex items-center">
                  <div className="text-nibret-blue mr-3">
                    {section.icon}
                  </div>
                  {section.title}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => onEditStep(section.step)}
                  className="flex items-center text-xs"
                >
                  <Edit className="w-3 h-3 mr-1" />
                  {translate('Edit', 'አርም')}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              {section.content}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Additional Notes */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {translate('Additional Notes (Optional)', 'ተጨማሪ ማስታወሻዎች (አማራጭ)')}
        </label>
        <Textarea
          {...register('additional_notes')}
          placeholder={translate(
            'Any additional information you would like to include...',
            'ማካተት የሚፈልጉት ማንኛውም ተጨማሪ መረጃ...'
          )}
          className="w-full min-h-[80px]"
          rows={3}
        />
        <p className="text-xs text-gray-500 mt-1">
          {translate('Special instructions, availability, or other important details', 'ልዩ መመሪያዎች፣ ተገኝነት፣ ወይም ሌሎች አስፈላጊ ዝርዝሮች')}
        </p>
      </div>

      {/* Terms and Conditions */}
      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <div className="flex items-start">
          <input
            type="checkbox"
            id="terms"
            checked={agreedToTerms}
            onChange={(e) => setAgreedToTerms(e.target.checked)}
            className="mt-1 mr-3"
          />
          <label htmlFor="terms" className="text-sm text-yellow-800">
            <strong>{translate('Terms and Conditions:', 'ውሎች እና ሁኔታዎች:')}</strong>
            <br />
            {translate(
              'I confirm that all information provided is accurate and I agree to Nibret\'s terms of service. I understand that false information may result in listing removal.',
              'የቀረበው ሁሉም መረጃ ትክክል መሆኑን አረጋግጣለሁ እና የኒብረትን የአገልግሎት ውሎች እቀበላለሁ። የተሳሳተ መረጃ ዝርዝሩ እንዲወገድ ሊያደርግ እንደሚችል ተረድቻለሁ።'
            )}
          </label>
        </div>
      </div>

      {/* Submission Warning */}
      {!agreedToTerms && (
        <div className="bg-red-50 p-4 rounded-lg border border-red-200 flex items-center">
          <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
          <span className="text-red-700 text-sm">
            {translate('Please agree to the terms and conditions to submit your listing', 'ዝርዝርዎን ለማስገባት እባክዎ ውሎች እና ሁኔታዎችን ይቀበሉ')}
          </span>
        </div>
      )}

      {/* Submission Info */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h4 className="font-medium text-blue-800 mb-2">
          {translate('What happens after submission?', 'ከማስገባት በኋላ ምን ይከሰታል?')}
        </h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• {translate('Your listing will be reviewed within 24-48 hours', 'ዝርዝርዎ በ24-48 ሰዓት ውስጥ ይገመገማል')}</li>
          <li>• {translate('We may contact you for verification or additional information', 'ለማረጋገጥ ወይም ተጨማሪ መረጃ ሊያገኙዎት እንችላለን')}</li>
          <li>• {translate('Once approved, your property will be published on Nibret', 'ከተፈቀደ በኋላ ንብረትዎ በኒብረት ላይ ይታተማል')}</li>
          <li>• {translate('You will receive email notifications about inquiries', 'ስለ ጥያቄዎች የኢሜይል ማሳወቂያዎችን ይቀበላሉ')}</li>
        </ul>
      </div>

      {/* Mobile Helper */}
      <div className="block md:hidden text-center">
        <p className="text-xs text-gray-500">
          {translate('Review all sections and agree to terms before submitting', 'ከማስገባትዎ በፊት ሁሉንም ክፍሎች ይገምግሙ እና ውሎችን ይቀበሉ')}
        </p>
      </div>
    </div>
  );
};

export default Step8Review;
