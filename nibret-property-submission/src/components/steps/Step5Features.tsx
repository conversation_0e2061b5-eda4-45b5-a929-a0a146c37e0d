import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Plus, X, CheckCircle, Star } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent } from '../ui/card';
import { useLanguage } from '../../context/LanguageContext';
// import { COMMON_FEATURES } from '../../types/property';

interface Step5Props {
  form: UseFormReturn<any>;
}

const Step5Features: React.FC<Step5Props> = ({ form }) => {
  const { translate } = useLanguage();
  const { watch, setValue } = form;
  const [customFeature, setCustomFeature] = useState('');
  
  const selectedFeatures = watch('features') || [];

  const featureTranslations: { [key: string]: { en: string; am: string } } = {
    'Parking': { en: 'Parking', am: 'የመኪና ማቆሚያ' },
    'Garden': { en: 'Garden', am: 'የአትክልት ስፍራ' },
    'Swimming Pool': { en: 'Swimming Pool', am: 'የመዋኛ ገንዳ' },
    'Security System': { en: 'Security System', am: 'የደህንነት ስርዓት' },
    'Air Conditioning': { en: 'Air Conditioning', am: 'የአየር ማቀዝቀዣ' },
    'Heating': { en: 'Heating', am: 'የማሞቂያ ስርዓት' },
    'Balcony': { en: 'Balcony', am: 'ባልኮኒ' },
    'Terrace': { en: 'Terrace', am: 'ቴራስ' },
    'Elevator': { en: 'Elevator', am: 'ሊፍት' },
    'Furnished': { en: 'Furnished', am: 'የቤት እቃ የተሞላ' },
    'Internet/WiFi': { en: 'Internet/WiFi', am: 'ኢንተርኔት/ዋይፋይ' },
    'Gym/Fitness Center': { en: 'Gym/Fitness Center', am: 'ጂም/የአካል ብቃት ማእከል' },
    'Laundry Room': { en: 'Laundry Room', am: 'የልብስ ማጠቢያ ክፍል' },
    'Storage Room': { en: 'Storage Room', am: 'የማከማቻ ክፍል' },
    'Fireplace': { en: 'Fireplace', am: 'የእሳት ምድጃ' },
    'City View': { en: 'City View', am: 'የከተማ እይታ' },
    'Mountain View': { en: 'Mountain View', am: 'የተራራ እይታ' },
    'Quiet Neighborhood': { en: 'Quiet Neighborhood', am: 'ጸጥ ያለ አካባቢ' },
    'Near Schools': { en: 'Near Schools', am: 'ትምህርት ቤቶች አጠገብ' },
    'Near Shopping': { en: 'Near Shopping', am: 'የግዢ ማእከሎች አጠገብ' },
    'Public Transport Access': { en: 'Public Transport Access', am: 'የህዝብ ማመላለሻ ተደራሽነት' }
  };

  const toggleFeature = (feature: string) => {
    const currentFeatures = selectedFeatures || [];
    const isSelected = currentFeatures.includes(feature);
    
    if (isSelected) {
      setValue('features', currentFeatures.filter((f: string) => f !== feature));
    } else {
      setValue('features', [...currentFeatures, feature]);
    }
  };

  const addCustomFeature = () => {
    if (customFeature.trim() && !selectedFeatures.includes(customFeature.trim())) {
      setValue('features', [...selectedFeatures, customFeature.trim()]);
      setCustomFeature('');
    }
  };

  const removeFeature = (feature: string) => {
    setValue('features', selectedFeatures.filter((f: string) => f !== feature));
  };

  const featureCategories = [
    {
      title: translate('Security & Safety', 'ደህንነት እና ጥበቃ'),
      features: ['Security System', 'Parking', 'Quiet Neighborhood']
    },
    {
      title: translate('Comfort & Convenience', 'ምቾት እና ማመቻቸት'),
      features: ['Air Conditioning', 'Heating', 'Elevator', 'Furnished', 'Internet/WiFi']
    },
    {
      title: translate('Outdoor & Recreation', 'የውጭ እና መዝናኛ'),
      features: ['Garden', 'Swimming Pool', 'Balcony', 'Terrace', 'Gym/Fitness Center']
    },
    {
      title: translate('Storage & Utility', 'ማከማቻ እና አገልግሎት'),
      features: ['Laundry Room', 'Storage Room', 'Fireplace']
    },
    {
      title: translate('Views & Location', 'እይታዎች እና አካባቢ'),
      features: ['City View', 'Mountain View', 'Near Schools', 'Near Shopping', 'Public Transport Access']
    }
  ];

  return (
    <div className="space-y-6">
      {/* Introduction */}
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-nibret-blue mb-2">
          {translate('What makes your property special?', 'ንብረትዎን ልዩ የሚያደርገው ምንድን ነው?')}
        </h3>
        <p className="text-gray-600 text-sm">
          {translate('Select all features and amenities that apply to your property', 'ለንብረትዎ የሚተገበሩ ሁሉንም ባህሪያት እና አገልግሎቶች ይምረጡ')}
        </p>
      </div>

      {/* Feature Categories */}
      {featureCategories.map((category, categoryIndex) => (
        <div key={categoryIndex} className="space-y-3">
          <h4 className="font-medium text-gray-800 flex items-center">
            <Star className="w-4 h-4 mr-2 text-nibret-gold" />
            {category.title}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {category.features.map((feature) => {
              const isSelected = selectedFeatures.includes(feature);
              return (
                <Card
                  key={feature}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    isSelected
                      ? 'ring-2 ring-nibret-blue bg-nibret-blue/5 border-nibret-blue'
                      : 'hover:bg-gray-50 border-gray-200'
                  }`}
                  onClick={() => toggleFeature(feature)}
                >
                  <CardContent className="p-3 flex items-center justify-between">
                    <span className={`text-sm font-medium ${
                      isSelected ? 'text-nibret-blue' : 'text-gray-700'
                    }`}>
                      {translate(
                        featureTranslations[feature]?.en || feature,
                        featureTranslations[feature]?.am || feature
                      )}
                    </span>
                    {isSelected && (
                      <CheckCircle className="w-4 h-4 text-nibret-blue" />
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      ))}

      {/* Custom Feature Input */}
      <div className="border-t pt-6">
        <h4 className="font-medium text-gray-800 mb-3">
          {translate('Add Custom Feature', 'ብጁ ባህሪ ያክሉ')}
        </h4>
        <div className="flex gap-3">
          <Input
            value={customFeature}
            onChange={(e) => setCustomFeature(e.target.value)}
            placeholder={translate('e.g., Rooftop access, Private entrance', 'ምሳሌ፣ የጣሪያ ተደራሽነት፣ የግል መግቢያ')}
            className="flex-1"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addCustomFeature();
              }
            }}
          />
          <Button
            type="button"
            onClick={addCustomFeature}
            disabled={!customFeature.trim()}
            variant="outline"
            className="flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            {translate('Add', 'ጨምር')}
          </Button>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          {translate('Add any unique features not listed above', 'ከላይ ያልተዘረዘሩ ልዩ ባህሪያትን ያክሉ')}
        </p>
      </div>

      {/* Selected Features Summary */}
      {selectedFeatures.length > 0 && (
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <h4 className="font-medium text-green-800 mb-3 flex items-center">
            <CheckCircle className="w-4 h-4 mr-2" />
            {translate('Selected Features', 'የተመረጡ ባህሪያት')} ({selectedFeatures.length})
          </h4>
          <div className="flex flex-wrap gap-2">
            {selectedFeatures.map((feature: string, index: number) => (
              <div
                key={index}
                className="bg-white px-3 py-1 rounded-full border border-green-300 flex items-center text-sm"
              >
                <span className="text-green-700">
                  {translate(
                    featureTranslations[feature]?.en || feature,
                    featureTranslations[feature]?.am || feature
                  )}
                </span>
                <button
                  type="button"
                  onClick={() => removeFeature(feature)}
                  className="ml-2 text-green-600 hover:text-green-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Feature Tips */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h4 className="font-medium text-blue-800 mb-2">
          {translate('💡 Feature Tips', '💡 የባህሪ ምክሮች')}
        </h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• {translate('More features can attract more potential buyers/renters', 'ብዙ ባህሪያት ብዙ ሊሆኑ የሚችሉ ገዢዎችን/ተከራዮችን ሊስቡ ይችላሉ')}</li>
          <li>• {translate('Be honest about features - accuracy builds trust', 'ስለ ባህሪያት ታማኝ ይሁኑ - ትክክለኛነት እምነት ይገነባል')}</li>
          <li>• {translate('Highlight unique features that set your property apart', 'ንብረትዎን የሚለዩ ልዩ ባህሪያትን አጉልተው ያሳዩ')}</li>
          <li>• {translate('You can always update features after listing', 'ከዘረዘሩ በኋላ ሁልጊዜ ባህሪያትን ማዘመን ይችላሉ')}</li>
        </ul>
      </div>

      {/* Mobile Helper */}
      <div className="block md:hidden text-center">
        <p className="text-xs text-gray-500">
          {translate('Tap features to select/deselect them', 'ባህሪያትን ለመምረጥ/ለመሰረዝ ይንኩ')}
        </p>
      </div>
    </div>
  );
};

export default Step5Features;
