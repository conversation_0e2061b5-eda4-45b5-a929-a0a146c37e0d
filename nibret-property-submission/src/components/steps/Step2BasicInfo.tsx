import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { DollarSign, FileText } from 'lucide-react';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { useLanguage } from '../../context/LanguageContext';
import { formatPrice } from '../../lib/utils';

interface Step2Props {
  form: UseFormReturn<any>;
}

const Step2BasicInfo: React.FC<Step2Props> = ({ form }) => {
  const { translate } = useLanguage();
  const { register, watch, setValue, formState: { errors } } = form;
  
  const currency = watch('currency');
  const price = watch('price');

  return (
    <div className="space-y-6">
      {/* Property Title */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <FileText className="w-4 h-4 inline mr-2" />
          {translate('Property Title', 'የንብረት ርዕስ')} *
        </label>
        <Input
          {...register('title')}
          placeholder={translate(
            'e.g., Beautiful 3-bedroom apartment in Bole',
            'ምሳሌ፣ በቦሌ ውስጥ ያለ ቆንጆ 3 መኝታ ክፍል አፓርትመንት'
          )}
          className="w-full"
        />
        {errors.title && (
          <p className="text-red-500 text-sm mt-1">
            {translate('Title must be at least 5 characters', 'ርዕሱ ቢያንስ 5 ቁምፊዎች መሆን አለበት')}
          </p>
        )}
      </div>

      {/* Property Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {translate('Property Description', 'የንብረት መግለጫ')} *
        </label>
        <Textarea
          {...register('description')}
          placeholder={translate(
            'Describe your property in detail. Include features, amenities, and what makes it special...',
            'ንብረትዎን በዝርዝር ይግለጹ። ባህሪያት፣ አገልግሎቶች እና ልዩ የሚያደርገውን ያካትቱ...'
          )}
          className="w-full min-h-[120px] resize-none"
          rows={5}
        />
        {errors.description && (
          <p className="text-red-500 text-sm mt-1">
            {translate('Description must be at least 10 characters', 'መግለጫው ቢያንስ 10 ቁምፊዎች መሆን አለበት')}
          </p>
        )}
      </div>

      {/* Price and Currency */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <DollarSign className="w-4 h-4 inline mr-2" />
          {translate('Price', 'ዋጋ')} *
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {/* Price Input */}
          <div className="md:col-span-2">
            <Input
              type="number"
              {...register('price', { valueAsNumber: true })}
              placeholder={translate('Enter price', 'ዋጋ ያስገቡ')}
              className="w-full"
              min="1"
              step="1000"
            />
            {errors.price && (
              <p className="text-red-500 text-sm mt-1">
                {translate('Price must be greater than 0', 'ዋጋው ከ0 በላይ መሆን አለበት')}
              </p>
            )}
          </div>

          {/* Currency Selection */}
          <div>
            <Select
              value={currency}
              onValueChange={(value) => setValue('currency', value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={translate('Currency', 'ምንዛሬ')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ETB">
                  <div className="flex items-center">
                    <span className="mr-2">🇪🇹</span>
                    ETB - {translate('Ethiopian Birr', 'የኢትዮጵያ ብር')}
                  </div>
                </SelectItem>
                <SelectItem value="USD">
                  <div className="flex items-center">
                    <span className="mr-2">🇺🇸</span>
                    USD - {translate('US Dollar', 'የአሜሪካ ዶላር')}
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Price Preview */}
        {price && price > 0 && currency && (
          <div className="mt-3 p-3 bg-gray-50 rounded-lg border">
            <p className="text-sm text-gray-600 mb-1">
              {translate('Price Preview:', 'የዋጋ ቅድመ እይታ:')}
            </p>
            <p className="text-lg font-semibold text-nibret-blue">
              {formatPrice(price, currency as 'ETB' | 'USD')}
            </p>
          </div>
        )}
      </div>

      {/* Price Guidelines */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h4 className="font-medium text-blue-800 mb-2">
          {translate('💡 Pricing Tips', '💡 የዋጋ አሰጣጥ ምክሮች')}
        </h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• {translate('Research similar properties in your area', 'በአካባቢዎ ያሉ ተመሳሳይ ንብረቶችን ይመርምሩ')}</li>
          <li>• {translate('Consider property condition and amenities', 'የንብረቱን ሁኔታ እና አገልግሎቶች ግምት ውስጥ ያስገቡ')}</li>
          <li>• {translate('Price competitively to attract buyers/renters', 'ገዢዎችን/ተከራዮችን ለመሳብ ተወዳዳሪ ዋጋ ያስቀምጡ')}</li>
          <li>• {translate('You can always adjust the price later', 'ዋጋውን በኋላ ሁልጊዜ ማስተካከል ይችላሉ')}</li>
        </ul>
      </div>

      {/* Responsive Layout Helper */}
      <div className="block md:hidden">
        <div className="text-xs text-gray-500 text-center">
          {translate('Swipe or scroll to see all fields', 'ሁሉንም መስኮች ለማየት ይጠቅሱ ወይም ይሸብሸቡ')}
        </div>
      </div>
    </div>
  );
};

export default Step2BasicInfo;
