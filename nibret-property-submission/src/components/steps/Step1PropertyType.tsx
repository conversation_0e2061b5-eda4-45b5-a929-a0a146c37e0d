import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Building, Home, Castle, Warehouse } from 'lucide-react';
import { Card, CardContent } from '../ui/card';
// import { Button } from '../ui/button';
import { useLanguage } from '../../context/LanguageContext';
import { PROPERTY_TYPES } from '../../types/property';

interface Step1Props {
  form: UseFormReturn<any>;
}

const Step1PropertyType: React.FC<Step1Props> = ({ form }) => {
  const { translate } = useLanguage();
  const { watch, setValue } = form;
  
  const propertyType = watch('propertyType');
  const status = watch('status');
  const listingType = watch('listing_type');

  const getPropertyIcon = (type: string) => {
    switch (type) {
      case 'house': return <Home className="w-6 h-6" />;
      case 'apartment': return <Building className="w-6 h-6" />;
      case 'villa': return <Castle className="w-6 h-6" />;
      case 'condo': return <Building className="w-6 h-6" />;
      case 'townhouse': return <Home className="w-6 h-6" />;
      case 'studio': return <Warehouse className="w-6 h-6" />;
      default: return <Building className="w-6 h-6" />;
    }
  };

  const propertyTypeTranslations = {
    house: { en: 'House', am: 'ቤት' },
    apartment: { en: 'Apartment', am: 'አፓርትመንት' },
    condo: { en: 'Condominium', am: 'ኮንዶሚኒየም' },
    villa: { en: 'Villa', am: 'ቪላ' },
    townhouse: { en: 'Townhouse', am: 'ታውን ሃውስ' },
    studio: { en: 'Studio', am: 'ስቱዲዮ' },
    other: { en: 'Other', am: 'ሌላ' }
  };

  return (
    <div className="space-y-8">
      {/* Property Type Selection */}
      <div>
        <h3 className="text-lg font-semibold text-nibret-blue mb-4">
          {translate('What type of property are you listing?', 'ምን አይነት ንብረት ነው የሚያስተዋውቁት?')}
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {PROPERTY_TYPES.map((type) => (
            <Card
              key={type.value}
              className={`cursor-pointer transition-all hover:shadow-md ${
                propertyType === type.value
                  ? 'ring-2 ring-nibret-blue bg-nibret-blue/5'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setValue('propertyType', type.value)}
            >
              <CardContent className="p-4 text-center">
                <div className={`mb-2 flex justify-center ${
                  propertyType === type.value ? 'text-nibret-blue' : 'text-gray-600'
                }`}>
                  {getPropertyIcon(type.value)}
                </div>
                <p className={`text-sm font-medium ${
                  propertyType === type.value ? 'text-nibret-blue' : 'text-gray-700'
                }`}>
                  {translate(
                    propertyTypeTranslations[type.value as keyof typeof propertyTypeTranslations]?.en || type.label,
                    propertyTypeTranslations[type.value as keyof typeof propertyTypeTranslations]?.am || type.label
                  )}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Property Status */}
      <div>
        <h3 className="text-lg font-semibold text-nibret-blue mb-4">
          {translate('What do you want to do with this property?', 'በዚህ ንብረት ምን ማድረግ ይፈልጋሉ?')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card
            className={`cursor-pointer transition-all hover:shadow-md ${
              status === 'for_sale'
                ? 'ring-2 ring-nibret-blue bg-nibret-blue/5'
                : 'hover:bg-gray-50'
            }`}
            onClick={() => {
              setValue('status', 'for_sale');
              setValue('listing_type', 'sale');
            }}
          >
            <CardContent className="p-6 text-center">
              <div className={`mb-3 ${
                status === 'for_sale' ? 'text-nibret-blue' : 'text-gray-600'
              }`}>
                <Building className="w-8 h-8 mx-auto" />
              </div>
              <h4 className={`font-semibold mb-2 ${
                status === 'for_sale' ? 'text-nibret-blue' : 'text-gray-700'
              }`}>
                {translate('Sell Property', 'ንብረት ሽጥ')}
              </h4>
              <p className="text-sm text-gray-600">
                {translate('List your property for sale', 'ንብረትዎን ለሽያጭ ያስተዋውቁ')}
              </p>
            </CardContent>
          </Card>

          <Card
            className={`cursor-pointer transition-all hover:shadow-md ${
              status === 'for_rent'
                ? 'ring-2 ring-nibret-blue bg-nibret-blue/5'
                : 'hover:bg-gray-50'
            }`}
            onClick={() => {
              setValue('status', 'for_rent');
              setValue('listing_type', 'rent');
            }}
          >
            <CardContent className="p-6 text-center">
              <div className={`mb-3 ${
                status === 'for_rent' ? 'text-nibret-blue' : 'text-gray-600'
              }`}>
                <Home className="w-8 h-8 mx-auto" />
              </div>
              <h4 className={`font-semibold mb-2 ${
                status === 'for_rent' ? 'text-nibret-blue' : 'text-gray-700'
              }`}>
                {translate('Rent Property', 'ንብረት አከራይ')}
              </h4>
              <p className="text-sm text-gray-600">
                {translate('List your property for rent', 'ንብረትዎን ለኪራይ ያስተዋውቁ')}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Listing Type Option */}
      {(status === 'for_sale' || status === 'for_rent') && (
        <div>
          <h3 className="text-lg font-semibold text-nibret-blue mb-4">
            {translate('Listing Options', 'የዝርዝር አማራጮች')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card
              className={`cursor-pointer transition-all hover:shadow-md ${
                listingType === 'sale'
                  ? 'ring-2 ring-nibret-blue bg-nibret-blue/5'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setValue('listing_type', 'sale')}
            >
              <CardContent className="p-4 text-center">
                <h4 className={`font-medium mb-1 ${
                  listingType === 'sale' ? 'text-nibret-blue' : 'text-gray-700'
                }`}>
                  {translate('Sale Only', 'ሽያጭ ብቻ')}
                </h4>
                <p className="text-xs text-gray-600">
                  {translate('For sale only', 'ለሽያጭ ብቻ')}
                </p>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all hover:shadow-md ${
                listingType === 'rent'
                  ? 'ring-2 ring-nibret-blue bg-nibret-blue/5'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setValue('listing_type', 'rent')}
            >
              <CardContent className="p-4 text-center">
                <h4 className={`font-medium mb-1 ${
                  listingType === 'rent' ? 'text-nibret-blue' : 'text-gray-700'
                }`}>
                  {translate('Rent Only', 'ኪራይ ብቻ')}
                </h4>
                <p className="text-xs text-gray-600">
                  {translate('For rent only', 'ለኪራይ ብቻ')}
                </p>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all hover:shadow-md ${
                listingType === 'both'
                  ? 'ring-2 ring-nibret-blue bg-nibret-blue/5'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setValue('listing_type', 'both')}
            >
              <CardContent className="p-4 text-center">
                <h4 className={`font-medium mb-1 ${
                  listingType === 'both' ? 'text-nibret-blue' : 'text-gray-700'
                }`}>
                  {translate('Both', 'ሁለቱም')}
                </h4>
                <p className="text-xs text-gray-600">
                  {translate('Sale or rent', 'ሽያጭ ወይም ኪራይ')}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Validation Messages */}
      {form.formState.errors.propertyType && (
        <p className="text-red-500 text-sm">
          {translate('Please select a property type', 'እባክዎ የንብረት አይነት ይምረጡ')}
        </p>
      )}
      {form.formState.errors.status && (
        <p className="text-red-500 text-sm">
          {translate('Please select what you want to do with the property', 'እባክዎ በንብረቱ ምን ማድረግ እንደሚፈልጉ ይምረጡ')}
        </p>
      )}
    </div>
  );
};

export default Step1PropertyType;
