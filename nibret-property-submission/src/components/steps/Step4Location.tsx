import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { MapPin, Navigation } from 'lucide-react';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { useLanguage } from '../../context/LanguageContext';
import { ETHIOPIAN_REGIONS } from '../../types/property';

interface Step4Props {
  form: UseFormReturn<any>;
}

const Step4Location: React.FC<Step4Props> = ({ form }) => {
  const { translate } = useLanguage();
  const { register, watch, setValue, formState: { errors } } = form;
  
  const address = watch('address');
  const city = watch('city');
  const region = watch('region');

  const regionTranslations: { [key: string]: { en: string; am: string } } = {
    'Addis Ababa': { en: 'Addis Ababa', am: 'አዲስ አበባ' },
    'Afar': { en: 'Afar', am: 'አፋር' },
    'Amhara': { en: 'Amhara', am: 'አማራ' },
    'Benishangul-Gumuz': { en: 'Benishangul-Gumuz', am: 'ቤንሻንጉል ጉሙዝ' },
    'Dire Dawa': { en: 'Dire Dawa', am: 'ድሬዳዋ' },
    'Gambela': { en: 'Gambela', am: 'ጋምቤላ' },
    'Harari': { en: 'Harari', am: 'ሐረሪ' },
    'Oromia': { en: 'Oromia', am: 'ኦሮሚያ' },
    'Sidama': { en: 'Sidama', am: 'ሲዳማ' },
    'SNNPR': { en: 'SNNPR', am: 'ደቡብ ብሔሮች' },
    'Somali': { en: 'Somali', am: 'ሶማሌ' },
    'Tigray': { en: 'Tigray', am: 'ትግራይ' }
  };

  const popularCities: { [key: string]: string[] } = {
    'Addis Ababa': ['Bole', 'Kirkos', 'Yeka', 'Arada', 'Lideta', 'Kolfe Keranio', 'Gulele', 'Nifas Silk-Lafto', 'Akaky Kaliti', 'Addis Ketema'],
    'Oromia': ['Adama', 'Jimma', 'Bishoftu', 'Shashamane', 'Hawassa', 'Nekemte', 'Ambo', 'Asella'],
    'Amhara': ['Bahir Dar', 'Gondar', 'Dessie', 'Debre Birhan', 'Kombolcha', 'Debre Markos'],
    'Tigray': ['Mekelle', 'Adigrat', 'Axum', 'Shire', 'Alamata'],
    'SNNPR': ['Hawassa', 'Arba Minch', 'Dilla', 'Sodo', 'Hosanna'],
    'Dire Dawa': ['Dire Dawa'],
    'Harari': ['Harar'],
    'Afar': ['Semera', 'Assaita'],
    'Benishangul-Gumuz': ['Assosa'],
    'Gambela': ['Gambela'],
    'Somali': ['Jijiga', 'Dire Dawa'],
    'Sidama': ['Hawassa', 'Dilla', 'Yirgalem']
  };

  return (
    <div className="space-y-6">
      {/* Street Address */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <MapPin className="w-4 h-4 inline mr-2" />
          {translate('Street Address', 'የመንገድ አድራሻ')} *
        </label>
        <Input
          {...register('address')}
          placeholder={translate(
            'e.g., Bole Road, near Atlas Hotel',
            'ምሳሌ፣ ቦሌ መንገድ፣ አትላስ ሆቴል አጠገብ'
          )}
          className="w-full"
        />
        {errors.address && (
          <p className="text-red-500 text-sm mt-1">
            {translate('Street address is required', 'የመንገድ አድራሻ ያስፈልጋል')}
          </p>
        )}
        <p className="text-xs text-gray-500 mt-1">
          {translate('Include landmarks or nearby places for better visibility', 'ለተሻለ እይታ ምልክቶችን ወይም አቅራቢያ ያሉ ቦታዎችን ያካትቱ')}
        </p>
      </div>

      {/* Region Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {translate('Region/State', 'ክልል/ግዛት')} *
        </label>
        <Select
          value={region}
          onValueChange={(value) => {
            setValue('region', value);
            setValue('city', ''); // Reset city when region changes
          }}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder={translate('Select region', 'ክልል ይምረጡ')} />
          </SelectTrigger>
          <SelectContent>
            {ETHIOPIAN_REGIONS.map((regionName) => (
              <SelectItem key={regionName} value={regionName}>
                <div className="flex items-center">
                  <span>
                    {translate(
                      regionTranslations[regionName]?.en || regionName,
                      regionTranslations[regionName]?.am || regionName
                    )}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.region && (
          <p className="text-red-500 text-sm mt-1">
            {translate('Please select a region', 'እባክዎ ክልል ይምረጡ')}
          </p>
        )}
      </div>

      {/* City Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {translate('City/Town', 'ከተማ/ሰፈር')} *
        </label>
        
        {region && popularCities[region] ? (
          // Show dropdown for popular cities if region is selected
          <Select
            value={city}
            onValueChange={(value) => setValue('city', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={translate('Select city', 'ከተማ ይምረጡ')} />
            </SelectTrigger>
            <SelectContent>
              {popularCities[region].map((cityName) => (
                <SelectItem key={cityName} value={cityName}>
                  {cityName}
                </SelectItem>
              ))}
              <SelectItem value="other">
                {translate('Other (specify below)', 'ሌላ (ከታች ይግለጹ)')}
              </SelectItem>
            </SelectContent>
          </Select>
        ) : (
          // Show text input if no region selected or no popular cities
          <Input
            {...register('city')}
            placeholder={translate('Enter city name', 'የከተማ ስም ያስገቡ')}
            className="w-full"
          />
        )}
        
        {/* Custom city input when "Other" is selected */}
        {city === 'other' && (
          <div className="mt-3">
            <Input
              placeholder={translate('Enter city name', 'የከተማ ስም ያስገቡ')}
              onChange={(e) => setValue('city', e.target.value)}
              className="w-full"
            />
          </div>
        )}
        
        {errors.city && (
          <p className="text-red-500 text-sm mt-1">
            {translate('City is required', 'ከተማ ያስፈልጋል')}
          </p>
        )}
      </div>

      {/* Location Summary */}
      {(address || city || region) && (
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-800 mb-2 flex items-center">
            <Navigation className="w-4 h-4 mr-2" />
            {translate('Location Summary', 'የአካባቢ ማጠቃለያ')}
          </h4>
          <div className="text-sm text-blue-700">
            <p className="font-medium">
              {[address, city, region].filter(Boolean).join(', ') || 
                translate('Complete the form to see location summary', 'የአካባቢ ማጠቃለያ ለማየት ቅጹን ይሙሉ')}
            </p>
          </div>
        </div>
      )}

      {/* Location Tips */}
      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
        <h4 className="font-medium text-green-800 mb-2">
          {translate('📍 Location Tips', '📍 የአካባቢ ምክሮች')}
        </h4>
        <ul className="text-sm text-green-700 space-y-1">
          <li>• {translate('Be specific about the location for better visibility', 'ለተሻለ እይታ ስለ አካባቢው ግልጽ ይሁኑ')}</li>
          <li>• {translate('Include nearby landmarks (schools, hospitals, malls)', 'አቅራቢያ ያሉ ምልክቶችን ያካትቱ (ትምህርት ቤቶች፣ ሆስፒታሎች፣ ሞሎች)')}</li>
          <li>• {translate('Mention public transport accessibility', 'የህዝብ ማመላለሻ ተደራሽነትን ይጥቀሱ')}</li>
          <li>• {translate('Accurate location helps buyers find your property', 'ትክክለኛ አካባቢ ገዢዎች ንብረትዎን እንዲያገኙ ይረዳል')}</li>
        </ul>
      </div>

      {/* Mobile Helper */}
      <div className="block md:hidden text-center">
        <p className="text-xs text-gray-500">
          {translate('Select region first, then city will be available', 'መጀመሪያ ክልል ይምረጡ፣ ከዚያ ከተማ ይገኛል')}
        </p>
      </div>
    </div>
  );
};

export default Step4Location;
