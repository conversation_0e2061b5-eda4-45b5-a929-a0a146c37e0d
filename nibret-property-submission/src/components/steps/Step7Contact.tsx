import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { User, Phone, Mail, Shield } from 'lucide-react';
import { Input } from '../ui/input';
import { useLanguage } from '../../context/LanguageContext';
import { validatePhoneNumber, formatPhoneNumber } from '../../lib/utils';

interface Step7Props {
  form: UseFormReturn<any>;
}

const Step7Contact: React.FC<Step7Props> = ({ form }) => {
  const { translate } = useLanguage();
  const { register, watch, setValue, formState: { errors } } = form;
  
  const contactInfo = watch('contact_info') || {};
  const { name, phone, email, agent_name } = contactInfo;

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setValue('contact_info.phone', value);
    
    // Auto-format phone number
    if (value.length >= 9) {
      const formatted = formatPhoneNumber(value);
      if (formatted !== value) {
        setValue('contact_info.phone', formatted);
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Contact Person Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <User className="w-4 h-4 inline mr-2" />
          {translate('Your Full Name', 'ሙሉ ስምዎ')} *
        </label>
        <Input
          {...register('contact_info.name')}
          placeholder={translate('e.g., John Doe', 'ምሳሌ፣ አበበ ከበደ')}
          className="w-full"
        />
        {errors.contact_info && 'name' in errors.contact_info && (
          <p className="text-red-500 text-sm mt-1">
            {translate('Your name is required', 'ስምዎ ያስፈልጋል')}
          </p>
        )}
        <p className="text-xs text-gray-500 mt-1">
          {translate('This will be shown to potential buyers/renters', 'ይህ ለሚሆኑ ገዢዎች/ተከራዮች ይታያል')}
        </p>
      </div>

      {/* Phone Number */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <Phone className="w-4 h-4 inline mr-2" />
          {translate('Phone Number', 'ስልክ ቁጥር')} *
        </label>
        <Input
          type="tel"
          value={phone || ''}
          onChange={handlePhoneChange}
          placeholder={translate('e.g., +251911234567 or 0911234567', 'ምሳሌ፣ +251911234567 ወይም 0911234567')}
          className="w-full"
        />
        {errors.contact_info && 'phone' in errors.contact_info && (
          <p className="text-red-500 text-sm mt-1">
            {translate('Valid phone number is required', 'ትክክለኛ ስልክ ቁጥር ያስፈልጋል')}
          </p>
        )}
        {phone && !validatePhoneNumber(phone) && (
          <p className="text-yellow-600 text-sm mt-1">
            {translate('Please enter a valid Ethiopian phone number', 'እባክዎ ትክክለኛ የኢትዮጵያ ስልክ ቁጥር ያስገቡ')}
          </p>
        )}
        <p className="text-xs text-gray-500 mt-1">
          {translate('Primary contact number for inquiries', 'ለጥያቄዎች ዋና የመገናኛ ቁጥር')}
        </p>
      </div>

      {/* Email Address */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <Mail className="w-4 h-4 inline mr-2" />
          {translate('Email Address', 'ኢሜይል አድራሻ')} *
        </label>
        <Input
          type="email"
          {...register('contact_info.email')}
          placeholder={translate('e.g., <EMAIL>', 'ምሳሌ፣ <EMAIL>')}
          className="w-full"
        />
        {errors.contact_info && 'email' in errors.contact_info && (
          <p className="text-red-500 text-sm mt-1">
            {translate('Valid email address is required', 'ትክክለኛ ኢሜይል አድራሻ ያስፈልጋል')}
          </p>
        )}
        <p className="text-xs text-gray-500 mt-1">
          {translate('For property updates and notifications', 'ለንብረት ዝማኔዎች እና ማሳወቂያዎች')}
        </p>
      </div>

      {/* Agent Name (Optional) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {translate('Agent/Representative Name (Optional)', 'ወኪል/ተወካይ ስም (አማራጭ)')}
        </label>
        <Input
          {...register('contact_info.agent_name')}
          placeholder={translate('e.g., Real Estate Agent Name', 'ምሳሌ፣ የሪል እስቴት ወኪል ስም')}
          className="w-full"
        />
        <p className="text-xs text-gray-500 mt-1">
          {translate('If you are listing on behalf of someone else', 'ለሌላ ሰው ወክለው እየዘረዘሩ ከሆነ')}
        </p>
      </div>

      {/* Contact Summary */}
      {(name || phone || email) && (
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-800 mb-3">
            {translate('Contact Information Summary', 'የመገናኛ መረጃ ማጠቃለያ')}
          </h4>
          <div className="space-y-2 text-sm text-blue-700">
            {name && (
              <div className="flex items-center">
                <User className="w-4 h-4 mr-2" />
                <span className="font-medium">{translate('Name:', 'ስም:')}</span>
                <span className="ml-2">{name}</span>
              </div>
            )}
            {phone && (
              <div className="flex items-center">
                <Phone className="w-4 h-4 mr-2" />
                <span className="font-medium">{translate('Phone:', 'ስልክ:')}</span>
                <span className="ml-2">{phone}</span>
                {validatePhoneNumber(phone) && (
                  <span className="ml-2 text-green-600">✓</span>
                )}
              </div>
            )}
            {email && (
              <div className="flex items-center">
                <Mail className="w-4 h-4 mr-2" />
                <span className="font-medium">{translate('Email:', 'ኢሜይል:')}</span>
                <span className="ml-2">{email}</span>
              </div>
            )}
            {agent_name && (
              <div className="flex items-center">
                <User className="w-4 h-4 mr-2" />
                <span className="font-medium">{translate('Agent:', 'ወኪል:')}</span>
                <span className="ml-2">{agent_name}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Privacy Notice */}
      <div className="bg-green-50 p-4 rounded-lg border border-green-200">
        <h4 className="font-medium text-green-800 mb-2 flex items-center">
          <Shield className="w-4 h-4 mr-2" />
          {translate('Privacy & Security', 'ግላዊነት እና ደህንነት')}
        </h4>
        <ul className="text-sm text-green-700 space-y-1">
          <li>• {translate('Your contact information is secure and encrypted', 'የመገናኛ መረጃዎ ደህንነቱ የተጠበቀ እና የተመሰጠረ ነው')}</li>
          <li>• {translate('Only serious buyers/renters will contact you', 'ቁርጠኛ ገዢዎች/ተከራዮች ብቻ ያገኙዎታል')}</li>
          <li>• {translate('You can update your contact info anytime', 'የመገናኛ መረጃዎን በማንኛውም ጊዜ ማዘመን ይችላሉ')}</li>
          <li>• {translate('We never share your info with third parties', 'መረጃዎን ለሦስተኛ ወገን አንሰጥም')}</li>
        </ul>
      </div>

      {/* Contact Tips */}
      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <h4 className="font-medium text-yellow-800 mb-2">
          {translate('📞 Contact Tips', '📞 የመገናኛ ምክሮች')}
        </h4>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• {translate('Keep your phone available for quick responses', 'ለፈጣን ምላሽ ስልክዎን ዝግጁ ያድርጉ')}</li>
          <li>• {translate('Check your email regularly for inquiries', 'ለጥያቄዎች ኢሜይልዎን በመደበኛነት ይመልከቱ')}</li>
          <li>• {translate('Be professional in all communications', 'በሁሉም ግንኙነቶች ሙያዊ ይሁኑ')}</li>
          <li>• {translate('Respond to inquiries within 24 hours', 'ጥያቄዎችን በ24 ሰዓት ውስጥ ይመልሱ')}</li>
        </ul>
      </div>

      {/* Mobile Helper */}
      <div className="block md:hidden text-center">
        <p className="text-xs text-gray-500">
          {translate('Double-check your contact information for accuracy', 'የመገናኛ መረጃዎን ትክክለኛነት ለማረጋገጥ ሁለት ጊዜ ይመልከቱ')}
        </p>
      </div>
    </div>
  );
};

export default Step7Contact;
