import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { BedDouble, Bath, Square, Calendar, Ruler } from 'lucide-react';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { useLanguage } from '../../context/LanguageContext';

interface Step3Props {
  form: UseFormReturn<any>;
}

const Step3PropertyDetails: React.FC<Step3Props> = ({ form }) => {
  const { translate } = useLanguage();
  const { register, watch, setValue, formState: { errors } } = form;
  
  const beds = watch('beds');
  const baths = watch('baths');
  const sqft = watch('sqft');
  const yearBuilt = watch('yearBuilt');
  // const lotSize = watch('lotSize');

  const incrementValue = (field: string, current: number, max: number = 20) => {
    if (current < max) {
      setValue(field, current + 1);
    }
  };

  const decrementValue = (field: string, current: number, min: number = 0) => {
    if (current > min) {
      setValue(field, current - 1);
    }
  };

  return (
    <div className="space-y-8">
      {/* Bedrooms and Bathrooms */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Bedrooms */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            <BedDouble className="w-4 h-4 inline mr-2" />
            {translate('Bedrooms', 'መኝታ ክፍሎች')} *
          </label>
          <div className="flex items-center space-x-3">
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => decrementValue('beds', beds, 0)}
              disabled={beds <= 0}
              className="h-12 w-12"
            >
              -
            </Button>
            <div className="flex-1 text-center">
              <div className="text-2xl font-bold text-nibret-blue">{beds}</div>
              <div className="text-xs text-gray-500">
                {translate('bedrooms', 'መኝታ ክፍሎች')}
              </div>
            </div>
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => incrementValue('beds', beds, 20)}
              disabled={beds >= 20}
              className="h-12 w-12"
            >
              +
            </Button>
          </div>
          {errors.beds && (
            <p className="text-red-500 text-sm mt-1">
              {translate('Please specify number of bedrooms', 'እባክዎ የመኝታ ክፍሎች ቁጥር ይግለጹ')}
            </p>
          )}
        </div>

        {/* Bathrooms */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            <Bath className="w-4 h-4 inline mr-2" />
            {translate('Bathrooms', 'መታጠቢያ ክፍሎች')} *
          </label>
          <div className="flex items-center space-x-3">
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => decrementValue('baths', baths, 0)}
              disabled={baths <= 0}
              className="h-12 w-12"
            >
              -
            </Button>
            <div className="flex-1 text-center">
              <div className="text-2xl font-bold text-nibret-blue">{baths}</div>
              <div className="text-xs text-gray-500">
                {translate('bathrooms', 'መታጠቢያ ክፍሎች')}
              </div>
            </div>
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => incrementValue('baths', baths, 20)}
              disabled={baths >= 20}
              className="h-12 w-12"
            >
              +
            </Button>
          </div>
          {errors.baths && (
            <p className="text-red-500 text-sm mt-1">
              {translate('Please specify number of bathrooms', 'እባክዎ የመታጠቢያ ክፍሎች ቁጥር ይግለጹ')}
            </p>
          )}
        </div>
      </div>

      {/* Square Footage */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <Square className="w-4 h-4 inline mr-2" />
          {translate('Square Footage (sqft)', 'ስኩዌር ጫማ (sqft)')} *
        </label>
        <Input
          type="number"
          {...register('sqft', { valueAsNumber: true })}
          placeholder={translate('e.g., 1200', 'ምሳሌ፣ 1200')}
          className="w-full"
          min="1"
          step="10"
        />
        {errors.sqft && (
          <p className="text-red-500 text-sm mt-1">
            {translate('Square footage must be at least 1', 'ስኩዌር ጫማ ቢያንስ 1 መሆን አለበት')}
          </p>
        )}
        <p className="text-xs text-gray-500 mt-1">
          {translate('Total interior floor space', 'አጠቃላይ የውስጥ ወለል ቦታ')}
        </p>
      </div>

      {/* Optional Fields */}
      <div className="border-t pt-6">
        <h4 className="text-md font-medium text-gray-800 mb-4">
          {translate('Additional Details (Optional)', 'ተጨማሪ ዝርዝሮች (አማራጭ)')}
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Year Built */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="w-4 h-4 inline mr-2" />
              {translate('Year Built', 'የተገነባበት ዓመት')}
            </label>
            <Input
              type="number"
              {...register('yearBuilt', { valueAsNumber: true })}
              placeholder={translate('e.g., 2020', 'ምሳሌ፣ 2020')}
              className="w-full"
              min="1800"
              max={new Date().getFullYear() + 5}
            />
            <p className="text-xs text-gray-500 mt-1">
              {translate('When was the property built?', 'ንብረቱ መቼ ተገንብቷል?')}
            </p>
          </div>

          {/* Lot Size */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Ruler className="w-4 h-4 inline mr-2" />
              {translate('Lot Size (sqft)', 'የመሬት መጠን (sqft)')}
            </label>
            <Input
              type="number"
              {...register('lotSize', { valueAsNumber: true })}
              placeholder={translate('e.g., 5000', 'ምሳሌ፣ 5000')}
              className="w-full"
              min="0"
              step="100"
            />
            <p className="text-xs text-gray-500 mt-1">
              {translate('Total land area including outdoor space', 'የውጭ ቦታን ጨምሮ አጠቃላይ የመሬት ስፋት')}
            </p>
          </div>
        </div>
      </div>

      {/* Property Summary */}
      {(beds > 0 || baths > 0 || sqft > 0) && (
        <div className="bg-nibret-blue/5 p-4 rounded-lg border border-nibret-blue/20">
          <h4 className="font-medium text-nibret-blue mb-2">
            {translate('Property Summary', 'የንብረት ማጠቃለያ')}
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-nibret-blue">{beds}</div>
              <div className="text-gray-600">{translate('Beds', 'መኝታ')}</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-nibret-blue">{baths}</div>
              <div className="text-gray-600">{translate('Baths', 'መታጠቢያ')}</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-nibret-blue">{sqft || 0}</div>
              <div className="text-gray-600">{translate('Sqft', 'ስኩ.ጫማ')}</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-nibret-blue">{yearBuilt || 'N/A'}</div>
              <div className="text-gray-600">{translate('Built', 'ተገንብቷል')}</div>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Helper */}
      <div className="block md:hidden text-center">
        <p className="text-xs text-gray-500">
          {translate('Use + and - buttons to adjust values', '+ እና - ቁልፎችን ተጠቅመው እሴቶችን ያስተካክሉ')}
        </p>
      </div>
    </div>
  );
};

export default Step3PropertyDetails;
