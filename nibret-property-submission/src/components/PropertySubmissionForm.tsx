import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Home,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  Building,
  MapPin,
  Camera,
  User,
  FileText
} from 'lucide-react';

import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Progress } from './ui/progress';
import { useToast } from './ui/use-toast';
import LanguageSwitcher from './LanguageSwitcher';
import { useLanguage } from '../context/LanguageContext';

import { PropertySubmission, FORM_STEPS } from '../types/property';
import { propertyAPI } from '../services/api';

// Import step components
import Step1PropertyType from './steps/Step1PropertyType';
import Step2BasicInfo from './steps/Step2BasicInfo';
import Step3PropertyDetails from './steps/Step3PropertyDetails';
import Step4Location from './steps/Step4Location';
import Step5Features from './steps/Step5Features';
import Step6Photos from './steps/Step6Photos';
import Step7Contact from './steps/Step7Contact';
import Step8Review from './steps/Step8Review';

// Form validation schema
const propertySubmissionSchema = z.object({
  // Step 1: Property Type & Purpose
  propertyType: z.enum(['house', 'apartment', 'condo', 'villa', 'townhouse', 'studio', 'other']),
  status: z.enum(['for_sale', 'for_rent']),
  listing_type: z.enum(['sale', 'rent', 'both']),
  
  // Step 2: Basic Information
  title: z.string().min(5, 'Title must be at least 5 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  price: z.number().min(1, 'Price must be greater than 0'),
  currency: z.enum(['ETB', 'USD']).default('ETB'),
  
  // Step 3: Property Details
  beds: z.number().min(0).max(20),
  baths: z.number().min(0).max(20),
  sqft: z.number().min(1, 'Square footage must be at least 1'),
  yearBuilt: z.number().min(1800).max(new Date().getFullYear() + 5).optional(),
  lotSize: z.number().min(0).optional(),
  
  // Step 4: Location
  address: z.string().min(5, 'Address must be at least 5 characters'),
  city: z.string().min(2, 'City is required'),
  region: z.string().min(2, 'Region is required'),
  
  // Step 5: Features
  features: z.array(z.string()).default([]),
  
  // Step 6: Images
  images: z.array(z.string()).default([]),
  
  // Step 7: Contact Information
  contact_info: z.object({
    name: z.string().min(2, 'Name is required'),
    phone: z.string().min(10, 'Valid phone number is required'),
    email: z.string().email('Valid email is required'),
    agent_name: z.string().optional(),
  }),
  
  // Step 8: Additional Notes
  additional_notes: z.string().optional(),
});

type FormData = z.infer<typeof propertySubmissionSchema>;

const PropertySubmissionForm: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { translate } = useLanguage();

  const form = useForm<FormData>({
    resolver: zodResolver(propertySubmissionSchema),
    defaultValues: {
      propertyType: 'house',
      status: 'for_sale',
      listing_type: 'sale',
      currency: 'ETB',
      beds: 1,
      baths: 1,
      sqft: 50,
      address: '',
      city: '',
      region: '',
      features: [],
      images: [],
      contact_info: {
        name: '',
        phone: '',
        email: '',
        agent_name: '',
      },
      additional_notes: '',
    },
    mode: 'onChange'
  });

  const totalSteps = FORM_STEPS.length;
  const progress = (currentStep / totalSteps) * 100;

  const nextStep = async () => {
    const currentStepData = FORM_STEPS[currentStep - 1];
    const isValid = await form.trigger(currentStepData.fields as any);
    
    if (isValid && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else if (!isValid) {
      toast({
        title: "Please fix the errors",
        description: "Some fields need your attention before proceeding.",
        variant: "destructive"
      });
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    
    try {
      // Transform form data to PropertySubmission format
      const propertyData: PropertySubmission = {
        ...data,
        lat: 9.0320, // Default coordinates (will be improved with geocoding)
        lng: 38.7469,
      };

      const result = await propertyAPI.submitProperty(propertyData);
      
      if (result.success) {
        toast({
          title: translate("Property submitted successfully!", "ንብረት በተሳካ ሁኔታ ተላክቷል!"),
          description: translate(
            "Your property has been submitted and will be reviewed shortly.",
            "ንብረትዎ ተላክቷል እና በቅርቡ ይገመገማል።"
          ),
        });
        navigate('/success');
      } else {
        throw new Error(result.error || 'Failed to submit property');
      }
    } catch (error) {
      console.error('Submission error:', error);
      toast({
        title: translate("Submission failed", "ማስገባት አልተሳካም"),
        description: translate(
          error instanceof Error ? error.message : "Please try again later.",
          error instanceof Error ? error.message : "እባክዎ በኋላ እንደገና ይሞክሩ።"
        ),
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStepIcon = (stepNumber: number) => {
    switch (stepNumber) {
      case 1: return <Building className="w-5 h-5" />;
      case 2: return <FileText className="w-5 h-5" />;
      case 3: return <Home className="w-5 h-5" />;
      case 4: return <MapPin className="w-5 h-5" />;
      case 5: return <CheckCircle className="w-5 h-5" />;
      case 6: return <Camera className="w-5 h-5" />;
      case 7: return <User className="w-5 h-5" />;
      case 8: return <CheckCircle className="w-5 h-5" />;
      default: return <Home className="w-5 h-5" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex-1"></div>
            <div className="flex items-center justify-center">
              <div className="bg-nibret-blue p-3 rounded-full">
                <Home className="w-8 h-8 text-white" />
              </div>
            </div>
            <div className="flex-1 flex justify-end">
              <LanguageSwitcher />
            </div>
          </div>
          <h1 className="text-2xl md:text-3xl font-bold text-nibret-blue mb-2">
            {translate('Submit Your Property', 'ንብረትዎን ያስተዋውቁ')}
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto text-sm md:text-base px-4">
            {translate(
              'List your property on Nibret Real Estate Platform. Follow our simple step-by-step process to reach thousands of potential buyers and renters.',
              'ንብረትዎን በኒብረት ሪል እስቴት መድረክ ላይ ያስተዋውቁ። በሺዎች የሚቆጠሩ ገዢዎችን እና ተከራዮችን ለማግኘት ቀላል ደረጃ በደረጃ ሂደታችንን ይከተሉ።'
            )}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6 md:mb-8">
          <div className="flex justify-between items-center mb-4">
            <span className="text-xs md:text-sm font-medium text-gray-500">
              {translate(`Step ${currentStep} of ${totalSteps}`, `ደረጃ ${currentStep} ከ ${totalSteps}`)}
            </span>
            <span className="text-xs md:text-sm font-medium text-nibret-blue">
              {Math.round(progress)}% {translate('Complete', 'ተጠናቅቋል')}
            </span>
          </div>
          <Progress value={progress} className="h-2 md:h-3" />
        </div>

        {/* Step Indicators */}
        <div className="mb-6 md:mb-8">
          {/* Mobile: Show only current step */}
          <div className="block md:hidden">
            <div className="text-center">
              <div className={`
                step-indicator mb-2 mx-auto
                ${currentStep === FORM_STEPS[currentStep - 1]?.id ? 'active' : ''}
              `}>
                {getStepIcon(currentStep)}
              </div>
              <span className="text-sm font-medium text-nibret-blue">
                {translate(FORM_STEPS[currentStep - 1]?.title, FORM_STEPS[currentStep - 1]?.titleAm)}
              </span>
            </div>
          </div>

          {/* Desktop: Show all steps */}
          <div className="hidden md:flex justify-between items-center overflow-x-auto pb-2">
            {FORM_STEPS.map((step) => (
              <div key={step.id} className="flex flex-col items-center min-w-0 flex-1">
                <div className={`
                  step-indicator mb-2
                  ${currentStep === step.id ? 'active' : ''}
                  ${currentStep > step.id ? 'completed' : ''}
                  ${currentStep < step.id ? 'inactive' : ''}
                `}>
                  {currentStep > step.id ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    getStepIcon(step.id)
                  )}
                </div>
                <span className={`
                  text-xs text-center font-medium px-1
                  ${currentStep === step.id ? 'text-nibret-blue' : ''}
                  ${currentStep > step.id ? 'text-nibret-gold' : ''}
                  ${currentStep < step.id ? 'text-gray-400' : ''}
                `}>
                  {translate(step.title, step.titleAm)}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Form Card */}
        <Card className="shadow-lg border-0">
          <CardHeader className="bg-gradient-to-r from-nibret-blue to-nibret-gold text-white p-4 md:p-6">
            <CardTitle className="flex items-center text-lg md:text-xl">
              {getStepIcon(currentStep)}
              <span className="ml-2">
                {translate(FORM_STEPS[currentStep - 1]?.title, FORM_STEPS[currentStep - 1]?.titleAm)}
              </span>
            </CardTitle>
            <CardDescription className="text-white/90 text-sm md:text-base">
              {translate(FORM_STEPS[currentStep - 1]?.description, FORM_STEPS[currentStep - 1]?.descriptionAm)}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-4 md:p-8">
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="step-transition">
                {/* Render step content based on current step */}
                <div className="min-h-[300px] md:min-h-[400px]">
                  {currentStep === 1 && <Step1PropertyType form={form} />}
                  {currentStep === 2 && <Step2BasicInfo form={form} />}
                  {currentStep === 3 && <Step3PropertyDetails form={form} />}
                  {currentStep === 4 && <Step4Location form={form} />}
                  {currentStep === 5 && <Step5Features form={form} />}
                  {currentStep === 6 && <Step6Photos form={form} />}
                  {currentStep === 7 && <Step7Contact form={form} />}
                  {currentStep === 8 && <Step8Review form={form} onEditStep={setCurrentStep} />}
                </div>
              </div>

              {/* Navigation Buttons */}
              <div className="flex flex-col sm:flex-row justify-between gap-3 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  disabled={currentStep === 1}
                  className="flex items-center justify-center order-2 sm:order-1"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  {translate('Previous', 'ቀዳሚ')}
                </Button>

                {currentStep < totalSteps ? (
                  <Button
                    type="button"
                    variant="nibret"
                    onClick={nextStep}
                    className="flex items-center justify-center order-1 sm:order-2"
                  >
                    {translate('Next', 'ቀጣይ')}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    variant="nibret-gold"
                    disabled={isSubmitting}
                    className="flex items-center justify-center order-1 sm:order-2"
                  >
                    {isSubmitting
                      ? translate('Submitting...', 'በመላክ ላይ...')
                      : translate('Submit Property', 'ንብረት አስገባ')
                    }
                    <CheckCircle className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Help Text */}
        <div className="text-center mt-8 px-4">
          <p className="text-xs md:text-sm text-gray-500">
            {translate('Need help? Contact us at', 'እርዳታ ይፈልጋሉ? እዚህ ያግኙን')}{' '}
            <a href="mailto:<EMAIL>" className="text-nibret-blue hover:underline">
              <EMAIL>
            </a>{' '}
            {translate('or call', 'ወይም ይደውሉ')}{' '}
            <a href="tel:+251911234567" className="text-nibret-blue hover:underline">
              +251 91 123 4567
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PropertySubmissionForm;
