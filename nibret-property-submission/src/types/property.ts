export interface PropertySubmission {
  // Basic Information
  title: string;
  description: string;
  
  // Property Details
  propertyType: 'house' | 'apartment' | 'condo' | 'villa' | 'townhouse' | 'studio' | 'other';
  status: 'for_sale' | 'for_rent';
  listing_type: 'sale' | 'rent' | 'both';
  
  // Pricing
  price: number;
  currency: 'ETB' | 'USD';
  
  // Physical Details
  beds: number;
  baths: number;
  sqft: number;
  yearBuilt?: number;
  lotSize?: number;
  
  // Location
  address: string;
  city: string;
  region: string;
  lat?: number;
  lng?: number;
  
  // Features
  features: string[];
  
  // Images
  images: string[];
  
  // Contact Information
  contact_info: {
    name: string;
    phone: string;
    email: string;
    agent_name?: string;
  };
  
  // Additional Information
  additional_notes?: string;
}

export interface FormStep {
  id: number;
  title: string;
  titleAm: string;
  description: string;
  descriptionAm: string;
  fields: string[];
}

export const FORM_STEPS: FormStep[] = [
  {
    id: 1,
    title: "Property Type & Purpose",
    titleAm: "የንብረት አይነት እና ዓላማ",
    description: "Tell us about your property type and listing purpose",
    descriptionAm: "ስለ ንብረትዎ አይነት እና የዝርዝር ዓላማ ይንገሩን",
    fields: ['propertyType', 'status', 'listing_type']
  },
  {
    id: 2,
    title: "Basic Information",
    titleAm: "መሰረታዊ መረጃ",
    description: "Provide basic details about your property",
    descriptionAm: "ስለ ንብረትዎ መሰረታዊ ዝርዝሮችን ያቅርቡ",
    fields: ['title', 'description', 'price', 'currency']
  },
  {
    id: 3,
    title: "Property Details",
    titleAm: "የንብረት ዝርዝሮች",
    description: "Specify the physical characteristics",
    descriptionAm: "አካላዊ ባህሪያትን ይግለጹ",
    fields: ['beds', 'baths', 'sqft', 'yearBuilt', 'lotSize']
  },
  {
    id: 4,
    title: "Location",
    titleAm: "አካባቢ",
    description: "Where is your property located?",
    descriptionAm: "ንብረትዎ የት ይገኛል?",
    fields: ['address', 'city', 'region']
  },
  {
    id: 5,
    title: "Features & Amenities",
    titleAm: "ባህሪያት እና አገልግሎቶች",
    description: "What makes your property special?",
    descriptionAm: "ንብረትዎን ልዩ የሚያደርገው ምንድን ነው?",
    fields: ['features']
  },
  {
    id: 6,
    title: "Photos",
    titleAm: "ፎቶዎች",
    description: "Upload photos of your property",
    descriptionAm: "የንብረትዎን ፎቶዎች ይስቀሉ",
    fields: ['images']
  },
  {
    id: 7,
    title: "Contact Information",
    titleAm: "የመገናኛ መረጃ",
    description: "How can potential buyers/renters reach you?",
    descriptionAm: "ሊሆኑ የሚችሉ ገዢዎች/ተከራዮች እንዴት ሊያገኙዎት ይችላሉ?",
    fields: ['contact_info']
  },
  {
    id: 8,
    title: "Review & Submit",
    titleAm: "ይገምግሙ እና ያስገቡ",
    description: "Review your information and submit",
    descriptionAm: "መረጃዎን ይገምግሙ እና ያስገቡ",
    fields: ['additional_notes']
  }
];

export const PROPERTY_TYPES = [
  { value: 'house', label: 'House' },
  { value: 'apartment', label: 'Apartment' },
  { value: 'condo', label: 'Condominium' },
  { value: 'villa', label: 'Villa' },
  { value: 'townhouse', label: 'Townhouse' },
  { value: 'studio', label: 'Studio' },
  { value: 'other', label: 'Other' }
];

export const ETHIOPIAN_REGIONS = [
  'Addis Ababa',
  'Afar',
  'Amhara',
  'Benishangul-Gumuz',
  'Dire Dawa',
  'Gambela',
  'Harari',
  'Oromia',
  'Sidama',
  'SNNPR',
  'Somali',
  'Tigray'
];

export const COMMON_FEATURES = [
  'Parking',
  'Garden',
  'Swimming Pool',
  'Security System',
  'Air Conditioning',
  'Heating',
  'Balcony',
  'Terrace',
  'Elevator',
  'Furnished',
  'Internet/WiFi',
  'Gym/Fitness Center',
  'Laundry Room',
  'Storage Room',
  'Fireplace',
  'City View',
  'Mountain View',
  'Quiet Neighborhood',
  'Near Schools',
  'Near Shopping',
  'Public Transport Access'
];
