// Test API connection to Nibret backend
const API_BASE_URL = 'http://localhost:3000';

const ADMIN_CREDENTIALS = {
  username: '**********',
  password: 'nibretadmin'
};

async function testAPIConnection() {
  console.log('🧪 Testing Nibret API Connection...\n');

  try {
    // Test 1: Check if API is reachable
    console.log('1️⃣ Testing API reachability...');
    
    const healthResponse = await fetch(`${API_BASE_URL}/properties/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    if (!healthResponse.ok) {
      throw new Error(`API returned status ${healthResponse.status}`);
    }

    const healthData = await healthResponse.json();
    console.log('✅ API is reachable');
    console.log(`✅ Found ${Array.isArray(healthData) ? healthData.length : 0} existing properties`);

    // Test 2: Test property submission
    console.log('\n2️⃣ Testing property submission...');
    
    const testProperty = {
      username: ADMIN_CREDENTIALS.username,
      password: ADMIN_CREDENTIALS.password,
      title: 'Test Property from Submission Form',
      description: 'This is a test property submitted from the property submission form',
      price: 2500000,
      currency: 'ETB',
      beds: 3,
      baths: 2,
      sqft: 120,
      address: 'Test Address, Addis Ababa, Addis Ababa',
      propertyType: 'apartment',
      status: 'for_sale',
      listing_type: 'sale',
      lat: 9.0320,
      lng: 38.7469,
      images: [],
      features: ['Parking', 'Security System'],
      contact_info: {
        phone: '+251911234567',
        email: '<EMAIL>',
        agent_name: 'Test User'
      },
      publish_status: 'published'
    };

    const submitResponse = await fetch(`${API_BASE_URL}/properties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testProperty)
    });

    const submitData = await submitResponse.json();

    if (submitResponse.ok) {
      console.log('✅ Property submission successful!');
      console.log(`✅ Created property with ID: ${submitData.data?.id || submitData.id}`);
      console.log(`✅ Property title: ${submitData.data?.title || submitData.title}`);
      console.log(`✅ Currency: ${submitData.data?.currency || submitData.currency}`);
    } else {
      console.log('❌ Property submission failed');
      console.log('Error:', submitData.error || submitData.message);
    }

    // Test 3: Verify the property appears in the list
    console.log('\n3️⃣ Verifying property appears in list...');
    
    const verifyResponse = await fetch(`${API_BASE_URL}/properties/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const verifyData = await verifyResponse.json();
    const testPropertyInList = verifyData.find(p => p.title === testProperty.title);

    if (testPropertyInList) {
      console.log('✅ Test property found in property list');
      console.log(`✅ Property ID: ${testPropertyInList.id}`);
      console.log(`✅ Property currency: ${testPropertyInList.currency}`);
    } else {
      console.log('❌ Test property not found in list');
    }

    console.log('\n🎉 API Connection Test Complete!');
    console.log('\n📋 Summary:');
    console.log('✅ API is reachable and responding');
    console.log('✅ Property submission endpoint working');
    console.log('✅ Currency field supported');
    console.log('✅ Properties appear in listings');
    console.log('\n💡 The property submission form should work correctly with this API!');

  } catch (error) {
    console.error('\n❌ API Connection Test Failed!');
    console.error('Error:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Make sure the Nibret API backend is running on http://localhost:3000');
    console.error('2. Check that MongoDB is connected and running');
    console.error('3. Verify admin credentials are correct');
    console.error('4. Ensure CORS is configured to allow requests from this domain');
  }
}

// Run the test
testAPIConnection();
