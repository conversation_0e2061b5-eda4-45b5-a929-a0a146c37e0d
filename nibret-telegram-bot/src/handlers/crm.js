const authManager = require('../utils/auth');
const api = require('../utils/api');
const Helpers = require('../utils/helpers');

class CRMHandler {
  constructor(bot) {
    this.bot = bot;
    this.setupHandlers();
  }

  setupHandlers() {
    // Handle CRM-related callbacks
    this.bot.on('callback_query', async (callbackQuery) => {
      const data = callbackQuery.data;
      
      if (data.startsWith('leads_') || data.startsWith('customers_') || 
          data.startsWith('crm_') || data.startsWith('followup_')) {
        await this.bot.answerCallbackQuery(callbackQuery.id);
        await this.handleCRMCallback(callbackQuery);
      }
    });
  }

  async handleCRMCallback(callbackQuery) {
    const userId = callbackQuery.from.id;
    const chatId = callbackQuery.message.chat.id;
    const data = callbackQuery.data;

    try {
      authManager.requireAdmin(userId);

      switch (data) {
        case 'leads_new':
          await this.showNewLeads(chatId, userId);
          break;
        case 'leads_active':
          await this.showActiveLeads(chatId, userId);
          break;
        case 'leads_list':
          await this.showAllLeads(chatId, userId);
          break;
        case 'customers_list':
          await this.showCustomers(chatId, userId);
          break;
        case 'crm_stats':
          await this.showCRMStats(chatId, userId);
          break;
        case 'followups_list':
          await this.showFollowUps(chatId, userId);
          break;
        default:
          if (data.startsWith('lead_')) {
            await this.handleLeadAction(chatId, userId, data);
          } else if (data.startsWith('customer_')) {
            await this.handleCustomerAction(chatId, userId, data);
          }
      }
    } catch (error) {
      console.error('CRM callback error:', error);
      await this.bot.sendMessage(chatId, error.message || '❌ An error occurred.');
    }
  }

  async showNewLeads(chatId, userId) {
    try {
      await this.bot.sendMessage(chatId, '🔄 Loading new leads...');

      const response = await api.getLeads({ status: 'new', limit: 10 });
      
      if (!response.success || !response.leads || response.leads.length === 0) {
        await this.bot.sendMessage(chatId, '📭 No new leads found.');
        return;
      }

      let message = `🆕 *New Leads (${response.leads.length})*\n\n`;
      
      response.leads.forEach((lead, index) => {
        const summary = Helpers.generateLeadSummary(lead);
        message += `*${index + 1}.* ${summary}\n\n`;
      });

      const keyboard = {
        inline_keyboard: [
          [
            { text: '📞 All Active Leads', callback_data: 'leads_active' },
            { text: '👥 All Leads', callback_data: 'leads_list' }
          ],
          [
            { text: '🔄 Refresh', callback_data: 'leads_new' },
            { text: '⬅️ Back to CRM', callback_data: 'crm_menu' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Show new leads error:', error);
      await this.bot.sendMessage(chatId, '❌ Failed to load new leads.');
    }
  }

  async showActiveLeads(chatId, userId) {
    try {
      await this.bot.sendMessage(chatId, '🔄 Loading active leads...');

      const activeStatuses = ['contacted', 'qualified', 'proposal_sent', 'negotiation', 'follow_up'];
      const response = await api.getLeads({ 
        status: { $in: activeStatuses }, 
        limit: 15 
      });
      
      if (!response.success || !response.leads || response.leads.length === 0) {
        await this.bot.sendMessage(chatId, '📭 No active leads found.');
        return;
      }

      let message = `📞 *Active Leads (${response.leads.length})*\n\n`;
      
      response.leads.forEach((lead, index) => {
        const summary = Helpers.generateLeadSummary(lead);
        message += `*${index + 1}.* ${summary}\n`;
        message += `🔗 /lead_${lead._id}\n\n`;
      });

      const keyboard = {
        inline_keyboard: [
          [
            { text: '🆕 New Leads', callback_data: 'leads_new' },
            { text: '👥 All Leads', callback_data: 'leads_list' }
          ],
          [
            { text: '📅 Follow-ups', callback_data: 'followups_list' },
            { text: '📊 CRM Stats', callback_data: 'crm_stats' }
          ],
          [
            { text: '🔄 Refresh', callback_data: 'leads_active' },
            { text: '⬅️ Back to CRM', callback_data: 'crm_menu' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Show active leads error:', error);
      await this.bot.sendMessage(chatId, '❌ Failed to load active leads.');
    }
  }

  async showAllLeads(chatId, userId, page = 1) {
    try {
      await this.bot.sendMessage(chatId, '🔄 Loading all leads...');

      const limit = 10;
      const skip = (page - 1) * limit;
      
      const response = await api.getLeads({ 
        limit, 
        skip,
        sort: { created_at: -1 }
      });
      
      if (!response.success || !response.leads || response.leads.length === 0) {
        await this.bot.sendMessage(chatId, '📭 No leads found.');
        return;
      }

      const totalPages = Math.ceil((response.total || response.leads.length) / limit);
      
      let message = `👥 *All Leads - Page ${page}/${totalPages}*\n\n`;
      
      response.leads.forEach((lead, index) => {
        const summary = Helpers.generateLeadSummary(lead);
        message += `*${skip + index + 1}.* ${summary}\n`;
        message += `🔗 /lead_${lead._id}\n\n`;
      });

      // Generate pagination keyboard
      const paginationKeyboard = Helpers.generatePaginationKeyboard(
        page, totalPages, 'leads_page'
      );

      const keyboard = {
        inline_keyboard: [
          ...paginationKeyboard,
          [
            { text: '🆕 New Leads', callback_data: 'leads_new' },
            { text: '📞 Active Leads', callback_data: 'leads_active' }
          ],
          [
            { text: '🔄 Refresh', callback_data: 'leads_list' },
            { text: '⬅️ Back to CRM', callback_data: 'crm_menu' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Show all leads error:', error);
      await this.bot.sendMessage(chatId, '❌ Failed to load leads.');
    }
  }

  async showCustomers(chatId, userId, page = 1) {
    try {
      await this.bot.sendMessage(chatId, '🔄 Loading customers...');

      const limit = 10;
      const skip = (page - 1) * limit;
      
      const response = await api.getCustomers({ 
        limit, 
        skip,
        sort: { created_at: -1 }
      });
      
      if (!response.success || !response.customers || response.customers.length === 0) {
        await this.bot.sendMessage(chatId, '📭 No customers found.');
        return;
      }

      const totalPages = Math.ceil((response.total || response.customers.length) / limit);
      
      let message = `👥 *Customers - Page ${page}/${totalPages}*\n\n`;
      
      response.customers.forEach((customer, index) => {
        const name = `${customer.first_name} ${customer.last_name}`;
        const phone = customer.phone ? `📞 ${customer.phone}` : '';
        const email = customer.email ? `📧 ${customer.email}` : '';
        const status = customer.status ? `📊 ${customer.status}` : '';
        const created = Helpers.formatRelativeTime(customer.created_at);
        
        message += `*${skip + index + 1}.* ${Helpers.escapeMarkdown(name)}\n`;
        message += `${phone}\n${email}\n${status}\n`;
        message += `📅 Joined: ${created}\n`;
        message += `🔗 /customer_${customer._id}\n\n`;
      });

      // Generate pagination keyboard
      const paginationKeyboard = Helpers.generatePaginationKeyboard(
        page, totalPages, 'customers_page'
      );

      const keyboard = {
        inline_keyboard: [
          ...paginationKeyboard,
          [
            { text: '👥 Active', callback_data: 'customers_active' },
            { text: '🎯 Potential', callback_data: 'customers_potential' }
          ],
          [
            { text: '🔄 Refresh', callback_data: 'customers_list' },
            { text: '⬅️ Back to CRM', callback_data: 'crm_menu' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Show customers error:', error);
      await this.bot.sendMessage(chatId, '❌ Failed to load customers.');
    }
  }

  async showCRMStats(chatId, userId) {
    try {
      await this.bot.sendMessage(chatId, '🔄 Loading CRM statistics...');

      // Get leads stats
      const leadsResponse = await api.getLeads({ limit: 1000 });
      const customersResponse = await api.getCustomers({ limit: 1000 });

      let message = `📊 *CRM Statistics*\n\n`;

      if (leadsResponse.success && leadsResponse.leads) {
        const leads = leadsResponse.leads;
        const totalLeads = leads.length;
        
        // Count by status
        const statusCounts = {};
        leads.forEach(lead => {
          statusCounts[lead.status] = (statusCounts[lead.status] || 0) + 1;
        });

        message += `👥 *Leads Overview*\n`;
        message += `📊 Total Leads: ${totalLeads}\n`;
        message += `🆕 New: ${statusCounts.new || 0}\n`;
        message += `📞 Contacted: ${statusCounts.contacted || 0}\n`;
        message += `✅ Qualified: ${statusCounts.qualified || 0}\n`;
        message += `🤝 In Negotiation: ${statusCounts.negotiation || 0}\n`;
        message += `🎉 Closed Won: ${statusCounts.closed_won || 0}\n`;
        message += `❌ Closed Lost: ${statusCounts.closed_lost || 0}\n\n`;
      }

      if (customersResponse.success && customersResponse.customers) {
        const customers = customersResponse.customers;
        const totalCustomers = customers.length;
        
        // Count by status
        const customerStatusCounts = {};
        customers.forEach(customer => {
          customerStatusCounts[customer.status] = (customerStatusCounts[customer.status] || 0) + 1;
        });

        message += `👤 *Customers Overview*\n`;
        message += `📊 Total Customers: ${totalCustomers}\n`;
        message += `✅ Active: ${customerStatusCounts.active || 0}\n`;
        message += `🎯 Potential: ${customerStatusCounts.potential || 0}\n`;
        message += `💰 Converted: ${customerStatusCounts.converted || 0}\n`;
      }

      const keyboard = {
        inline_keyboard: [
          [
            { text: '👥 View Leads', callback_data: 'leads_list' },
            { text: '👤 View Customers', callback_data: 'customers_list' }
          ],
          [
            { text: '🔄 Refresh Stats', callback_data: 'crm_stats' },
            { text: '⬅️ Back to CRM', callback_data: 'crm_menu' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Show CRM stats error:', error);
      await this.bot.sendMessage(chatId, '❌ Failed to load CRM statistics.');
    }
  }

  async showFollowUps(chatId, userId) {
    try {
      await this.bot.sendMessage(chatId, '🔄 Loading follow-ups...');

      // Get leads that need follow-up
      const response = await api.getLeads({ 
        status: 'follow_up',
        limit: 20,
        sort: { updated_at: -1 }
      });
      
      if (!response.success || !response.leads || response.leads.length === 0) {
        await this.bot.sendMessage(chatId, '📭 No follow-ups scheduled.');
        return;
      }

      let message = `📅 *Scheduled Follow-ups (${response.leads.length})*\n\n`;
      
      response.leads.forEach((lead, index) => {
        const name = `${lead.first_name} ${lead.last_name}`;
        const phone = lead.phone ? `📞 ${lead.phone}` : '';
        const lastContact = Helpers.formatRelativeTime(lead.updated_at);
        
        message += `*${index + 1}.* ${Helpers.escapeMarkdown(name)}\n`;
        message += `${phone}\n`;
        message += `⏰ Last contact: ${lastContact}\n`;
        message += `🔗 /lead_${lead._id}\n\n`;
      });

      const keyboard = {
        inline_keyboard: [
          [
            { text: '📞 Active Leads', callback_data: 'leads_active' },
            { text: '👥 All Leads', callback_data: 'leads_list' }
          ],
          [
            { text: '🔄 Refresh', callback_data: 'followups_list' },
            { text: '⬅️ Back to CRM', callback_data: 'crm_menu' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Show follow-ups error:', error);
      await this.bot.sendMessage(chatId, '❌ Failed to load follow-ups.');
    }
  }

  async handleLeadAction(chatId, userId, data) {
    // Extract lead ID from callback data
    const leadId = data.split('_')[1];
    
    try {
      const response = await api.getLead(leadId);
      
      if (!response.success || !response.lead) {
        await this.bot.sendMessage(chatId, '❌ Lead not found.');
        return;
      }

      const lead = response.lead;
      const summary = Helpers.generateLeadSummary(lead);
      
      const message = `
👤 *Lead Details*

${summary}

*Actions:*
Choose what you'd like to do with this lead:
      `;

      const keyboard = {
        inline_keyboard: [
          [
            { text: '📞 Mark Contacted', callback_data: `lead_status_${leadId}_contacted` },
            { text: '✅ Mark Qualified', callback_data: `lead_status_${leadId}_qualified` }
          ],
          [
            { text: '📄 Proposal Sent', callback_data: `lead_status_${leadId}_proposal_sent` },
            { text: '🤝 In Negotiation', callback_data: `lead_status_${leadId}_negotiation` }
          ],
          [
            { text: '🎉 Closed Won', callback_data: `lead_status_${leadId}_closed_won` },
            { text: '❌ Closed Lost', callback_data: `lead_status_${leadId}_closed_lost` }
          ],
          [
            { text: '📅 Schedule Follow-up', callback_data: `lead_followup_${leadId}` },
            { text: '⬅️ Back to Leads', callback_data: 'leads_list' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Handle lead action error:', error);
      await this.bot.sendMessage(chatId, '❌ Failed to load lead details.');
    }
  }

  async handleCustomerAction(chatId, userId, data) {
    // Extract customer ID from callback data
    const customerId = data.split('_')[1];
    
    try {
      const response = await api.getCustomers({ _id: customerId });
      
      if (!response.success || !response.customers || response.customers.length === 0) {
        await this.bot.sendMessage(chatId, '❌ Customer not found.');
        return;
      }

      const customer = response.customers[0];
      const name = `${customer.first_name} ${customer.last_name}`;
      const phone = customer.phone ? `📞 ${customer.phone}` : '';
      const email = customer.email ? `📧 ${customer.email}` : '';
      const status = customer.status ? `📊 Status: ${customer.status}` : '';
      const created = Helpers.formatDate(customer.created_at);
      
      const message = `
👤 *Customer Details*

*${Helpers.escapeMarkdown(name)}*
${phone}
${email}
${status}
📅 Joined: ${created}

*Property Preferences:*
${customer.preferences ? JSON.stringify(customer.preferences, null, 2) : 'None specified'}
      `;

      const keyboard = {
        inline_keyboard: [
          [
            { text: '📝 Update Status', callback_data: `customer_update_${customerId}` },
            { text: '📞 Create Lead', callback_data: `customer_to_lead_${customerId}` }
          ],
          [
            { text: '⬅️ Back to Customers', callback_data: 'customers_list' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Handle customer action error:', error);
      await this.bot.sendMessage(chatId, '❌ Failed to load customer details.');
    }
  }
}

module.exports = CRMHandler;
