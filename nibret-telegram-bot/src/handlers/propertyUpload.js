const authManager = require('../utils/auth');
const api = require('../utils/api');
const Helpers = require('../utils/helpers');
const config = require('../config/config');

class PropertyUploadHandler {
  constructor(bot) {
    this.bot = bot;
    this.uploadSessions = new Map(); // Store upload sessions
    this.setupHandlers();
  }

  setupHandlers() {
    // Handle property upload callback
    this.bot.on('callback_query', async (callbackQuery) => {
      const data = callbackQuery.data;
      
      if (data === 'property_upload') {
        await this.bot.answerCallbackQuery(callbackQuery.id);
        await this.startPropertyUpload(callbackQuery.message.chat.id, callbackQuery.from.id);
      } else if (data.startsWith('upload_')) {
        await this.bot.answerCallbackQuery(callbackQuery.id);
        await this.handleUploadCallback(callbackQuery);
      }
    });

    // Handle messages during upload process
    this.bot.on('message', async (msg) => {
      const userId = msg.from.id;
      const session = this.uploadSessions.get(userId);
      
      if (session && !msg.text?.startsWith('/')) {
        await this.handleUploadMessage(msg, session);
      }
    });

    // Handle photo uploads
    this.bot.on('photo', async (msg) => {
      const userId = msg.from.id;
      const session = this.uploadSessions.get(userId);
      
      if (session && session.step === 'images') {
        await this.handleImageUpload(msg, session);
      }
    });
  }

  async startPropertyUpload(chatId, userId) {
    try {
      authManager.requireAdmin(userId);

      // Create upload session
      const session = {
        userId,
        chatId,
        step: 'title',
        data: {},
        images: [],
        startTime: new Date()
      };

      this.uploadSessions.set(userId, session);

      const welcomeMessage = `
🏗️ *Property Upload Wizard*

Let's create a new property listing! I'll guide you through each step.

*Step 1/9: Property Title*
Please enter a descriptive title for your property:

Example: "Modern 3BR Apartment in Bole"

Type /cancel to cancel the upload process.
      `;

      await this.bot.sendMessage(chatId, welcomeMessage, {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [{ text: '❌ Cancel Upload', callback_data: 'upload_cancel' }]
          ]
        }
      });

    } catch (error) {
      await this.bot.sendMessage(chatId, error.message);
    }
  }

  async handleUploadMessage(msg, session) {
    const { chatId, step, data } = session;
    const text = msg.text?.trim();

    if (!text) {
      await this.bot.sendMessage(chatId, '❌ Please provide a valid text input.');
      return;
    }

    try {
      switch (step) {
        case 'title':
          await this.handleTitleInput(session, text);
          break;
        case 'description':
          await this.handleDescriptionInput(session, text);
          break;
        case 'price':
          await this.handlePriceInput(session, text);
          break;
        case 'beds':
          await this.handleBedsInput(session, text);
          break;
        case 'baths':
          await this.handleBathsInput(session, text);
          break;
        case 'sqm':
          await this.handleSqmInput(session, text);
          break;
        case 'address':
          await this.handleAddressInput(session, text);
          break;
        case 'type':
          await this.handleTypeSelection(session);
          break;
        case 'images':
          await this.bot.sendMessage(chatId, '📸 Please send images as photos, not text. Send /next when done.');
          break;
        default:
          await this.bot.sendMessage(chatId, '❌ Unknown step. Please try again.');
      }
    } catch (error) {
      console.error('Upload message error:', error);
      await this.bot.sendMessage(chatId, '❌ An error occurred. Please try again.');
    }
  }

  async handleTitleInput(session, title) {
    if (title.length < 3) {
      await this.bot.sendMessage(session.chatId, '❌ Title must be at least 3 characters long.');
      return;
    }

    session.data.title = title;
    session.step = 'description';

    const message = `
✅ Title saved: "${Helpers.escapeMarkdown(title)}"

*Step 2/9: Description*
Please provide a detailed description of the property:

Include features, amenities, and any special details.
      `;

    await this.bot.sendMessage(session.chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [{ text: '❌ Cancel Upload', callback_data: 'upload_cancel' }]
        ]
      }
    });
  }

  async handleDescriptionInput(session, description) {
    if (description.length < 10) {
      await this.bot.sendMessage(session.chatId, '❌ Description must be at least 10 characters long.');
      return;
    }

    session.data.description = description;
    session.step = 'price';

    const message = `
✅ Description saved!

*Step 3/9: Price*
Please enter the property price in ETB:

Example: 5000000 (for 5 million ETB)
Just enter the number without currency symbol.
      `;

    await this.bot.sendMessage(session.chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [{ text: '❌ Cancel Upload', callback_data: 'upload_cancel' }]
        ]
      }
    });
  }

  async handlePriceInput(session, priceText) {
    const price = parseFloat(priceText.replace(/[,\s]/g, ''));
    
    if (isNaN(price) || price <= 0) {
      await this.bot.sendMessage(session.chatId, '❌ Please enter a valid price (numbers only).');
      return;
    }

    session.data.price = price;
    session.data.currency = 'ETB';
    session.step = 'beds';

    const message = `
✅ Price saved: ${Helpers.formatCurrency(price, 'ETB')}

*Step 4/9: Bedrooms*
How many bedrooms does the property have?

Enter a number (0 for studio apartments):
      `;

    await this.bot.sendMessage(session.chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '0', callback_data: 'upload_beds_0' },
            { text: '1', callback_data: 'upload_beds_1' },
            { text: '2', callback_data: 'upload_beds_2' }
          ],
          [
            { text: '3', callback_data: 'upload_beds_3' },
            { text: '4', callback_data: 'upload_beds_4' },
            { text: '5+', callback_data: 'upload_beds_5' }
          ],
          [{ text: '❌ Cancel Upload', callback_data: 'upload_cancel' }]
        ]
      }
    });
  }

  async handleBedsInput(session, bedsText) {
    const beds = parseInt(bedsText);
    
    if (isNaN(beds) || beds < 0) {
      await this.bot.sendMessage(session.chatId, '❌ Please enter a valid number of bedrooms (0 or more).');
      return;
    }

    session.data.beds = beds;
    session.step = 'baths';

    const message = `
✅ Bedrooms saved: ${beds}

*Step 5/9: Bathrooms*
How many bathrooms does the property have?

Enter a number:
      `;

    await this.bot.sendMessage(session.chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '1', callback_data: 'upload_baths_1' },
            { text: '2', callback_data: 'upload_baths_2' },
            { text: '3', callback_data: 'upload_baths_3' }
          ],
          [
            { text: '4', callback_data: 'upload_baths_4' },
            { text: '5+', callback_data: 'upload_baths_5' }
          ],
          [{ text: '❌ Cancel Upload', callback_data: 'upload_cancel' }]
        ]
      }
    });
  }

  async handleBathsInput(session, bathsText) {
    const baths = parseInt(bathsText);
    
    if (isNaN(baths) || baths < 0) {
      await this.bot.sendMessage(session.chatId, '❌ Please enter a valid number of bathrooms (0 or more).');
      return;
    }

    session.data.baths = baths;
    session.step = 'sqm';

    const message = `
✅ Bathrooms saved: ${baths}

*Step 6/9: Area Size*
What is the total area in square meters?

Example: 120 (for 120 m²)
      `;

    await this.bot.sendMessage(session.chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [{ text: '❌ Cancel Upload', callback_data: 'upload_cancel' }]
        ]
      }
    });
  }

  async handleSqmInput(session, sqmText) {
    const sqm = parseFloat(sqmText);
    
    if (isNaN(sqm) || sqm <= 0) {
      await this.bot.sendMessage(session.chatId, '❌ Please enter a valid area size (numbers only).');
      return;
    }

    session.data.sqm = sqm;
    session.step = 'address';

    const message = `
✅ Area saved: ${sqm} m²

*Step 7/9: Address*
Please provide the full address of the property:

Example: "Bole Sub City, Addis Ababa, Ethiopia"
      `;

    await this.bot.sendMessage(session.chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [{ text: '❌ Cancel Upload', callback_data: 'upload_cancel' }]
        ]
      }
    });
  }

  async handleAddressInput(session, address) {
    if (address.length < 5) {
      await this.bot.sendMessage(session.chatId, '❌ Address must be at least 5 characters long.');
      return;
    }

    session.data.address = address;
    session.step = 'type';

    await this.handleTypeSelection(session);
  }

  async handleTypeSelection(session) {
    const message = `
✅ Address saved!

*Step 8/9: Property Type*
What type of property is this?
      `;

    await this.bot.sendMessage(session.chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🏠 House', callback_data: 'upload_type_house' },
            { text: '🏢 Apartment', callback_data: 'upload_type_apartment' }
          ],
          [
            { text: '🏘️ Condo', callback_data: 'upload_type_condo' },
            { text: '🏰 Villa', callback_data: 'upload_type_villa' }
          ],
          [
            { text: '🏘️ Townhouse', callback_data: 'upload_type_townhouse' },
            { text: '🏠 Studio', callback_data: 'upload_type_studio' }
          ],
          [
            { text: '🏢 Office', callback_data: 'upload_type_office' },
            { text: '🏗️ Other', callback_data: 'upload_type_other' }
          ],
          [{ text: '❌ Cancel Upload', callback_data: 'upload_cancel' }]
        ]
      }
    });
  }

  async handleUploadCallback(callbackQuery) {
    const userId = callbackQuery.from.id;
    const chatId = callbackQuery.message.chat.id;
    const data = callbackQuery.data;
    const session = this.uploadSessions.get(userId);

    if (!session) {
      await this.bot.sendMessage(chatId, '❌ No active upload session found.');
      return;
    }

    try {
      if (data === 'upload_cancel') {
        await this.cancelUpload(session);
      } else if (data.startsWith('upload_beds_')) {
        const beds = parseInt(data.split('_')[2]);
        session.data.beds = beds;
        session.step = 'baths';
        await this.handleBedsInput(session, beds.toString());
      } else if (data.startsWith('upload_baths_')) {
        const baths = parseInt(data.split('_')[2]);
        session.data.baths = baths;
        session.step = 'sqm';
        await this.handleBathsInput(session, baths.toString());
      } else if (data.startsWith('upload_type_')) {
        const type = data.split('_')[2];
        session.data.propertyType = type;
        session.step = 'images';
        await this.handleTypeSelected(session, type);
      } else if (data === 'upload_skip_images') {
        await this.handleImagesComplete(session);
      } else if (data === 'upload_finish') {
        await this.finishUpload(session);
      }
    } catch (error) {
      console.error('Upload callback error:', error);
      await this.bot.sendMessage(chatId, '❌ An error occurred. Please try again.');
    }
  }

  async handleTypeSelected(session, type) {
    session.data.propertyType = type;
    session.step = 'images';

    const message = `
✅ Property type saved: ${Helpers.formatPropertyType(type)}

*Step 9/9: Images*
Please send property images (up to ${config.app.maxImagesPerProperty} photos):

📸 Send photos one by one
✅ Click "Finish Upload" when done
⏭️ Click "Skip Images" to upload without photos
      `;

    await this.bot.sendMessage(session.chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '✅ Finish Upload', callback_data: 'upload_finish' },
            { text: '⏭️ Skip Images', callback_data: 'upload_skip_images' }
          ],
          [{ text: '❌ Cancel Upload', callback_data: 'upload_cancel' }]
        ]
      }
    });
  }

  async handleImageUpload(msg, session) {
    if (session.images.length >= config.app.maxImagesPerProperty) {
      await this.bot.sendMessage(session.chatId, 
        `❌ Maximum ${config.app.maxImagesPerProperty} images allowed.`);
      return;
    }

    try {
      // Get the largest photo size
      const photo = msg.photo[msg.photo.length - 1];
      const fileId = photo.file_id;
      
      // Get file info
      const file = await this.bot.getFile(fileId);
      const fileUrl = `https://api.telegram.org/file/bot${config.telegram.token}/${file.file_path}`;
      
      // Store image info
      session.images.push({
        fileId,
        fileUrl,
        fileSize: photo.file_size,
        width: photo.width,
        height: photo.height
      });

      const message = `
📸 Image ${session.images.length} uploaded successfully!

Total images: ${session.images.length}/${config.app.maxImagesPerProperty}

Continue sending more images or click "Finish Upload" when done.
      `;

      await this.bot.sendMessage(session.chatId, message, {
        reply_markup: {
          inline_keyboard: [
            [
              { text: '✅ Finish Upload', callback_data: 'upload_finish' },
              { text: '❌ Cancel Upload', callback_data: 'upload_cancel' }
            ]
          ]
        }
      });

    } catch (error) {
      console.error('Image upload error:', error);
      await this.bot.sendMessage(session.chatId, '❌ Failed to process image. Please try again.');
    }
  }

  async finishUpload(session) {
    const { chatId, data, images } = session;

    try {
      await this.bot.sendMessage(chatId, '🔄 Creating property listing...');

      // Validate data
      const validationErrors = Helpers.validatePropertyData(data);
      if (validationErrors.length > 0) {
        await this.bot.sendMessage(chatId, 
          `❌ Validation errors:\n${validationErrors.join('\n')}`);
        return;
      }

      // Prepare property data
      const propertyData = {
        ...data,
        status: 'for_sale',
        publishStatus: 'published',
        images: images.map(img => img.fileUrl), // Use Telegram URLs for now
        createdBy: 'telegram_bot',
        telegramUserId: session.userId
      };

      // Create property via API
      const result = await api.createProperty(propertyData);

      if (result.success) {
        const property = result.property;
        const summary = Helpers.generatePropertySummary(property);
        
        const successMessage = `
🎉 *Property Created Successfully!*

${summary}

🆔 Property ID: \`${property._id}\`
📅 Created: ${Helpers.formatDate(property.createdAt)}

The property is now live on the Nibret platform!
        `;

        await this.bot.sendMessage(chatId, successMessage, {
          parse_mode: 'Markdown',
          reply_markup: {
            inline_keyboard: [
              [
                { text: '🏠 View Properties', callback_data: 'properties_list' },
                { text: '➕ Upload Another', callback_data: 'property_upload' }
              ],
              [{ text: '🏠 Main Menu', callback_data: 'menu' }]
            ]
          }
        });

        // Clean up session
        this.uploadSessions.delete(session.userId);

      } else {
        throw new Error(result.message || 'Failed to create property');
      }

    } catch (error) {
      console.error('Property creation error:', error);
      
      let errorMessage = '❌ *Failed to create property*\n\n';
      
      if (error.response?.data?.message) {
        errorMessage += error.response.data.message;
      } else {
        errorMessage += error.message || 'Unknown error occurred';
      }

      errorMessage += '\n\nPlease try again or contact support.';

      await this.bot.sendMessage(chatId, errorMessage, {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '🔄 Try Again', callback_data: 'upload_finish' },
              { text: '❌ Cancel Upload', callback_data: 'upload_cancel' }
            ]
          ]
        }
      });
    }
  }

  async cancelUpload(session) {
    const { chatId, userId } = session;
    
    this.uploadSessions.delete(userId);
    
    await this.bot.sendMessage(chatId, '❌ Property upload cancelled.', {
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🏠 Main Menu', callback_data: 'menu' },
            { text: '➕ Start New Upload', callback_data: 'property_upload' }
          ]
        ]
      }
    });
  }
}

module.exports = PropertyUploadHandler;
