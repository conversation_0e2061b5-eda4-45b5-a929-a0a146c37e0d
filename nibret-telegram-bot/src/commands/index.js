const authManager = require('../utils/auth');
const api = require('../utils/api');
const Helpers = require('../utils/helpers');

class CommandHandler {
  constructor(bot) {
    this.bot = bot;
    this.userSessions = new Map(); // Store user conversation states
    this.setupCommands();
  }

  setupCommands() {
    // Public commands
    this.bot.onText(/\/start/, this.handleStart.bind(this));
    this.bot.onText(/\/help/, this.handleHelp.bind(this));
    
    // Admin commands
    this.bot.onText(/\/login/, this.handleLogin.bind(this));
    this.bot.onText(/\/logout/, this.handleLogout.bind(this));
    this.bot.onText(/\/menu/, this.handleMenu.bind(this));
    this.bot.onText(/\/status/, this.handleStatus.bind(this));
    
    // Property commands
    this.bot.onText(/\/properties/, this.handleProperties.bind(this));
    this.bot.onText(/\/upload/, this.handleUploadProperty.bind(this));
    this.bot.onText(/\/search (.+)/, this.handleSearchProperties.bind(this));
    
    // CRM commands
    this.bot.onText(/\/leads/, this.handleLeads.bind(this));
    this.bot.onText(/\/customers/, this.handleCustomers.bind(this));
    this.bot.onText(/\/stats/, this.handleStats.bind(this));
    
    // Callback query handler
    this.bot.on('callback_query', this.handleCallbackQuery.bind(this));
    
    // Message handlers for conversations
    this.bot.on('message', this.handleMessage.bind(this));
  }

  // Start command - Welcome message
  async handleStart(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;
    const firstName = msg.from.first_name || 'User';

    const welcomeMessage = `
🏠 *Welcome to Nibret Real Estate Bot!*

Hello ${Helpers.escapeMarkdown(firstName)}! 👋

I'm your personal assistant for managing properties and CRM activities on the Nibret platform.

*What I can help you with:*
🏢 Property management (upload, edit, view)
👥 CRM activities (leads, customers, follow-ups)
📊 Analytics and statistics
🔍 Property search and filtering

*Getting Started:*
${authManager.hasAdminAccess(userId) ?
  '✅ You have admin access! Use /menu to access all features.' :
  '❌ You need admin privileges to use this bot. Contact your administrator.'}

Use /help to see all available commands.
    `;

    const keyboard = {
      inline_keyboard: authManager.hasAdminAccess(userId) ? [
        [
          { text: '📋 Help', callback_data: 'help' },
          { text: '🏠 Main Menu', callback_data: 'menu' }
        ]
      ] : [
        [
          { text: '📋 Help', callback_data: 'help' }
        ]
      ]
    };

    await this.bot.sendMessage(chatId, welcomeMessage, {
      parse_mode: 'Markdown',
      reply_markup: keyboard
    });
  }

  // Help command
  async handleHelp(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;
    const isAdmin = authManager.hasAdminAccess(userId);
    const isAuthenticated = authManager.isAuthenticated(userId);

    let helpMessage = `
🆘 *Nibret Bot Help*

*Public Commands:*
/start - Welcome message and bot introduction
/help - Show this help message

`;

    if (isAdmin) {
      helpMessage += `
*Admin Commands:*
/login - Authenticate with Nibret API
/logout - End your session
/menu - Show main menu
/status - Check your authentication status

*Property Management:*
/properties - View all properties
/upload - Upload a new property
/search <query> - Search properties

*CRM Management:*
/leads - View and manage leads
/customers - View customer list
/stats - View analytics and statistics

*Usage Tips:*
• Use inline keyboards for easy navigation
• Upload multiple images when creating properties
• Use search filters to find specific properties
• Update lead statuses to track progress
`;
    } else {
      helpMessage += `
❌ *Admin Access Required*

You need admin privileges to use most features of this bot.
Contact your administrator to get access.
`;
    }

    const keyboard = {
      inline_keyboard: isAdmin ? [
        [
          { text: '🏠 Main Menu', callback_data: 'menu' },
          { text: '📊 Quick Stats', callback_data: 'stats' }
        ]
      ] : [
        [
          { text: '🔐 Login', callback_data: 'login' }
        ]
      ]
    };

    await this.bot.sendMessage(chatId, helpMessage, {
      parse_mode: 'Markdown',
      reply_markup: keyboard
    });
  }

  // Login command (now optional for admins)
  async handleLogin(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      // Check if user has admin access
      authManager.requireAdmin(userId);

      // For admins, login is automatic now
      if (authManager.hasAdminAccess(userId)) {
        const successMessage = `
✅ *Welcome Admin!*

You have automatic access to all features.

*Your Access:*
👤 Role: ${authManager.getUserRole(userId)}
🔑 API Access: Enabled
⚡ Auto-authenticated

Use /menu to access all features.
        `;

        const keyboard = {
          inline_keyboard: [
            [
              { text: '🏠 Main Menu', callback_data: 'menu' },
              { text: '📊 Quick Stats', callback_data: 'stats' }
            ]
          ]
        };

        await this.bot.sendMessage(chatId, successMessage, {
          parse_mode: 'Markdown',
          reply_markup: keyboard
        });
        return;
      }

      // Legacy login flow for non-admins (shouldn't reach here)
      await this.bot.sendMessage(chatId, '❌ Access denied. Admin privileges required.');

    } catch (error) {
      console.error('Login error:', error);

      let errorMessage = '❌ *Access Denied*\n\n';

      if (error.message.includes('Unauthorized')) {
        errorMessage += 'You do not have admin privileges to use this bot.';
      } else {
        errorMessage += 'An error occurred. Please try again later.';
      }

      await this.bot.sendMessage(chatId, errorMessage, {
        parse_mode: 'Markdown'
      });
    }
  }

  // Logout command (now just clears session but admins auto-re-authenticate)
  async handleLogout(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      authManager.requireAdmin(userId);
      authManager.destroySession(userId);

      await this.bot.sendMessage(chatId, '✅ Session cleared. As an admin, you still have automatic access to all features.');
    } catch (error) {
      await this.bot.sendMessage(chatId, error.message);
    }
  }

  // Status command
  async handleStatus(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      authManager.requireAdmin(userId);

      const userInfo = authManager.formatUserInfo(userId);

      let statusMessage = `
📊 *Bot Status*

*Your Account:*
👤 Telegram ID: \`${userInfo.telegramId}\`
🎭 Role: ${userInfo.role}
✅ Authentication: Auto-enabled for admins
⚡ Access Level: Full
`;

      if (userInfo.sessionInfo) {
        statusMessage += `
📅 Session Created: ${Helpers.formatDate(userInfo.sessionInfo.createdAt)}
⏰ Last Activity: ${Helpers.formatRelativeTime(userInfo.sessionInfo.lastActivity)}
`;
      }

      statusMessage += `\n*Bot Information:*
🤖 Bot Status: Active
📊 Commands Available: All features enabled
`;

      const keyboard = {
        inline_keyboard: [
          [
            { text: '🏠 Main Menu', callback_data: 'menu' },
            { text: '🔄 Refresh', callback_data: 'status' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, statusMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Status error:', error);
      await this.bot.sendMessage(chatId, error.message || '❌ An error occurred.');
    }
  }

  // Main menu
  async handleMenu(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      authManager.requireAdmin(userId);

      const menuMessage = `
🏠 *Nibret Admin Panel*

Choose an option from the menu below:
`;

      const keyboard = {
        inline_keyboard: [
          [
            { text: '🏢 Properties', callback_data: 'properties_menu' },
            { text: '👥 CRM', callback_data: 'crm_menu' }
          ],
          [
            { text: '📊 Analytics', callback_data: 'stats' },
            { text: '📋 Help', callback_data: 'help' }
          ],
          [
            { text: '🔄 Refresh', callback_data: 'menu' },
            { text: '📊 Status', callback_data: 'status' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, menuMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Menu error:', error);
      await this.bot.sendMessage(chatId, error.message || '❌ Access denied.');
    }
  }

  // Callback query handler
  async handleCallbackQuery(callbackQuery) {
    const chatId = callbackQuery.message.chat.id;
    const userId = callbackQuery.from.id;
    const data = callbackQuery.data;

    // Answer callback query to remove loading state
    await this.bot.answerCallbackQuery(callbackQuery.id);

    try {
      switch (data) {
        case 'help':
          await this.handleHelp({ chat: { id: chatId }, from: { id: userId } });
          break;
        case 'login':
          await this.handleLogin({ chat: { id: chatId }, from: { id: userId } });
          break;
        case 'logout':
          await this.handleLogout({ chat: { id: chatId }, from: { id: userId } });
          break;
        case 'menu':
          await this.handleMenu({ chat: { id: chatId }, from: { id: userId } });
          break;
        case 'stats':
          await this.handleStats({ chat: { id: chatId }, from: { id: userId } });
          break;
        case 'properties_menu':
          await this.handlePropertiesMenu(chatId, userId);
          break;
        case 'crm_menu':
          await this.handleCRMMenu(chatId, userId);
          break;
        default:
          // Handle other callback queries in respective handlers
          await this.handleSpecificCallback(chatId, userId, data);
      }
    } catch (error) {
      console.error('Callback query error:', error);
      await this.bot.sendMessage(chatId, '❌ An error occurred. Please try again.');
    }
  }

  // Properties menu
  async handlePropertiesMenu(chatId, userId) {
    try {
      authManager.requireAdmin(userId);

      const menuMessage = `
🏢 *Property Management*

What would you like to do?
`;

    const keyboard = {
      inline_keyboard: [
        [
          { text: '📋 View All', callback_data: 'properties_list' },
          { text: '➕ Upload New', callback_data: 'property_upload' }
        ],
        [
          { text: '🔍 Search', callback_data: 'property_search' },
          { text: '📊 Statistics', callback_data: 'property_stats' }
        ],
        [
          { text: '⬅️ Back to Menu', callback_data: 'menu' }
        ]
      ]
    };

      await this.bot.sendMessage(chatId, menuMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Properties menu error:', error);
      await this.bot.sendMessage(chatId, error.message || '❌ Access denied.');
    }
  }

  // CRM menu
  async handleCRMMenu(chatId, userId) {
    try {
      authManager.requireAdmin(userId);

      const menuMessage = `
👥 *CRM Management*

Manage your leads and customers:
`;

    const keyboard = {
      inline_keyboard: [
        [
          { text: '🆕 New Leads', callback_data: 'leads_new' },
          { text: '📞 Active Leads', callback_data: 'leads_active' }
        ],
        [
          { text: '👥 All Customers', callback_data: 'customers_list' },
          { text: '📅 Follow-ups', callback_data: 'followups_list' }
        ],
        [
          { text: '📊 CRM Stats', callback_data: 'crm_stats' },
          { text: '⬅️ Back to Menu', callback_data: 'menu' }
        ]
      ]
    };

      await this.bot.sendMessage(chatId, menuMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('CRM menu error:', error);
      await this.bot.sendMessage(chatId, error.message || '❌ Access denied.');
    }
  }

  // Placeholder for specific callback handlers
  async handleSpecificCallback(chatId, userId, data) {
    // This will be implemented in separate handler files
    await this.bot.sendMessage(chatId, `🔄 Feature "${data}" is being implemented...`);
  }

  // Message handler for conversations
  async handleMessage(msg) {
    // Skip if it's a command
    if (msg.text && msg.text.startsWith('/')) {
      return;
    }

    const chatId = msg.chat.id;
    const userId = msg.from.id;

    // Check if user is in a conversation state
    const session = this.userSessions.get(userId);
    if (session) {
      // Handle conversation based on session state
      await this.handleConversation(msg, session);
    }
  }

  // Conversation handler
  async handleConversation(msg, session) {
    // This will be implemented for property upload and other interactive features
    console.log('Handling conversation:', session.state);
  }

  // Property commands
  async handleProperties(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      authManager.requireAdmin(userId);
      await this.handlePropertiesMenu(chatId, userId);
    } catch (error) {
      await this.bot.sendMessage(chatId, error.message || '❌ Access denied.');
    }
  }

  async handleUploadProperty(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      authManager.requireAdmin(userId);

      const message = `
🏢 *Property Upload*

Starting property upload wizard...
Please use the menu for the interactive upload process.
      `;

      const keyboard = {
        inline_keyboard: [
          [
            { text: '➕ Start Upload', callback_data: 'property_upload' },
            { text: '🏠 Properties Menu', callback_data: 'properties_menu' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      await this.bot.sendMessage(chatId, error.message || '❌ Access denied.');
    }
  }

  async handleSearchProperties(msg, match) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      authManager.requireAdmin(userId);

      const searchTerm = match[1];
      await this.bot.sendMessage(chatId, `🔍 Searching for properties: "${searchTerm}"\n\n🔄 This feature will be implemented with API integration.`);

    } catch (error) {
      await this.bot.sendMessage(chatId, error.message || '❌ Access denied.');
    }
  }

  async handleLeads(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      authManager.requireAdmin(userId);
      await this.handleCRMMenu(chatId, userId);
    } catch (error) {
      await this.bot.sendMessage(chatId, error.message || '❌ Access denied.');
    }
  }

  async handleCustomers(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      authManager.requireAdmin(userId);

      const message = `
👥 *Customer Management*

Loading customer list...
Use the CRM menu for full customer management.
      `;

      const keyboard = {
        inline_keyboard: [
          [
            { text: '👥 View All Customers', callback_data: 'customers_list' },
            { text: '🏠 CRM Menu', callback_data: 'crm_menu' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      await this.bot.sendMessage(chatId, error.message || '❌ Access denied.');
    }
  }

  async handleStats(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      authManager.requireAdmin(userId);

      await this.bot.sendMessage(chatId, '📊 Loading statistics...');

      // This will be implemented with actual API calls
      const statsMessage = `
📊 *Quick Statistics*

🏢 Total Properties: Loading...
👥 Total Leads: Loading...
💰 Total Value: Loading...
📈 This Month: Loading...

Use the menu for detailed analytics.
`;

      const keyboard = {
        inline_keyboard: [
          [
            { text: '🏢 Property Stats', callback_data: 'property_stats' },
            { text: '👥 CRM Stats', callback_data: 'crm_stats' }
          ],
          [
            { text: '🏠 Main Menu', callback_data: 'menu' },
            { text: '🔄 Refresh', callback_data: 'stats' }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, statsMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Stats error:', error);
      await this.bot.sendMessage(chatId, error.message || '❌ Access denied.');
    }
  }
}

module.exports = CommandHandler;
