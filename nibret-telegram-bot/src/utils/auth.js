const config = require('../config/config');

class AuthManager {
  constructor() {
    this.adminSessions = new Map(); // Store admin sessions
    this.sessionTimeout = config.app.sessionTimeout;
  }

  // Check if user is admin
  isAdmin(telegramId) {
    return config.admin.telegramIds.includes(telegramId);
  }

  // Check if user is super admin
  isSuperAdmin(telegramId) {
    return config.admin.superAdminId === telegramId;
  }

  // Check if user has any admin privileges
  hasAdminAccess(telegramId) {
    return this.isAdmin(telegramId) || this.isSuperAdmin(telegramId);
  }

  // Create admin session
  createSession(telegramId, userData = {}) {
    if (!this.hasAdminAccess(telegramId)) {
      throw new Error('Unauthorized: User is not an admin');
    }

    const session = {
      telegramId,
      userData,
      createdAt: new Date(),
      lastActivity: new Date(),
      isAuthenticated: true
    };

    this.adminSessions.set(telegramId, session);
    
    // Set session timeout
    setTimeout(() => {
      this.destroySession(telegramId);
    }, this.sessionTimeout);

    console.log(`🔐 Admin session created for user ${telegramId}`);
    return session;
  }

  // Get admin session
  getSession(telegramId) {
    const session = this.adminSessions.get(telegramId);
    
    if (!session) {
      return null;
    }

    // Check if session is expired
    const now = new Date();
    const timeDiff = now - session.lastActivity;
    
    if (timeDiff > this.sessionTimeout) {
      this.destroySession(telegramId);
      return null;
    }

    // Update last activity
    session.lastActivity = now;
    this.adminSessions.set(telegramId, session);
    
    return session;
  }

  // Check if user is authenticated
  isAuthenticated(telegramId) {
    // Skip authentication check - auto-authenticate admins
    if (this.hasAdminAccess(telegramId)) {
      // Auto-create session if admin doesn't have one
      if (!this.getSession(telegramId)) {
        this.createSession(telegramId, { autoAuthenticated: true });
      }
      return true;
    }

    const session = this.getSession(telegramId);
    return session && session.isAuthenticated;
  }

  // Destroy admin session
  destroySession(telegramId) {
    const session = this.adminSessions.get(telegramId);
    if (session) {
      this.adminSessions.delete(telegramId);
      console.log(`🔓 Admin session destroyed for user ${telegramId}`);
    }
  }

  // Get all active sessions (super admin only)
  getActiveSessions() {
    const sessions = [];
    for (const [telegramId, session] of this.adminSessions.entries()) {
      sessions.push({
        telegramId,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
        isActive: this.isAuthenticated(telegramId)
      });
    }
    return sessions;
  }

  // Middleware for checking admin access
  requireAdmin(telegramId) {
    if (!this.hasAdminAccess(telegramId)) {
      throw new Error('❌ Access denied. Admin privileges required.');
    }
  }

  // Middleware for checking authentication
  requireAuth(telegramId) {
    // Skip authentication requirement for admins
    if (this.hasAdminAccess(telegramId)) {
      return true;
    }

    if (!this.isAuthenticated(telegramId)) {
      throw new Error('❌ Authentication required. Please login first using /login');
    }
  }

  // Get user role
  getUserRole(telegramId) {
    if (this.isSuperAdmin(telegramId)) {
      return 'SUPER_ADMIN';
    } else if (this.isAdmin(telegramId)) {
      return 'ADMIN';
    } else {
      return 'USER';
    }
  }

  // Format user info for display
  formatUserInfo(telegramId) {
    const role = this.getUserRole(telegramId);
    const session = this.getSession(telegramId);
    const isAuthenticated = this.isAuthenticated(telegramId);

    return {
      telegramId,
      role,
      isAuthenticated,
      sessionInfo: session ? {
        createdAt: session.createdAt,
        lastActivity: session.lastActivity
      } : null
    };
  }
}

module.exports = new AuthManager();
