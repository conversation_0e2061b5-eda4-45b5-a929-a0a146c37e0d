# Quick Start Guide

Get your Nibret Telegram Bot up and running in 5 minutes!

## Prerequisites

- Node.js 16+ installed
- Telegram account
- Access to Nibret API

## Step 1: Create Telegram Bot

1. Open Telegram and message [@BotFather](https://t.me/BotFather)
2. Send `/newbot` command
3. Choose a name for your bot (e.g., "Nibret Property Manager")
4. Choose a username (e.g., "nibret_property_bot")
5. Copy the bot token (looks like: `*********:ABCdefGHIjklMNOpqrsTUVwxyz`)

## Step 2: Get Your Telegram ID

1. Message [@userinfobot](https://t.me/userinfobot) on Telegram
2. Copy your user ID (a number like: `*********`)

## Step 3: Install and Setup

```bash
# Navigate to the bot directory
cd nibret-telegram-bot

# Install dependencies
npm install

# Run interactive setup
npm run setup
```

The setup will ask for:
- **Bot Token**: Paste the token from BotFather
- **API URL**: Use `https://api.nibret.com` (or your custom URL)
- **API Credentials**: Use `**********` / `nibretadmin` (or your credentials)
- **Admin IDs**: Paste your Telegram ID from userinfobot
- **Webhook**: Leave empty for now (optional)

## Step 4: Start the Bot

```bash
# Development mode (auto-restart on changes)
npm run dev

# Or production mode
npm start
```

You should see:
```
✅ Bot started successfully!
🤖 Bot name: Your Bot Name
👤 Bot username: @your_bot_username
```

## Step 5: Test the Bot

1. Find your bot on Telegram (search for the username you created)
2. Send `/start` - you should get a welcome message
3. Send `/menu` - you should see the main menu (no login required!)

## Quick Commands

Once you start chatting with the bot, try these commands:

- `/menu` - Main menu with all options
- `/upload` - Start uploading a property
- `/properties` - View all properties
- `/leads` - View CRM leads
- `/stats` - View statistics
- `/help` - Get help

## Troubleshooting

### Bot not responding?
- Check if the bot is running (no errors in terminal)
- Verify bot token is correct
- Make sure you're messaging the right bot

### Can't access features?
- Ensure your Telegram ID is in the admin list in `.env`
- Check if API URL is accessible
- Verify API credentials are correct

### Access denied?
- Confirm your Telegram ID is correct
- Check if you're in the `ADMIN_TELEGRAM_IDS` list
- Try `/status` to check your authentication

### Property upload fails?
- Ensure API is running and accessible
- Check all required fields are filled
- Verify image sizes are within limits

## Next Steps

### Production Deployment

1. **Environment Variables**: Set up proper environment variables
2. **Process Manager**: Use PM2 for production
3. **Webhook**: Configure webhook for better performance
4. **Monitoring**: Set up logging and monitoring

### Webhook Setup (Optional)

For production, webhooks are more efficient than polling:

1. Get a public domain/IP
2. Set up SSL certificate
3. Configure webhook URL in setup
4. Use `npm run webhook` instead of `npm start`

### PM2 Deployment

```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start src/bot.js --name "nibret-bot"

# Save PM2 configuration
pm2 save

# Set up auto-start
pm2 startup
```

## Support

- Check the main README.md for detailed documentation
- Review logs for error messages
- Test API connectivity separately
- Verify all configuration values

## Security Notes

- Keep your bot token secret
- Don't share your `.env` file
- Only add trusted users as admins
- Regularly update dependencies

---

🎉 **Congratulations!** Your Nibret Telegram Bot is now ready to manage properties and CRM activities!
