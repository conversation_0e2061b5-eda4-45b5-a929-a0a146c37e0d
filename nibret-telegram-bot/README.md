# Nibret Telegram Bot

A comprehensive Telegram bot for managing properties and CRM activities on the Nibret Real Estate platform.

## Features

### 🏢 Property Management
- **Interactive Property Upload**: Step-by-step wizard for creating property listings
- **Property Search & Filtering**: Find properties by various criteria
- **Property Statistics**: View analytics and performance metrics
- **Image Upload Support**: Upload multiple property images via Telegram

### 👥 CRM Management
- **Lead Tracking**: View and manage leads by status
- **Customer Management**: Track customer information and preferences
- **Follow-up Scheduling**: Schedule and track follow-up activities
- **Activity Logging**: Automatic logging of CRM activities
- **Status Updates**: Update lead and customer statuses

### 🔐 Security & Authentication
- **Admin-Only Access**: Role-based access control
- **Session Management**: Secure session handling with timeouts
- **API Integration**: Secure integration with Nibret API
- **Multi-Admin Support**: Support for multiple admin users

### 📊 Analytics & Reporting
- **Property Statistics**: Total properties, types, status breakdown
- **CRM Analytics**: Lead conversion rates, customer metrics
- **Monthly Reports**: Performance tracking over time

## Installation

### Prerequisites
- Node.js 16+ 
- Access to Nibret API
- Telegram Bot Token (from @BotFather)

### Setup Steps

1. **Clone and Install**
   ```bash
   cd nibret-telegram-bot
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   # Telegram Bot Configuration
   TELEGRAM_BOT_TOKEN=your_bot_token_here
   WEBHOOK_URL=https://your-domain.com/webhook
   WEBHOOK_PORT=3001

   # Nibret API Configuration
   NIBRET_API_URL=https://api.nibret.com
   NIBRET_API_USERNAME=0965789832
   NIBRET_API_PASSWORD=nibretadmin

   # Admin Configuration
   ADMIN_TELEGRAM_IDS=123456789,987654321
   SUPER_ADMIN_TELEGRAM_ID=123456789
   ```

3. **Get Your Telegram ID**
   - Message @userinfobot on Telegram to get your user ID
   - Add your ID to `ADMIN_TELEGRAM_IDS` in `.env`

4. **Start the Bot**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## Usage

### Getting Started
1. Start a chat with your bot on Telegram
2. Send `/start` to see the welcome message
3. Use `/menu` to access all features (no login required for admins!)

### Commands

#### Public Commands
- `/start` - Welcome message and bot introduction
- `/help` - Show help and available commands

#### Admin Commands
- `/menu` - Show main menu with all options (auto-authenticated for admins)
- `/status` - Check your access status
- `/login` - Show welcome message (optional, admins are auto-authenticated)
- `/logout` - Clear session (optional, admins maintain access)

#### Property Commands
- `/properties` - View all properties
- `/upload` - Start property upload wizard
- `/search <query>` - Search properties

#### CRM Commands
- `/leads` - View and manage leads
- `/customers` - View customer list
- `/stats` - View analytics and statistics

### Property Upload Process

The bot provides an interactive wizard for uploading properties:

1. **Title**: Enter property title
2. **Description**: Detailed property description
3. **Price**: Property price in ETB
4. **Bedrooms**: Number of bedrooms
5. **Bathrooms**: Number of bathrooms
6. **Area**: Total area in square meters
7. **Address**: Full property address
8. **Type**: Property type (house, apartment, etc.)
9. **Images**: Upload property photos (optional)

### CRM Features

#### Lead Management
- View leads by status (new, contacted, qualified, etc.)
- Update lead status with one click
- Schedule follow-up activities
- Track lead conversion progress

#### Customer Management
- View all customers with contact information
- Track customer preferences and status
- Convert customers to leads
- Monitor customer activity

## API Integration

The bot integrates with the existing Nibret API:

- **Authentication**: Uses admin credentials for API access
- **Property Management**: Full CRUD operations
- **CRM Operations**: Lead and customer management
- **File Uploads**: Image upload support
- **Analytics**: Statistics and reporting

## Architecture

```
nibret-telegram-bot/
├── src/
│   ├── bot.js              # Main bot application
│   ├── config/
│   │   └── config.js       # Configuration management
│   ├── commands/
│   │   └── index.js        # Command handlers
│   ├── handlers/
│   │   ├── propertyUpload.js # Property upload wizard
│   │   └── crm.js          # CRM functionality
│   └── utils/
│       ├── api.js          # Nibret API client
│       ├── auth.js         # Authentication manager
│       └── helpers.js      # Utility functions
├── package.json
├── .env.example
└── README.md
```

## Security Features

- **Role-Based Access**: Only configured admins can use the bot
- **Session Management**: Automatic session expiration
- **API Security**: Secure API integration with credentials
- **Input Validation**: All user inputs are validated
- **Error Handling**: Comprehensive error handling and logging

## Deployment

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Using PM2 (Recommended for production)
```bash
npm install -g pm2
pm2 start src/bot.js --name "nibret-bot"
pm2 save
pm2 startup
```

### Docker (Optional)
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
CMD ["npm", "start"]
```

## Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check if `TELEGRAM_BOT_TOKEN` is correct
   - Verify bot is running without errors
   - Check network connectivity

2. **Authentication failed**
   - Verify `NIBRET_API_URL` is accessible
   - Check API credentials in `.env`
   - Ensure API server is running

3. **Access denied**
   - Verify your Telegram ID is in `ADMIN_TELEGRAM_IDS`
   - Check if you've run `/login` command
   - Verify session hasn't expired

4. **Property upload fails**
   - Check API connectivity
   - Verify all required fields are provided
   - Check image size limits

### Logs
The bot provides detailed logging for debugging:
- API requests and responses
- Authentication events
- Error messages with context
- User activity tracking

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For support and questions:
- Check the troubleshooting section
- Review bot logs for error details
- Contact the development team

## License

MIT License - see LICENSE file for details.
