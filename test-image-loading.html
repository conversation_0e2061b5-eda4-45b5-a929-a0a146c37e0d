<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Loading</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-image {
            max-width: 300px;
            max-height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Image Loading Test</h1>
        <p>This page tests if images can be loaded from the backend server.</p>
        
        <div id="status" class="status info">
            <strong>Status:</strong> Ready to test
        </div>

        <div>
            <button class="btn" onclick="testImageLoading()">🧪 Test Image Loading</button>
            <button class="btn" onclick="testCORS()">🌐 Test CORS</button>
            <button class="btn" onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div id="imageContainer"></div>

        <div class="debug-info" id="debugInfo">
            <strong>Debug Information:</strong><br>
            Backend URL: http://localhost:3000<br>
            Frontend URL: http://localhost:5173<br>
            Test Image: property-1748524945687-3aa7aophyi9.png<br>
            <br>
            <strong>Console Output:</strong><br>
            <span id="consoleOutput">No tests run yet...</span>
        </div>
    </div>

    <script>
        let consoleOutput = [];

        // Override console methods to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleOutput.push('LOG: ' + args.join(' '));
            updateConsoleOutput();
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            consoleOutput.push('ERROR: ' + args.join(' '));
            updateConsoleOutput();
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            consoleOutput.push('WARN: ' + args.join(' '));
            updateConsoleOutput();
        };

        function updateConsoleOutput() {
            document.getElementById('consoleOutput').textContent = consoleOutput.slice(-10).join('\n');
        }

        function updateStatus(type, message) {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.innerHTML = `<strong>Status:</strong> ${message}`;
        }

        async function testCORS() {
            updateStatus('info', 'Testing CORS...');
            console.log('🌐 Testing CORS headers...');

            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();
                
                console.log('✅ CORS test successful');
                console.log('Response:', data);
                updateStatus('success', 'CORS is working correctly');
            } catch (error) {
                console.error('❌ CORS test failed:', error);
                updateStatus('error', 'CORS test failed: ' + error.message);
            }
        }

        function testImageLoading() {
            updateStatus('info', 'Testing image loading...');
            console.log('🖼️ Testing image loading...');

            const imageContainer = document.getElementById('imageContainer');
            imageContainer.innerHTML = '';

            // Test multiple images
            const testImages = [
                'property-1748524945687-3aa7aophyi9.png',
                'property-1748524597132-uwehkoy1ayn.png',
                'property-1748523877117-e97959jq1fl.png'
            ];

            testImages.forEach((imageName, index) => {
                const imageUrl = `http://localhost:3000/uploads/${imageName}`;
                console.log(`Testing image ${index + 1}: ${imageUrl}`);

                const img = document.createElement('img');
                img.className = 'test-image';
                img.alt = `Test image ${index + 1}`;
                
                img.onload = function() {
                    console.log(`✅ Image ${index + 1} loaded successfully: ${imageUrl}`);
                    if (index === 0) {
                        updateStatus('success', 'Images are loading correctly!');
                    }
                };

                img.onerror = function(e) {
                    console.error(`❌ Image ${index + 1} failed to load: ${imageUrl}`);
                    console.error('Error event:', e);
                    if (index === 0) {
                        updateStatus('error', 'Image loading failed - check console for details');
                    }
                };

                img.src = imageUrl;
                imageContainer.appendChild(img);

                // Add image info
                const info = document.createElement('p');
                info.textContent = `Image ${index + 1}: ${imageName}`;
                imageContainer.appendChild(info);
            });
        }

        function clearResults() {
            document.getElementById('imageContainer').innerHTML = '';
            consoleOutput = [];
            updateConsoleOutput();
            updateStatus('info', 'Results cleared');
        }

        // Auto-run tests on page load
        window.addEventListener('load', function() {
            console.log('🚀 Page loaded, running automatic tests...');
            setTimeout(() => {
                testCORS();
                setTimeout(() => {
                    testImageLoading();
                }, 2000);
            }, 1000);
        });
    </script>
</body>
</html>
