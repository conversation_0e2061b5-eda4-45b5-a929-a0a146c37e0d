// Simple test to verify dashboard APIs are working
async function testDashboard() {
  console.log('🧪 Final Dashboard Test...\n');

  try {
    // Login
    const loginResponse = await fetch('http://localhost:3000/accounts/login/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: '**********', password: 'nibretadmin' })
    });
    const loginData = await loginResponse.json();
    const token = loginData.access_token;
    console.log('✅ Login successful');

    // Test Properties
    const propertiesResponse = await fetch('http://localhost:3000/properties/list', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: '**********', password: 'nibretadmin' })
    });
    const propertiesData = await propertiesResponse.json();
    console.log(`✅ Properties: ${propertiesData.length} found`);

    // Test Users
    const usersResponse = await fetch('http://localhost:3000/accounts/users', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const usersData = await usersResponse.json();
    console.log(`✅ Users: ${usersData.users.length} found`);

    // Test Leads
    const leadsResponse = await fetch('http://localhost:3000/leads', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const leadsData = await leadsResponse.json();
    console.log(`✅ Leads: ${leadsData.data.length} found`);

    // Test Lead Stats
    const leadStatsResponse = await fetch('http://localhost:3000/leads/stats', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const leadStatsData = await leadStatsResponse.json();
    console.log(`✅ Lead Stats: ${JSON.stringify(leadStatsData.data.stats)}`);

    // Test Property Stats
    const propertyStatsResponse = await fetch('http://localhost:3000/properties/stats/breakdown', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const propertyStatsData = await propertyStatsResponse.json();
    console.log(`✅ Property Stats: ${JSON.stringify(propertyStatsData.data)}`);

    console.log('\n🎉 All Dashboard APIs Working!');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testDashboard();
