// Test CRM APIs specifically
async function testCRMAPIs() {
  console.log('🧪 Testing CRM APIs...\n');

  try {
    // 1. Login
    console.log('1️⃣ Testing Login...');
    const loginRes = await fetch('http://localhost:3000/accounts/login/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: '**********', password: 'nibretadmin' })
    });
    
    if (!loginRes.ok) {
      const errorText = await loginRes.text();
      throw new Error(`Login failed: ${loginRes.status} - ${errorText}`);
    }
    
    const loginData = await loginRes.json();
    console.log('✅ Login successful');
    const token = loginData.access_token;

    // 2. Test Leads API
    console.log('\n2️⃣ Testing Leads API...');
    const leadsRes = await fetch('http://localhost:3000/leads?page=1&limit=20&sort_by=created_at&sort_order=desc', {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!leadsRes.ok) {
      const errorText = await leadsRes.text();
      throw new Error(`Leads API failed: ${leadsRes.status} - ${errorText}`);
    }
    
    const leadsData = await leadsRes.json();
    console.log(`✅ Leads API successful: ${leadsData.data.length} leads found`);
    console.log('📄 Pagination:', leadsData.pagination);
    
    if (leadsData.data.length > 0) {
      const firstLead = leadsData.data[0];
      console.log('👤 Sample lead:', {
        id: firstLead._id,
        name: `${firstLead.first_name} ${firstLead.last_name}`,
        email: firstLead.email,
        status: firstLead.status,
        source: firstLead.source
      });
    }

    // 3. Test Lead Stats API
    console.log('\n3️⃣ Testing Lead Stats API...');
    const statsRes = await fetch('http://localhost:3000/leads/stats', {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!statsRes.ok) {
      const errorText = await statsRes.text();
      throw new Error(`Lead Stats API failed: ${statsRes.status} - ${errorText}`);
    }
    
    const statsData = await statsRes.json();
    console.log('✅ Lead Stats API successful');
    console.log('📊 Stats:', statsData.data.stats);

    // 4. Test Users API (for customers)
    console.log('\n4️⃣ Testing Users API...');
    const usersRes = await fetch('http://localhost:3000/accounts/users', {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!usersRes.ok) {
      const errorText = await usersRes.text();
      throw new Error(`Users API failed: ${usersRes.status} - ${errorText}`);
    }
    
    const usersData = await usersRes.json();
    const customers = usersData.users.filter(user => user.role === 'CUSTOMER');
    console.log(`✅ Users API successful: ${customers.length} customers found`);

    // 5. Test Lead Status Update (if we have leads)
    if (leadsData.data.length > 0) {
      console.log('\n5️⃣ Testing Lead Status Update...');
      const leadId = leadsData.data[0]._id;
      const currentStatus = leadsData.data[0].status;
      const newStatus = currentStatus === 'new' ? 'contacted' : 'new';
      
      const updateRes = await fetch(`http://localhost:3000/leads/${leadId}/status`, {
        method: 'PATCH',
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          status: newStatus, 
          notes: 'Status updated via API test' 
        })
      });
      
      if (!updateRes.ok) {
        const errorText = await updateRes.text();
        console.log(`⚠️ Lead Status Update failed: ${updateRes.status} - ${errorText}`);
      } else {
        const updateData = await updateRes.json();
        console.log(`✅ Lead Status Update successful: ${currentStatus} → ${newStatus}`);
      }
    }

    console.log('\n🎉 CRM API Test COMPLETED!');
    
    console.log('\n📊 Summary:');
    console.log(`• Leads: ${leadsData.data.length}`);
    console.log(`• Customers: ${usersData.users.filter(u => u.role === 'CUSTOMER').length}`);
    console.log(`• Lead Stats: Total=${statsData.data.stats.total}, New=${statsData.data.stats.new}`);

  } catch (error) {
    console.error('❌ CRM Test failed:', error.message);
    console.error('🔍 Full error:', error);
  }
}

testCRMAPIs();
