<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        input[type="file"] {
            display: none;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .image-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .image-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255,0,0,0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 12px;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Image Upload Test</h1>
        <p>This page tests the image upload functionality with the local storage fallback system.</p>
        
        <div id="status" class="status info">
            <strong>Status:</strong> Ready to test upload
        </div>

        <div class="upload-area" id="uploadArea">
            <h3>📁 Upload Images</h3>
            <p>Drag and drop images here, or click to select files</p>
            <input type="file" id="fileInput" multiple accept="image/*">
            <button class="btn" onclick="document.getElementById('fileInput').click()">
                Select Images
            </button>
        </div>

        <div class="image-preview" id="imagePreview"></div>

        <div class="debug-info" id="debugInfo">
            <strong>Debug Information:</strong><br>
            API Base URL: <span id="apiUrl">http://localhost:3000</span><br>
            Upload Endpoint: <span id="uploadEndpoint">/upload/image</span><br>
            Authentication: <span id="authStatus">Checking...</span><br>
            Server Status: <span id="serverStatus">Checking...</span>
        </div>

        <button class="btn" onclick="testLogin()">🔑 Test Login</button>
        <button class="btn" onclick="testServerStatus()">🌐 Test Server</button>
        <button class="btn" onclick="clearImages()">🗑️ Clear Images</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        let authToken = localStorage.getItem('token');
        let uploadedImages = [];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            testServerStatus();
            checkAuthStatus();
        });

        function setupEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFiles(files);
            });

            // File input
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });

            // Click to upload
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });
        }

        async function testServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('serverStatus').textContent = '✅ Online';
                    updateStatus('success', `Server is running: ${data.message}`);
                } else {
                    document.getElementById('serverStatus').textContent = '❌ Error';
                    updateStatus('error', 'Server responded with error');
                }
            } catch (error) {
                document.getElementById('serverStatus').textContent = '❌ Offline';
                updateStatus('error', `Server is not accessible: ${error.message}`);
            }
        }

        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/accounts/login/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: '**********',
                        password: 'nibretadmin'
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.access_token) {
                    authToken = data.access_token;
                    localStorage.setItem('token', authToken);
                    document.getElementById('authStatus').textContent = '✅ Authenticated';
                    updateStatus('success', 'Login successful! You can now upload images.');
                } else {
                    document.getElementById('authStatus').textContent = '❌ Failed';
                    updateStatus('error', 'Login failed. Check credentials.');
                }
            } catch (error) {
                document.getElementById('authStatus').textContent = '❌ Error';
                updateStatus('error', `Login error: ${error.message}`);
            }
        }

        function checkAuthStatus() {
            if (authToken) {
                document.getElementById('authStatus').textContent = '✅ Token Found';
            } else {
                document.getElementById('authStatus').textContent = '❌ No Token';
                updateStatus('info', 'Please login first to upload images.');
            }
        }

        async function handleFiles(files) {
            if (!authToken) {
                updateStatus('error', 'Please login first!');
                return;
            }

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                await uploadFile(file);
            }
        }

        async function uploadFile(file) {
            const imageId = Date.now() + Math.random();
            
            // Add preview
            addImagePreview(imageId, URL.createObjectURL(file), true);
            
            try {
                const formData = new FormData();
                formData.append('image', file);

                updateStatus('info', `Uploading ${file.name}...`);

                const response = await fetch(`${API_BASE}/upload/image`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                });

                if (!response.ok) {
                    throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
                }

                const result = await response.json();
                
                if (result.success && result.data) {
                    // Update preview with uploaded image
                    updateImagePreview(imageId, result.data.secure_url, false);
                    uploadedImages.push(result.data);
                    
                    updateStatus('success', `✅ ${file.name} uploaded successfully!`);
                    updateDebugInfo(result.data);
                } else {
                    throw new Error('Invalid response format');
                }

            } catch (error) {
                removeImagePreview(imageId);
                updateStatus('error', `❌ Upload failed: ${error.message}`);
                console.error('Upload error:', error);
            }
        }

        function addImagePreview(id, url, uploading = false) {
            const preview = document.getElementById('imagePreview');
            const div = document.createElement('div');
            div.className = 'image-item';
            div.id = `image-${id}`;
            
            div.innerHTML = `
                <img src="${url}" alt="Preview">
                ${uploading ? '<div class="progress"><div class="progress-bar" style="width: 50%"></div></div>' : ''}
                <button class="remove-btn" onclick="removeImagePreview('${id}')">&times;</button>
            `;
            
            preview.appendChild(div);
        }

        function updateImagePreview(id, url, uploading = false) {
            const element = document.getElementById(`image-${id}`);
            if (element) {
                element.innerHTML = `
                    <img src="${url}" alt="Uploaded">
                    <button class="remove-btn" onclick="removeImagePreview('${id}')">&times;</button>
                `;
            }
        }

        function removeImagePreview(id) {
            const element = document.getElementById(`image-${id}`);
            if (element) {
                element.remove();
            }
        }

        function clearImages() {
            document.getElementById('imagePreview').innerHTML = '';
            uploadedImages = [];
            updateStatus('info', 'All images cleared.');
        }

        function updateStatus(type, message) {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.innerHTML = `<strong>Status:</strong> ${message}`;
        }

        function updateDebugInfo(data) {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <strong>Debug Information:</strong><br>
                API Base URL: <span id="apiUrl">${API_BASE}</span><br>
                Upload Endpoint: <span id="uploadEndpoint">/upload/image</span><br>
                Authentication: <span id="authStatus">✅ Authenticated</span><br>
                Server Status: <span id="serverStatus">✅ Online</span><br>
                <br>
                <strong>Last Upload Response:</strong><br>
                URL: ${data.secure_url}<br>
                Public ID: ${data.public_id}<br>
                Format: ${data.format}<br>
                Size: ${data.bytes} bytes<br>
                Storage: Local (nibret-api/uploads/)
            `;
        }
    </script>
</body>
</html>
