// Test what fields the backend actually accepts

const testProperty = {
  username: '0965789832',
  password: 'nibretadmin',
  title: 'Test Property',
  description: 'Test description',
  price: 1000000,
  currency: 'ETB',
  beds: 2,
  baths: 1,
  sqft: 100,
  yearBuilt: 2020,
  address: 'Test Address',
  propertyType: 'house',
  status: 'for_sale',
  listing_type: 'sale',
  images: [],
  features: []
};

async function testBackendSchema() {
  try {
    console.log('🧪 Testing backend schema with minimal property...');
    
    const response = await fetch('http://localhost:3000/properties', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testProperty),
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Success! Property created:', result);
    } else {
      console.log('❌ Error:', result);
    }
    
  } catch (error) {
    console.error('💥 Request failed:', error.message);
  }
}

testBackendSchema();
