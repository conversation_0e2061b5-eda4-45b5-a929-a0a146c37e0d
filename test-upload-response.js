const fs = require('fs');

async function testUploadResponse() {
  console.log('🧪 Testing Upload Response Format...\n');

  try {
    // Step 1: Login to get token
    console.log('1️⃣ Logging in...');
    const loginResponse = await fetch('http://localhost:3000/accounts/login/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: '**********',
        password: 'nibretadmin'
      })
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }

    const loginData = await loginResponse.json();
    const token = loginData.access_token;
    console.log('✅ Login successful');

    // Step 2: Create a test image file
    console.log('\n2️⃣ Creating test image...');
    const testImagePath = 'test-response-image.png';
    
    // Create a simple 1x1 PNG image (base64 encoded)
    const pngData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77yQAAAABJRU5ErkJggg==', 'base64');
    fs.writeFileSync(testImagePath, pngData);
    console.log('✅ Test image created');

    // Step 3: Upload the image
    console.log('\n3️⃣ Uploading image...');
    const formData = new FormData();
    const imageBlob = new Blob([pngData], { type: 'image/png' });
    formData.append('image', imageBlob, 'test-response-image.png');

    const uploadResponse = await fetch('http://localhost:3000/upload/image', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!uploadResponse.ok) {
      throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
    }

    const uploadData = await uploadResponse.json();
    console.log('✅ Upload successful');
    
    // Step 4: Analyze the response
    console.log('\n4️⃣ Analyzing Upload Response...');
    console.log('📄 Full Response:');
    console.log(JSON.stringify(uploadData, null, 2));
    
    console.log('\n🔍 Key Fields:');
    console.log('• success:', uploadData.success);
    console.log('• data.public_id:', uploadData.data?.public_id);
    console.log('• data.secure_url:', uploadData.data?.secure_url);
    console.log('• data.format:', uploadData.data?.format);
    console.log('• data.bytes:', uploadData.data?.bytes);
    
    if (uploadData.data?.urls) {
      console.log('\n🖼️ Available URLs:');
      Object.entries(uploadData.data.urls).forEach(([key, url]) => {
        console.log(`• ${key}: ${url}`);
      });
    }

    // Step 5: Test image access
    console.log('\n5️⃣ Testing Image Access...');
    const imageUrl = uploadData.data.secure_url;
    
    const imageResponse = await fetch(imageUrl);
    console.log(`📊 Image Response: ${imageResponse.status} ${imageResponse.statusText}`);
    console.log(`📏 Content-Length: ${imageResponse.headers.get('content-length')} bytes`);
    console.log(`🗂️ Content-Type: ${imageResponse.headers.get('content-type')}`);
    
    if (imageResponse.ok) {
      console.log('✅ Image is accessible');
    } else {
      console.log('❌ Image is not accessible');
    }

    // Step 6: Frontend compatibility check
    console.log('\n6️⃣ Frontend Compatibility Check...');
    console.log('Expected by ImageUpload component:');
    console.log('• result.data.secure_url ✅ Available:', !!uploadData.data?.secure_url);
    console.log('• result.data.public_id ✅ Available:', !!uploadData.data?.public_id);
    
    // Cleanup
    fs.unlinkSync(testImagePath);
    console.log('\n🧹 Cleanup completed');

    console.log('\n🎉 Upload Response Test COMPLETED!');
    
    return uploadData;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('🔍 Error details:', error);
  }
}

// Run the test
testUploadResponse();
