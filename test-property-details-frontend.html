<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Property Details API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .property-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .property-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .property-price {
            font-size: 16px;
            color: #2563eb;
            font-weight: bold;
        }
        .property-details {
            margin: 10px 0;
            color: #666;
        }
        .error {
            color: #dc2626;
            background: #fef2f2;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #fecaca;
        }
        .success {
            color: #059669;
            background: #f0fdf4;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #bbf7d0;
        }
        .loading {
            color: #7c3aed;
            font-style: italic;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        .image-gallery {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        .image-gallery img {
            width: 150px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 Property Details API Test</h1>
        
        <div>
            <h3>Test Property Details Endpoint</h3>
            <button onclick="testPropertyDetails('684698c27a749cbf92bf8119')">
                Test Platinum Plaza Property
            </button>
            <button onclick="testPropertyDetails('683ebd66857bc953e9d088b5')">
                Test Villa in Megenagna
            </button>
            <button onclick="testPropertyDetails('invalid-id')">
                Test Invalid ID
            </button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://nibret-api.onrender.com';
        
        async function testPropertyDetails(propertyId) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading">🔄 Loading property details...</div>';
            
            try {
                console.log(`Testing property ID: ${propertyId}`);
                
                const response = await fetch(`${API_BASE_URL}/properties/${propertyId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Property data:', data);
                
                if (data.success && data.data) {
                    displayProperty(data.data);
                } else {
                    throw new Error('Invalid response format');
                }
                
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error fetching property</h4>
                        <p><strong>Property ID:</strong> ${propertyId}</p>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p><strong>URL:</strong> ${API_BASE_URL}/properties/${propertyId}</p>
                    </div>
                `;
            }
        }
        
        function displayProperty(property) {
            const resultsDiv = document.getElementById('results');
            
            const formatPrice = (price, currency) => {
                if (currency === 'USD') {
                    return new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        maximumFractionDigits: 0
                    }).format(price);
                } else {
                    return new Intl.NumberFormat('en-ET', {
                        style: 'currency',
                        currency: 'ETB',
                        maximumFractionDigits: 0
                    }).format(price);
                }
            };
            
            const imagesHtml = property.images && property.images.length > 0 
                ? `<div class="image-gallery">
                     ${property.images.map(img => `<img src="${img}" alt="${property.title}" onerror="this.style.display='none'">`).join('')}
                   </div>`
                : '<p>No images available</p>';
            
            resultsDiv.innerHTML = `
                <div class="success">
                    <h4>✅ Property Details Retrieved Successfully</h4>
                </div>
                
                <div class="property-card">
                    <div class="property-title">${property.title}</div>
                    <div class="property-price">${formatPrice(property.price, property.currency)}</div>
                    
                    <div class="property-details">
                        <p><strong>ID:</strong> ${property.id}</p>
                        <p><strong>Type:</strong> ${property.propertyType}</p>
                        <p><strong>Status:</strong> ${property.status}</p>
                        <p><strong>Address:</strong> ${property.address}</p>
                        <p><strong>Beds:</strong> ${property.beds} | <strong>Baths:</strong> ${property.baths} | <strong>Sqft:</strong> ${property.sqft}</p>
                        <p><strong>Year Built:</strong> ${property.yearBuilt || 'N/A'}</p>
                        <p><strong>Views:</strong> ${property.views || 0}</p>
                        <p><strong>Featured:</strong> ${property.is_featured ? 'Yes' : 'No'}</p>
                        <p><strong>Publish Status:</strong> ${property.publish_status}</p>
                        <p><strong>Owner:</strong> ${property.owner?.first_name} ${property.owner?.last_name}</p>
                        <p><strong>Contact:</strong> ${property.owner?.phone} | ${property.owner?.email}</p>
                    </div>
                    
                    ${property.description ? `<p><strong>Description:</strong> ${property.description}</p>` : ''}
                    
                    ${imagesHtml}
                    
                    <div style="margin-top: 15px;">
                        <strong>Frontend URL:</strong> 
                        <a href="http://localhost:5173/property/${property.id}" target="_blank">
                            http://localhost:5173/property/${property.id}
                        </a>
                    </div>
                </div>
            `;
        }
        
        // Test on page load
        window.onload = function() {
            console.log('🚀 Property Details API Test Page Loaded');
            console.log('API Base URL:', API_BASE_URL);
        };
    </script>
</body>
</html>
