// Simple dashboard test
async function testDashboard() {
  console.log('🧪 Dashboard API Test\n');

  try {
    // 1. Login
    console.log('1️⃣ Testing Login...');
    const loginRes = await fetch('http://localhost:3000/accounts/login/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: '**********', password: 'nibretadmin' })
    });
    
    if (!loginRes.ok) throw new Error(`Login failed: ${loginRes.status}`);
    const loginData = await loginRes.json();
    console.log('✅ Login successful');

    const token = loginData.access_token;

    // 2. Properties
    console.log('\n2️⃣ Testing Properties...');
    const propRes = await fetch('http://localhost:3000/properties/list', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: '**********', password: 'nibretadmin' })
    });
    
    if (!propRes.ok) throw new Error(`Properties failed: ${propRes.status}`);
    const properties = await propRes.json();
    console.log(`✅ Properties: ${properties.length} found`);

    // 3. Users
    console.log('\n3️⃣ Testing Users...');
    const usersRes = await fetch('http://localhost:3000/accounts/users', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (!usersRes.ok) throw new Error(`Users failed: ${usersRes.status}`);
    const usersData = await usersRes.json();
    console.log(`✅ Users: ${usersData.users.length} found`);

    // 4. Leads
    console.log('\n4️⃣ Testing Leads...');
    const leadsRes = await fetch('http://localhost:3000/leads', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (!leadsRes.ok) throw new Error(`Leads failed: ${leadsRes.status}`);
    const leadsData = await leadsRes.json();
    console.log(`✅ Leads: ${leadsData.data.length} found`);

    // 5. Lead Stats
    console.log('\n5️⃣ Testing Lead Stats...');
    const statsRes = await fetch('http://localhost:3000/leads/stats', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (!statsRes.ok) throw new Error(`Lead Stats failed: ${statsRes.status}`);
    const statsData = await statsRes.json();
    console.log(`✅ Lead Stats: Total=${statsData.data.stats.total}, New=${statsData.data.stats.new}`);

    // 6. Property Stats
    console.log('\n6️⃣ Testing Property Stats...');
    const propStatsRes = await fetch('http://localhost:3000/properties/stats/breakdown', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (!propStatsRes.ok) throw new Error(`Property Stats failed: ${propStatsRes.status}`);
    const propStatsData = await propStatsRes.json();
    console.log(`✅ Property Stats: ${propStatsData.data.statusBreakdown.length} status categories`);

    console.log('\n🎉 All Dashboard APIs Working!');
    console.log('\n📊 Summary:');
    console.log(`• Properties: ${properties.length}`);
    console.log(`• Users: ${usersData.users.length}`);
    console.log(`• Leads: ${leadsData.data.length}`);
    console.log(`• Lead Stats: Total=${statsData.data.stats.total}, New=${statsData.data.stats.new}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testDashboard();
