<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exchange Rate API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .api-test { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 10px 0; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Exchange Rate API Test</h1>
    <button onclick="testAllAPIs()">Test All APIs</button>
    
    <div id="results"></div>

    <script>
        const APIs = [
            {
                name: 'ExchangeRate-API',
                url: 'https://api.exchangerate-api.com/v4/latest/USD',
                parser: (data) => ({
                    rate: data.rates?.ETB,
                    lastUpdated: data.time_last_updated ? new Date(data.time_last_updated * 1000).toISOString() : new Date().toISOString(),
                    source: 'ExchangeRate-API'
                })
            },
            {
                name: 'Exchange Rates API',
                url: 'https://api.exchangerate.host/latest?base=USD&symbols=ETB',
                parser: (data) => ({
                    rate: data.rates?.ETB,
                    lastUpdated: data.date ? new Date(data.date).toISOString() : new Date().toISOString(),
                    source: 'Exchange Rates API'
                })
            },
            {
                name: 'Fixer.io',
                url: 'http://data.fixer.io/api/latest?access_key=********************************&base=USD&symbols=ETB',
                parser: (data) => ({
                    rate: data.rates?.ETB,
                    lastUpdated: data.date ? new Date(data.date).toISOString() : new Date().toISOString(),
                    source: 'Fixer.io'
                })
            }
        ];

        async function testAPI(api) {
            const resultDiv = document.getElementById(`result-${api.name.replace(/\s+/g, '-')}`);
            if (!resultDiv) return;
            
            resultDiv.className = 'api-test loading';
            resultDiv.innerHTML = `<h3>${api.name}</h3><p>Testing...</p>`;
            
            try {
                const response = await fetch(api.url);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const parsed = api.parser(data);
                
                if (!parsed.rate || parsed.rate <= 0) {
                    throw new Error('Invalid rate received');
                }
                
                resultDiv.className = 'api-test success';
                resultDiv.innerHTML = `
                    <h3>${api.name} ✅</h3>
                    <p><strong>Rate:</strong> ${parsed.rate} ETB/USD</p>
                    <p><strong>Last Updated:</strong> ${parsed.lastUpdated}</p>
                    <p><strong>Source:</strong> ${parsed.source}</p>
                    <details>
                        <summary>Raw Response</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
            } catch (error) {
                resultDiv.className = 'api-test error';
                resultDiv.innerHTML = `
                    <h3>${api.name} ❌</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }

        async function testAllAPIs() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            // Create result divs for each API
            APIs.forEach(api => {
                const div = document.createElement('div');
                div.id = `result-${api.name.replace(/\s+/g, '-')}`;
                div.className = 'api-test loading';
                div.innerHTML = `<h3>${api.name}</h3><p>Preparing...</p>`;
                resultsDiv.appendChild(div);
            });
            
            // Test all APIs concurrently
            const promises = APIs.map(api => testAPI(api));
            await Promise.allSettled(promises);
        }
    </script>
</body>
</html>
