#!/bin/bash

# Build script for production deployment
echo "🚀 Building Nibret Frontend for Production..."

# Check if .env.production exists
if [ ! -f .env.production ]; then
    echo "❌ .env.production file not found!"
    echo "Please create .env.production with your production environment variables."
    exit 1
fi

# Copy production environment file
echo "📋 Using production environment variables..."
cp .env.production .env.local

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the application
echo "🔨 Building application..."
npm run build

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully!"
    echo "📁 Build files are in the 'dist' directory"
    echo ""
    echo "🌐 To deploy:"
    echo "1. Upload the 'dist' folder to your web server"
    echo "2. Configure your web server to serve the index.html file"
    echo "3. Set up proper redirects for SPA routing"
    echo ""
    echo "📊 Build size:"
    du -sh dist/
else
    echo "❌ Build failed!"
    exit 1
fi
