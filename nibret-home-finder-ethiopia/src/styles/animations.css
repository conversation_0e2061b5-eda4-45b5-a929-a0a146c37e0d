/* Search and Filter Highlighting Animations */
@keyframes subtle-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(255, 215, 0, 0.1);
  }
}

@keyframes search-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(59, 130, 246, 0.4);
  }
}

@keyframes filter-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6), 0 0 30px rgba(255, 215, 0, 0.4);
  }
}

.animate-pulse-subtle {
  animation: subtle-pulse 3s ease-in-out infinite;
}

.search-highlight {
  animation: search-glow 4s ease-in-out infinite;
  animation-delay: 1s;
}

.filter-highlight {
  animation: filter-glow 4s ease-in-out infinite;
  animation-delay: 2.5s;
}

/* Hover effects to pause animations */
.search-highlight:hover,
.filter-highlight:hover,
.animate-pulse-subtle:hover {
  animation-play-state: paused;
}

/* Focus effects */
.search-highlight:focus {
  animation: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}
