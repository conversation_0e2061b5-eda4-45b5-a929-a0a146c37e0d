
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { currencyAPI } from '../lib/api/currency';

// Types
type Language = 'en' | 'am';
type Currency = 'USD' | 'ETB';

interface AppContextProps {
  language: Language;
  currency: Currency;
  setLanguage: (language: Language) => void;
  setCurrency: (currency: Currency) => void;
  convertCurrency: (amount: number, fromCurrency?: Currency) => number;
  formatCurrency: (amount: number, targetCurrency?: Currency) => string;
  translate: (english: string, amharic: string) => string;
  exchangeRate: number;
}

interface AppProviderProps {
  children: ReactNode;
}

// Fallback exchange rate: 1 USD = 57 ETB (conservative fallback - hidden from UI)
export const FALLBACK_ETB_EXCHANGE_RATE = 57;

// Create context with default values
const AppContext = createContext<AppContextProps>({
  language: 'en',
  currency: 'USD',
  setLanguage: () => {},
  setCurrency: () => {},
  convertCurrency: (amount) => amount,
  formatCurrency: (amount) => `$${amount}`,
  translate: (english) => english,
  exchangeRate: FALLBACK_ETB_EXCHANGE_RATE,
});

// Provider component
export const AppProvider = ({ children }: AppProviderProps) => {
  const [language, setLanguage] = useState<Language>('en');
  const [currency, setCurrency] = useState<Currency>('USD');
  const [exchangeRate, setExchangeRate] = useState<number>(FALLBACK_ETB_EXCHANGE_RATE);

  // Load exchange rate on mount and when currency changes
  useEffect(() => {
    const loadExchangeRate = async () => {
      try {
        const rate = await currencyAPI.getExchangeRate();
        setExchangeRate(rate.rate);
      } catch (error) {
        console.warn('Failed to load exchange rate, using fallback:', error);
        setExchangeRate(FALLBACK_ETB_EXCHANGE_RATE);
      }
    };

    loadExchangeRate();
  }, []);

  const convertCurrency = (amount: number, fromCurrency?: Currency): number => {
    const sourceCurrency = fromCurrency || 'USD'; // Default source currency

    // If target currency is same as source, no conversion needed
    if (currency === sourceCurrency) {
      return amount;
    }

    // Convert USD to ETB
    if (sourceCurrency === 'USD' && currency === 'ETB') {
      return amount * exchangeRate;
    }

    // Convert ETB to USD
    if (sourceCurrency === 'ETB' && currency === 'USD') {
      return amount / exchangeRate;
    }

    return amount;
  };

  const formatCurrency = (amount: number, targetCurrency?: Currency): string => {
    const currencyToUse = targetCurrency || currency;

    if (currencyToUse === 'USD') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    } else {
      return new Intl.NumberFormat('en-ET', {
        style: 'currency',
        currency: 'ETB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    }
  };

  const translate = (english: string, amharic: string): string => {
    return language === 'en' ? english : amharic;
  };

  return (
    <AppContext.Provider value={{
      language,
      currency,
      setLanguage,
      setCurrency,
      convertCurrency,
      formatCurrency,
      translate,
      exchangeRate
    }}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook for using the context
export const useAppContext = () => useContext(AppContext);
