import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAppContext } from '@/context/AppContext';
import { useToast } from '@/hooks/use-toast';
import { Property, propertyApi } from '@/lib/api/properties';
import { Tags, Save, X } from 'lucide-react';
import AdminPropertyTags from './AdminPropertyTags';

interface AdminPropertyTagManagerProps {
  property: Property;
  onUpdate?: (updatedProperty: Property) => void;
  onClose?: () => void;
}

const AVAILABLE_ADMIN_TAGS = [
  'hot_deal',
  'price_reduced',
  'new_listing',
  'open_house',
  'virtual_tour',
  'luxury',
  'investment_opportunity',
  'quick_sale',
  'motivated_seller',
  'under_contract',
  'coming_soon',
  'exclusive',
  'waterfront',
  'corner_lot',
  'renovated',
  'move_in_ready'
];

const AdminPropertyTagManager: React.FC<AdminPropertyTagManagerProps> = ({
  property,
  onUpdate,
  onClose
}) => {
  const { translate } = useAppContext();
  const { toast } = useToast();
  const [selectedTags, setSelectedTags] = useState<string[]>(property.admin_tags || []);
  const [priorityLevel, setPriorityLevel] = useState<string>(property.priority_level || 'normal');
  const [adminNotes, setAdminNotes] = useState<string>(property.admin_notes || '');
  const [isLoading, setIsLoading] = useState(false);

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const getTagLabel = (tag: string) => {
    const labels: { [key: string]: string } = {
      hot_deal: translate('Hot Deal', 'ሙቅ ዲል'),
      price_reduced: translate('Price Reduced', 'ዋጋ ቀንሷል'),
      new_listing: translate('New Listing', 'አዲስ ዝርዝር'),
      open_house: translate('Open House', 'ክፍት ቤት'),
      virtual_tour: translate('Virtual Tour', 'ቨርቹዋል ጉብኝት'),
      luxury: translate('Luxury', 'ቅንጦት'),
      investment_opportunity: translate('Investment Opportunity', 'የኢንቨስትመንት እድል'),
      quick_sale: translate('Quick Sale', 'ፈጣን ሽያጭ'),
      motivated_seller: translate('Motivated Seller', 'ተነሳሽ ሻጭ'),
      under_contract: translate('Under Contract', 'በኮንትራት ስር'),
      coming_soon: translate('Coming Soon', 'በቅርቡ'),
      exclusive: translate('Exclusive', 'ልዩ'),
      waterfront: translate('Waterfront', 'የውሃ ዳርቻ'),
      corner_lot: translate('Corner Lot', 'ጥግ ቦታ'),
      renovated: translate('Renovated', 'ታድሷል'),
      move_in_ready: translate('Move-in Ready', 'ለመግባት ዝግጁ')
    };
    return labels[tag] || tag.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const updateData = {
        admin_tags: selectedTags,
        priority_level: priorityLevel,
        admin_notes: adminNotes.trim() || undefined
      };

      const updatedProperty = await propertyApi.updateProperty(property.id, updateData);
      
      toast({
        title: translate('Success', 'ተሳክቷል'),
        description: translate('Property tags updated successfully', 'የንብረት መለያዎች በተሳካ ሁኔታ ተዘምነዋል'),
      });

      if (onUpdate) {
        onUpdate({ ...property, ...updateData });
      }

      if (onClose) {
        onClose();
      }
    } catch (error: any) {
      console.error('Error updating property tags:', error);
      toast({
        title: translate('Error', 'ስህተት'),
        description: translate('Failed to update property tags', 'የንብረት መለያዎችን ማዘመን አልተሳካም'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Tags className="w-5 h-5 mr-2" />
            {translate('Manage Property Tags', 'የንብረት መለያዎችን አስተዳድር')}
          </CardTitle>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
        <p className="text-sm text-gray-600">
          {property.title}
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Tags Preview */}
        <div>
          <Label className="text-sm font-medium mb-2 block">
            {translate('Current Tags', 'አሁን ያሉ መለያዎች')}
          </Label>
          <AdminPropertyTags
            adminTags={selectedTags}
            priorityLevel={priorityLevel as any}
            size="default"
            showPriority={true}
          />
        </div>

        {/* Priority Level */}
        <div>
          <Label className="text-sm font-medium mb-2 block">
            {translate('Priority Level', 'የቅድሚያ ደረጃ')}
          </Label>
          <Select value={priorityLevel} onValueChange={setPriorityLevel}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">{translate('Low Priority', 'ዝቅተኛ ቅድሚያ')}</SelectItem>
              <SelectItem value="normal">{translate('Normal', 'መደበኛ')}</SelectItem>
              <SelectItem value="high">{translate('High Priority', 'ከፍተኛ ቅድሚያ')}</SelectItem>
              <SelectItem value="urgent">{translate('Urgent', 'አስቸኳይ')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Available Tags */}
        <div>
          <Label className="text-sm font-medium mb-3 block">
            {translate('Available Tags', 'ያሉ መለያዎች')}
          </Label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {AVAILABLE_ADMIN_TAGS.map((tag) => (
              <div key={tag} className="flex items-center space-x-2">
                <Checkbox
                  id={tag}
                  checked={selectedTags.includes(tag)}
                  onCheckedChange={() => handleTagToggle(tag)}
                />
                <Label
                  htmlFor={tag}
                  className="text-sm font-normal cursor-pointer flex-1"
                >
                  {getTagLabel(tag)}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Admin Notes */}
        <div>
          <Label className="text-sm font-medium mb-2 block">
            {translate('Admin Notes', 'የአስተዳዳሪ ማስታወሻዎች')}
          </Label>
          <Textarea
            value={adminNotes}
            onChange={(e) => setAdminNotes(e.target.value)}
            placeholder={translate('Add internal notes about this property...', 'ስለዚህ ንብረት የውስጥ ማስታወሻዎችን ይጨምሩ...')}
            rows={3}
            maxLength={1000}
          />
          <p className="text-xs text-gray-500 mt-1">
            {adminNotes.length}/1000 {translate('characters', 'ቁምፊዎች')}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          {onClose && (
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              {translate('Cancel', 'ሰርዝ')}
            </Button>
          )}
          <Button onClick={handleSave} disabled={isLoading} className="bg-nibret-blue hover:bg-nibret-blue/90">
            <Save className="w-4 h-4 mr-2" />
            {isLoading ? translate('Saving...', 'በማስቀመጥ ላይ...') : translate('Save Tags', 'መለያዎችን አስቀምጥ')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminPropertyTagManager;
