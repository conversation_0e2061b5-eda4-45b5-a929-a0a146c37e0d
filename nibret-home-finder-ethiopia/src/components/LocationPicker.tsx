import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, ExternalLink, Search, Navigation } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';

interface LocationPickerProps {
  lat?: number;
  lng?: number;
  onLocationChange: (lat: number, lng: number) => void;
}

const LocationPicker: React.FC<LocationPickerProps> = ({ lat, lng, onLocationChange }) => {
  const { translate } = useAppContext();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  // Default to Addis Ababa coordinates
  const currentLat = lat || 9.0320;
  const currentLng = lng || 38.7469;

  const openGoogleMaps = () => {
    const mapUrl = `https://www.google.com/maps/@${currentLat},${currentLng},15z`;
    window.open(mapUrl, '_blank');
  };

  const openGoogleMapsSearch = () => {
    const searchUrl = `https://www.google.com/maps/search/${encodeURIComponent(searchQuery)}`;
    window.open(searchUrl, '_blank');
  };

  const useCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLat = position.coords.latitude;
          const newLng = position.coords.longitude;
          onLocationChange(newLat, newLng);
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('Unable to get your current location. Please enter coordinates manually or use the map.');
        }
      );
    } else {
      alert('Geolocation is not supported by this browser.');
    }
  };

  const commonLocations = [
    { name: 'Addis Ababa City Center', lat: 9.0320, lng: 38.7469 },
    { name: 'Bole', lat: 8.9806, lng: 38.7578 },
    { name: 'Piassa', lat: 9.0320, lng: 38.7469 },
    { name: 'Kazanchis', lat: 9.0320, lng: 38.7469 },
    { name: 'CMC', lat: 9.0320, lng: 38.7469 },
    { name: 'Megenagna', lat: 9.0320, lng: 38.7469 },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MapPin className="mr-2 h-5 w-5" />
          {translate("Property Location", "የንብረት አካባቢ")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Coordinates Display */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label>{translate("Latitude", "ኬንትሮስ")}</Label>
            <Input
              type="number"
              step="0.000001"
              value={currentLat}
              onChange={(e) => onLocationChange(parseFloat(e.target.value) || currentLat, currentLng)}
              placeholder="9.0320"
            />
          </div>
          <div>
            <Label>{translate("Longitude", "ኬንትሮስ")}</Label>
            <Input
              type="number"
              step="0.000001"
              value={currentLng}
              onChange={(e) => onLocationChange(currentLat, parseFloat(e.target.value) || currentLng)}
              placeholder="38.7469"
            />
          </div>
        </div>

        {/* Location Search */}
        <div className="space-y-2">
          <Label>{translate("Search Location", "አካባቢ ፈልግ")}</Label>
          <div className="flex gap-2">
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={translate("Enter address or landmark", "አድራሻ ወይም ምልክት ያስገቡ")}
              onKeyPress={(e) => e.key === 'Enter' && openGoogleMapsSearch()}
              className="text-gray-900 placeholder:text-gray-500 bg-white"
            />
            <Button
              type="button"
              variant="outline"
              onClick={openGoogleMapsSearch}
              disabled={!searchQuery.trim()}
            >
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Quick Location Buttons */}
        <div className="space-y-2">
          <Label>{translate("Quick Locations", "ፈጣን አካባቢዎች")}</Label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {commonLocations.map((location, index) => (
              <Button
                key={index}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => onLocationChange(location.lat, location.lng)}
                className="text-xs"
              >
                {location.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={useCurrentLocation}
            className="flex items-center gap-2"
          >
            <Navigation className="h-4 w-4" />
            {translate("Use My Location", "የእኔን አካባቢ ተጠቀም")}
          </Button>
          
          <Button
            type="button"
            variant="outline"
            onClick={openGoogleMaps}
            className="flex items-center gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            {translate("Open in Google Maps", "በጉግል ካርታ ክፈት")}
          </Button>
        </div>

        {/* Current Location Display */}
        <div className="bg-blue-50 p-3 rounded-lg">
          <p className="text-sm text-blue-700">
            <strong>{translate("Selected Location:", "የተመረጠ አካባቢ:")}</strong>
          </p>
          <p className="text-sm text-blue-600">
            {translate("Latitude:", "ኬንትሮስ:")} {currentLat.toFixed(6)}
          </p>
          <p className="text-sm text-blue-600">
            {translate("Longitude:", "ኬንትሮስ:")} {currentLng.toFixed(6)}
          </p>
          <p className="text-xs text-blue-500 mt-2">
            {translate("You can manually edit coordinates above, use quick locations, search for an address, or open the map to select a precise location.", "ከላይ መጋጠሚያዎችን በእጅ ማርትዕ፣ ፈጣን አካባቢዎችን መጠቀም፣ አድራሻ መፈለግ ወይም ትክክለኛ አካባቢ ለመምረጥ ካርታ መክፈት ይችላሉ።")}
          </p>
        </div>

        {/* Instructions */}
        <div className="bg-gray-50 p-3 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-2">
            {translate("How to use:", "እንዴት መጠቀም:")}
          </h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• {translate("Click 'Use My Location' to automatically detect your location", "አካባቢዎን በራስ-ሰር ለማወቅ 'የእኔን አካባቢ ተጠቀም' ይጫኑ")}</li>
            <li>• {translate("Select from quick locations for common areas in Addis Ababa", "በአዲስ አበባ ውስጥ ለተለመዱ አካባቢዎች ከፈጣን አካባቢዎች ይምረጡ")}</li>
            <li>• {translate("Search for a specific address or landmark", "ለተወሰነ አድራሻ ወይም ምልክት ይፈልጉ")}</li>
            <li>• {translate("Open Google Maps to visually select the exact location", "ትክክለኛውን አካባቢ በእይታ ለመምረጥ ጉግል ካርታ ይክፈቱ")}</li>
            <li>• {translate("Manually enter latitude and longitude if you know them", "ካወቁዋቸው ኬንትሮስ እና ኬንትሮስ በእጅ ያስገቡ")}</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default LocationPicker;
