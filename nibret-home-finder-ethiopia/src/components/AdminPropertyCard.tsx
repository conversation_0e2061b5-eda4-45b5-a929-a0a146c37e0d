import React, { useState } from 'react';
import { useAppContext } from '../context/AppContext';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  MapPin,
  Star,
  ChevronLeft,
  ChevronRight,
  Edit,
  Trash2,
  Eye,
  Archive,
  FileText,
  Calendar,
  MoreVertical
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { Property } from '@/lib/api';
import PropertyTags from './PropertyTags';
import PriceDisplay from './PriceDisplay';

interface AdminPropertyCardProps {
  property: Property;
  showSlideshow?: boolean;
  onDelete?: (propertyId: string) => void;
  onStatusChange?: (propertyId: string, status: string) => void;
}

const AdminPropertyCard: React.FC<AdminPropertyCardProps> = ({ 
  property, 
  showSlideshow = false,
  onDelete,
  onStatusChange
}) => {
  const { translate, formatCurrency, convertCurrency } = useAppContext();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const {
    id,
    title,
    price,
    currency,
    beds,
    baths,
    sqm,
    address,
    images = [],
    publish_status,
    created_at
  } = property;

  const hasMultipleImages = images.length > 1;
  const currentImage = images[currentImageIndex] || '/placeholder-property.jpg';

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleDelete = () => {
    if (window.confirm(`Are you sure you want to delete "${title}"? This action cannot be undone.`)) {
      onDelete?.(id);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-500';
      case 'draft': return 'bg-yellow-500';
      case 'archived': return 'bg-gray-500';
      default: return 'bg-blue-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published': return <Eye className="h-3 w-3" />;
      case 'draft': return <FileText className="h-3 w-3" />;
      case 'archived': return <Archive className="h-3 w-3" />;
      default: return <FileText className="h-3 w-3" />;
    }
  };

  return (
    <Card className="property-card overflow-hidden h-full hover:shadow-lg transition-shadow">
      <div className="relative h-48 overflow-hidden group">
        <img
          src={currentImage}
          alt={title}
          className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
        />

        {/* Image Navigation */}
        {showSlideshow && hasMultipleImages && (
          <>
            <button
              onClick={prevImage}
              className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-black/70"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={nextImage}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-black/70"
            >
              <ChevronRight className="h-4 w-4" />
            </button>

            {/* Image Indicators */}
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
              {images.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                  }`}
                />
              ))}
            </div>
          </>
        )}

        {/* Status Badge */}
        <div className="absolute top-2 left-2">
          <Badge className={`${getStatusColor(publish_status)} text-white shadow-lg`}>
            {getStatusIcon(publish_status)}
            <span className="ml-1 capitalize">{publish_status}</span>
          </Badge>
        </div>

        {/* Featured Badge */}
        {property.is_featured && (
          <div className="absolute top-2 right-2">
            <Badge className="bg-yellow-500 text-white shadow-lg">
              <Star className="h-3 w-3 mr-1 fill-current" />
              {translate('Featured', 'ተመራጭ')}
            </Badge>
          </div>
        )}
      </div>

      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 flex-1">
            {title}
          </h3>
        </div>

        <div className="flex items-center text-gray-600 mb-2">
          <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
          <span className="text-sm truncate">{address}</span>
        </div>

        <div className="flex items-center justify-between mb-3">
          {price > 0 ? (
            <PriceDisplay
              price={convertCurrency(price, currency)}
              currency="ETB"
              className="text-lg font-bold text-nibret-blue"
            />
          ) : (
            <span className="text-lg font-bold text-gray-500">
              {translate('Price on Request', 'ዋጋ በጥያቄ')}
            </span>
          )}
          {/* Property Details - Only show if at least one value exists */}
          {(beds > 0 || baths > 0 || sqm > 0) && (
            <div className="flex items-center text-sm text-gray-600 space-x-3">
              {beds > 0 && <span>{beds} {translate('beds', 'መኝታ')}</span>}
              {baths > 0 && <span>{baths} {translate('baths', 'መታጠቢያ')}</span>}
              {sqm > 0 && <span>{sqm}m²</span>}
            </div>
          )}
        </div>

        {/* Property Tags */}
        <div className="mb-3">
          <PropertyTags
            propertyType={property.propertyType}
            status={property.status}
            listing_type={property.listing_type}
            is_featured={property.is_featured}
            size="sm"
          />
        </div>

        {/* Created Date */}
        <div className="flex items-center text-xs text-gray-500 mb-3">
          <Calendar className="h-3 w-3 mr-1" />
          {translate('Created', 'ተፈጥሯል')}: {new Date(created_at).toLocaleDateString()}
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <div className="flex space-x-2 w-full">
          {/* Primary Action - View Property */}
          <Button asChild variant="default" className="flex-1">
            <Link to={`/property/${id}`}>
              <Eye className="h-4 w-4 mr-2" />
              {translate('View', 'ይመልከቱ')}
            </Link>
          </Button>

          {/* Actions Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {/* Edit Property */}
              <DropdownMenuItem asChild>
                <Link to={`/edit-property/${id}`} className="flex items-center cursor-pointer">
                  <Edit className="h-4 w-4 mr-2" />
                  {translate('Edit Property', 'ንብረት አርትዕ')}
                </Link>
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {/* Status Actions */}
              {publish_status !== 'published' && (
                <DropdownMenuItem onClick={() => onStatusChange?.(id, 'published')}>
                  <Eye className="h-4 w-4 mr-2" />
                  {translate('Publish', 'አትም')}
                </DropdownMenuItem>
              )}

              {publish_status !== 'draft' && (
                <DropdownMenuItem onClick={() => onStatusChange?.(id, 'draft')}>
                  <FileText className="h-4 w-4 mr-2" />
                  {translate('Set as Draft', 'እንደ ረቂቅ አስቀምጥ')}
                </DropdownMenuItem>
              )}

              {publish_status !== 'archived' && (
                <DropdownMenuItem onClick={() => onStatusChange?.(id, 'archived')}>
                  <Archive className="h-4 w-4 mr-2" />
                  {translate('Archive', 'ማህደር')}
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />

              {/* Delete Action */}
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-red-600 focus:text-red-600 focus:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {translate('Delete Property', 'ንብረት ሰርዝ')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardFooter>
    </Card>
  );
};

export default AdminPropertyCard;
