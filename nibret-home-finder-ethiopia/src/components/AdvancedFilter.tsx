
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Search, Filter } from 'lucide-react';
import { useAppContext } from '../context/AppContext';

export interface FilterOptions {
  minPrice?: number;
  maxPrice?: number;
  minBeds?: number;
  maxBeds?: number;
  propertyType?: string;
  sortBy?: string;
  minSqft?: number;
  maxSqft?: number;
  searchTerm?: string;
}

interface AdvancedFilterProps {
  onFilter: (options: FilterOptions) => void;
  initialOptions?: FilterOptions;
  showPropertyTypeFilter?: boolean;
  showBedroomsFilter?: boolean;
  showPriceFilter?: boolean;
  showSqftFilter?: boolean;
  showSortOptions?: boolean;
  className?: string;
}

const AdvancedFilter = ({
  onFilter,
  initialOptions = {},
  showPropertyTypeFilter = true,
  showBedroomsFilter = true,
  showPriceFilter = true,
  showSqftFilter = true,
  showSortOptions = true,
  className = '',
}: AdvancedFilterProps) => {
  const { translate } = useAppContext();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(initialOptions.searchTerm || '');
  const [options, setOptions] = useState<FilterOptions>(initialOptions);

  const handleInputChange = (key: keyof FilterOptions, value: any) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    handleInputChange('searchTerm', value);
  };

  const handleApplyFilters = () => {
    onFilter({ ...options, searchTerm });
    setIsOpen(false);
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    const clearedOptions = {};
    setOptions(clearedOptions);
    onFilter(clearedOptions);
  };

  const handleSearchSubmit = () => {
    onFilter({ ...options, searchTerm });
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="flex gap-2 w-full">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            type="text"
            placeholder={translate("Search by location, title, features...", "በአካባቢ፣ በርዕስ፣ በባህሪያት ይፈልጉ...")}
            className="pl-10 bg-white border border-gray-200 h-12 text-gray-900 placeholder:text-gray-500"
            value={searchTerm}
            onChange={handleSearchChange}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearchSubmit();
              }
            }}
          />
        </div>
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="h-12 px-3 flex items-center gap-2">
              <Filter className="h-4 w-4" />
              {translate("Filters", "ማጣሪያዎች")}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-80 p-4 bg-white" align="end">
            <div className="space-y-4">
              <h3 className="font-medium">{translate("Filter Options", "የማጣሪያ አማራጮች")}</h3>
              
              {showPropertyTypeFilter && (
                <div className="space-y-2">
                  <Label>{translate("Property Type", "የንብረት አይነት")}</Label>
                  <Select 
                    value={options.propertyType || undefined} 
                    onValueChange={(val) => handleInputChange('propertyType', val)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={translate("Any type", "ማንኛውም አይነት")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="any">
                        {translate("Any type", "ማንኛውም አይነት")}
                      </SelectItem>
                      <SelectItem value="house">
                        {translate("House", "ቤት")}
                      </SelectItem>
                      <SelectItem value="apartment">
                        {translate("Apartment", "አፓርታማ")}
                      </SelectItem>
                      <SelectItem value="villa">
                        {translate("Villa", "ቪላ")}
                      </SelectItem>
                      <SelectItem value="condo">
                        {translate("Condo", "ኮንዶ")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              {showBedroomsFilter && (
                <div className="space-y-2">
                  <Label>{translate("Bedrooms", "መኝታ ቤቶች")}</Label>
                  <div className="flex gap-4">
                    <div className="w-1/2">
                      <Select 
                        value={options.minBeds?.toString() || undefined} 
                        onValueChange={(val) => handleInputChange('minBeds', val ? parseInt(val) : undefined)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={translate("Min", "ዝቅተኛ")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="any">
                            {translate("Any", "ማንኛውም")}
                          </SelectItem>
                          {[1, 2, 3, 4, 5].map((n) => (
                            <SelectItem key={n} value={n.toString()}>{n}+</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="w-1/2">
                      <Select 
                        value={options.maxBeds?.toString() || undefined} 
                        onValueChange={(val) => handleInputChange('maxBeds', val ? parseInt(val) : undefined)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={translate("Max", "ከፍተኛ")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="any">
                            {translate("Any", "ማንኛውም")}
                          </SelectItem>
                          {[1, 2, 3, 4, 5, 6, 7].map((n) => (
                            <SelectItem key={n} value={n.toString()}>{n}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}
              
              {showPriceFilter && (
                <div className="space-y-2">
                  <Label>{translate("Price Range", "የዋጋ ክልል")}</Label>
                  <div className="flex gap-4">
                    <div className="w-1/2">
                      <Input
                        type="number"
                        placeholder={translate("Min $", "ዝቅተኛ $")}
                        value={options.minPrice || ''}
                        onChange={(e) => handleInputChange('minPrice', e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </div>
                    <div className="w-1/2">
                      <Input
                        type="number"
                        placeholder={translate("Max $", "ከፍተኛ $")}
                        value={options.maxPrice || ''}
                        onChange={(e) => handleInputChange('maxPrice', e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </div>
                  </div>
                </div>
              )}
              
              {showSqftFilter && (
                <div className="space-y-2">
                  <Label>{translate("Square Footage", "ስኴር ጫማ")}</Label>
                  <div className="flex gap-4">
                    <div className="w-1/2">
                      <Input
                        type="number"
                        placeholder={translate("Min sqft", "ዝቅተኛ ስኴር ጫማ")}
                        value={options.minSqft || ''}
                        onChange={(e) => handleInputChange('minSqft', e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </div>
                    <div className="w-1/2">
                      <Input
                        type="number"
                        placeholder={translate("Max sqft", "ከፍተኛ ስኴር ጫማ")}
                        value={options.maxSqft || ''}
                        onChange={(e) => handleInputChange('maxSqft', e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </div>
                  </div>
                </div>
              )}
              
              {showSortOptions && (
                <div className="space-y-2">
                  <Label>{translate("Sort By", "ደርድር በ")}</Label>
                  <Select 
                    value={options.sortBy || 'newest'} 
                    onValueChange={(val) => handleInputChange('sortBy', val)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={translate("Select option", "አማራጭ ይምረጡ")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newest">
                        {translate("Newest first", "አዲስ መጀመሪያ")}
                      </SelectItem>
                      <SelectItem value="price_high">
                        {translate("Price high to low", "ዋጋ ከፍተኛ ወደ ዝቅተኛ")}
                      </SelectItem>
                      <SelectItem value="price_low">
                        {translate("Price low to high", "ዋጋ ዝቅተኛ ወደ ከፍተኛ")}
                      </SelectItem>
                      <SelectItem value="sqft_high">
                        {translate("Largest size", "ትልቅ መጠን")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              <div className="flex gap-2 pt-2">
                <Button variant="outline" className="w-1/2" onClick={handleClearFilters}>
                  {translate("Clear", "አጽዳ")}
                </Button>
                <Button className="w-1/2 bg-nibret-blue hover:bg-nibret-blue/90" onClick={handleApplyFilters}>
                  {translate("Apply", "ተግብር")}
                </Button>
              </div>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default AdvancedFilter;
