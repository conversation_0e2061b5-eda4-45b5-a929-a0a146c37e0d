import React, { useState, useEffect } from 'react';
import { currencyAPI } from '../lib/api/currency';
import { useAppContext } from '../context/AppContext';

const ExchangeRateDebug: React.FC = () => {
  const { exchangeRate, convertCurrency, formatCurrency } = useAppContext();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testExchangeRate = async () => {
    setIsLoading(true);
    try {
      console.log('🧪 Testing exchange rate...');
      
      // Test getting exchange rate
      const rate = await currencyAPI.getExchangeRate();
      console.log('✅ Exchange rate:', rate);
      
      // Test conversions
      const usd100ToEtb = convertCurrency(100, 'USD');
      const etb5000ToUsd = convertCurrency(5000, 'ETB');
      
      // Test API health
      const health = await currencyAPI.checkAPIHealth();
      console.log('🏥 API Health:', health);
      
      setDebugInfo({
        currentRate: rate,
        contextRate: exchangeRate,
        conversions: {
          usd100ToEtb,
          etb5000ToUsd
        },
        apiHealth: health,
        formattedPrices: {
          usd100: formatCurrency(100, 'USD'),
          etb5000: formatCurrency(5000, 'ETB'),
          convertedUsd100: formatCurrency(usd100ToEtb, 'ETB'),
          convertedEtb5000: formatCurrency(etb5000ToUsd, 'USD')
        }
      });
    } catch (error) {
      console.error('❌ Exchange rate test failed:', error);
      setDebugInfo({
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Auto-test on component mount
    testExchangeRate();
  }, []);

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 m-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-yellow-800">
          🔧 Exchange Rate Debug
        </h3>
        <button
          onClick={testExchangeRate}
          disabled={isLoading}
          className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700 disabled:opacity-50"
        >
          {isLoading ? 'Testing...' : 'Refresh Test'}
        </button>
      </div>
      
      {debugInfo && (
        <div className="space-y-3 text-sm">
          {debugInfo.error ? (
            <div className="text-red-600">
              <strong>Error:</strong> {debugInfo.error}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <strong>Current API Rate:</strong>
                  <div className="ml-2">
                    {debugInfo.currentRate?.rate} ETB/USD
                    <br />
                    <span className={`text-sm ${debugInfo.currentRate?.source === 'Fallback Rate' ? 'text-red-600' : 'text-gray-600'}`}>
                      Source: {debugInfo.currentRate?.source}
                      {debugInfo.currentRate?.source === 'Fallback Rate' && ' (Hidden from UI)'}
                    </span>
                  </div>
                </div>
                
                <div>
                  <strong>Context Rate:</strong>
                  <div className="ml-2">
                    {debugInfo.contextRate} ETB/USD
                  </div>
                </div>
              </div>
              
              <div>
                <strong>Test Conversions:</strong>
                <div className="ml-2 grid grid-cols-1 md:grid-cols-2 gap-2">
                  <div>
                    $100 USD → {debugInfo.formattedPrices?.convertedUsd100}
                  </div>
                  <div>
                    5,000 ETB → {debugInfo.formattedPrices?.convertedEtb5000}
                  </div>
                </div>
              </div>
              
              <div>
                <strong>API Health:</strong>
                <div className="ml-2">
                  {Object.entries(debugInfo.apiHealth || {}).map(([api, status]) => (
                    <span key={api} className={`inline-block mr-3 ${status ? 'text-green-600' : 'text-red-600'}`}>
                      {api}: {status ? '✅' : '❌'}
                    </span>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      )}
      
      <div className="mt-3 text-xs text-gray-600">
        This debug component will be removed in production. Check browser console for detailed logs.
      </div>
    </div>
  );
};

export default ExchangeRateDebug;
