import React, { useState, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { Upload, X, Image as ImageIcon, Loader2, Plus } from 'lucide-react';
import { validateImageFile, getOptimizedImageUrl, extractPublicId } from '@/lib/cloudinary';
import { useAppContext } from '@/context/AppContext';
import { uploadApi } from '@/lib/api';

interface ImageUploadProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  maxImages?: number;
  allowUrlInput?: boolean;
  className?: string;
}

interface UploadedImage {
  url: string;
  publicId: string;
  uploading: boolean;
  progress: number;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  images,
  onImagesChange,
  maxImages = 10,
  allowUrlInput = true,
  className = ''
}) => {
  const { translate } = useAppContext();
  const { toast } = useToast();
  const [uploadingImages, setUploadingImages] = useState<UploadedImage[]>([]);
  const [urlInput, setUrlInput] = useState('');

  // Convert existing URLs to UploadedImage format (memoized to prevent infinite re-renders)
  const existingImages: UploadedImage[] = useMemo(() =>
    images.map(url => ({
      url,
      publicId: extractPublicId(url),
      uploading: false,
      progress: 100
    })), [images]);

  const allImages = useMemo(() => {
    const combined = [...existingImages, ...uploadingImages];
    console.log('🔄 allImages recalculated:', {
      existingImages: existingImages.length,
      uploadingImages: uploadingImages.length,
      total: combined.length,
      urls: combined.map(img => img.url)
    });
    return combined;
  }, [existingImages, uploadingImages]);

  // Handle file selection
  const handleFileSelect = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);

    // Check if adding these files would exceed the limit
    if (allImages.length + fileArray.length > maxImages) {
      toast({
        variant: "destructive",
        title: translate("Too many images", "በጣም ብዙ ምስሎች"),
        description: translate(`Maximum ${maxImages} images allowed`, `ከፍተኛው ${maxImages} ምስሎች ተፈቅደዋል`),
      });
      return;
    }

    // Validate each file
    for (const file of fileArray) {
      const validation = validateImageFile(file);
      if (!validation.valid) {
        toast({
          variant: "destructive",
          title: translate("Invalid file", "ልክ ያልሆነ ፋይል"),
          description: validation.error,
        });
        return;
      }
    }

    // Create preview images for immediate UI feedback
    const newUploadingImages: UploadedImage[] = fileArray.map(file => ({
      url: URL.createObjectURL(file),
      publicId: '',
      uploading: true,
      progress: 0
    }));

    setUploadingImages(prev => [...prev, ...newUploadingImages]);

    try {
      // Use multiple upload API for better performance
      if (fileArray.length > 1) {
        console.log('📤 Uploading multiple images:', fileArray.length);

        // Simulate progress for all images
        const progressInterval = setInterval(() => {
          setUploadingImages(prev =>
            prev.map(img =>
              img.uploading ? { ...img, progress: Math.min(img.progress + 5, 90) } : img
            )
          );
        }, 300);

        const result = await uploadApi.uploadMultipleImages(fileArray);
        clearInterval(progressInterval);

        if (!result.success || !result.data) {
          throw new Error(result.error || 'Multiple upload failed');
        }

        // Update all images at once
        const uploadedUrls = result.data.map((img: any) => img.secure_url);
        const newImages = [...images, ...uploadedUrls];
        onImagesChange(newImages);

        // Clear uploading images
        setUploadingImages(prev => prev.filter(img => !img.uploading));

        toast({
          title: translate("Upload successful", "መጫን ተሳክቷል"),
          description: translate(`${fileArray.length} images uploaded successfully`, `${fileArray.length} ምስሎች በተሳካ ሁኔታ ተጭነዋል`),
        });

      } else {
        // Single file upload
        const file = fileArray[0];
        console.log('📤 Uploading single image:', file.name);

        const progressInterval = setInterval(() => {
          setUploadingImages(prev =>
            prev.map(img =>
              img.uploading ? { ...img, progress: Math.min(img.progress + 10, 90) } : img
            )
          );
        }, 200);

        const result = await uploadApi.uploadImage(file);
        clearInterval(progressInterval);

        if (!result.success) {
          throw new Error(result.error || 'Upload failed');
        }

        // Add to final images list
        const newImages = [...images, result.data.secure_url];
        onImagesChange(newImages);

        // Clear uploading images
        setUploadingImages(prev => prev.filter(img => !img.uploading));

        toast({
          title: translate("Upload successful", "መጫን ተሳክቷል"),
          description: translate("Image uploaded successfully", "ምስል በተሳካ ሁኔታ ተጭኗል"),
        });
      }

    } catch (error) {
      console.error('Upload error:', error);

      // Clear all uploading images on error
      setUploadingImages(prev => prev.filter(img => !img.uploading));

      toast({
        variant: "destructive",
        title: translate("Upload failed", "መጫን አልተሳካም"),
        description: translate("Failed to upload images. Please try again.", "ምስሎችን መጫን አልተሳካም። እባክዎ እንደገና ይሞክሩ።"),
      });
    }
  }, [images, onImagesChange, maxImages, allImages.length, toast, translate]);

  // Handle drag and drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  // Handle URL input
  const handleUrlAdd = useCallback(() => {
    if (!urlInput.trim()) return;

    if (allImages.length >= maxImages) {
      toast({
        variant: "destructive",
        title: translate("Too many images", "በጣም ብዙ ምስሎች"),
        description: translate(`Maximum ${maxImages} images allowed`, `ከፍተኛው ${maxImages} ምስሎች ተፈቅደዋል`),
      });
      return;
    }

    // Basic URL validation
    try {
      new URL(urlInput);
    } catch {
      toast({
        variant: "destructive",
        title: translate("Invalid URL", "ልክ ያልሆነ URL"),
        description: translate("Please enter a valid image URL", "እባክዎ ትክክለኛ የምስል URL ያስገቡ"),
      });
      return;
    }

    const newImages = [...images, urlInput.trim()];
    onImagesChange(newImages);
    setUrlInput('');

    toast({
      title: translate("Image added", "ምስል ተጨምሯል"),
      description: translate("Image URL added successfully", "የምስል URL በተሳካ ሁኔታ ተጨምሯል"),
    });
  }, [urlInput, images, onImagesChange, allImages.length, maxImages, toast, translate]);

  // Remove image
  const handleRemoveImage = useCallback((index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    onImagesChange(newImages);
  }, [images, onImagesChange]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
          >
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {translate("Upload Images", "ምስሎችን ይጫኑ")}
            </h3>
            <p className="text-gray-600 mb-4">
              {translate("Drag and drop images here, or click to select", "ምስሎችን እዚህ ይጎትቱ እና ይጣሉ፣ ወይም ለመምረጥ ይጫኑ")}
            </p>

            <Input
              type="file"
              multiple
              accept="image/*"
              onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
              className="hidden"
              id="image-upload"
            />

            <Label htmlFor="image-upload">
              <Button type="button" variant="outline" className="cursor-pointer">
                <Plus className="h-4 w-4 mr-2" />
                {translate("Select Images", "ምስሎችን ይምረጡ")}
              </Button>
            </Label>

            <p className="text-sm text-gray-500 mt-2">
              {translate(`Maximum ${maxImages} images, up to 10MB each`, `ከፍተኛው ${maxImages} ምስሎች፣ እያንዳንዳቸው እስከ 10MB`)}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* URL Input */}
      {allowUrlInput && (
        <Card>
          <CardContent className="p-4">
            <Label className="text-sm font-medium mb-2 block">
              {translate("Or add image URL", "ወይም የምስል URL ያክሉ")}
            </Label>
            <div className="flex gap-2">
              <Input
                value={urlInput}
                onChange={(e) => setUrlInput(e.target.value)}
                placeholder={translate("https://example.com/image.jpg", "https://example.com/image.jpg")}
                onKeyPress={(e) => e.key === 'Enter' && handleUrlAdd()}
              />
              <Button onClick={handleUrlAdd} variant="outline">
                {translate("Add", "አክል")}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Image Preview Grid */}
      {allImages.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {allImages.map((image, index) => (
            <Card key={index} className="relative overflow-hidden">
              <CardContent className="p-0">
                <div className="aspect-square relative">
                  {image.uploading ? (
                    <div className="w-full h-full flex flex-col items-center justify-center bg-gray-100">
                      <Loader2 className="h-8 w-8 animate-spin text-gray-400 mb-2" />
                      <Progress value={image.progress} className="w-3/4" />
                      <p className="text-xs text-gray-500 mt-1">{image.progress}%</p>
                    </div>
                  ) : (
                    <>
                      <img
                        src={image.url}
                        alt={`Property image ${index + 1}`}
                        className="w-full h-full object-cover"
                        onLoad={() => {
                          console.log('✅ Image loaded successfully:', image.url);
                        }}
                        onError={(e) => {
                          console.error('❌ Failed to load image:', image.url);
                          console.error('Error details:', e);

                          // Try to reload the image once
                          const img = e.target as HTMLImageElement;
                          if (!img.dataset.retried) {
                            img.dataset.retried = 'true';
                            console.log('🔄 Retrying image load...');
                            setTimeout(() => {
                              img.src = image.url + '?retry=' + Date.now();
                            }, 1000);
                          } else {
                            // Show placeholder after retry fails
                            img.style.display = 'none';
                            console.error('💥 Image load failed after retry');
                          }
                        }}
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2 h-6 w-6 p-0"
                        onClick={() => handleRemoveImage(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Image Count */}
      <p className="text-sm text-gray-600 text-center">
        {translate(`${images.length} of ${maxImages} images`, `${images.length} ከ ${maxImages} ምስሎች`)}
      </p>
    </div>
  );
};

export default ImageUpload;
