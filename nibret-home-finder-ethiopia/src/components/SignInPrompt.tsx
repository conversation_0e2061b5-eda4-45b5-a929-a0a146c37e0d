import React, { useState, useContext } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AuthContext } from '@/App';
import { useAppContext } from '@/context/AppContext';
import { 
  Lock, 
  Star, 
  Users, 
  TrendingUp, 
  Eye, 
  Heart, 
  Search, 
  MapPin, 
  Bell,
  Shield,
  Zap,
  Gift,
  ArrowRight,
  X
} from 'lucide-react';
import AuthModal from './AuthModal';

interface SignInPromptProps {
  variant?: 'banner' | 'card' | 'modal' | 'inline' | 'floating';
  trigger?: 'search' | 'view' | 'contact' | 'save' | 'filter' | 'general';
  title?: string;
  description?: string;
  benefits?: string[];
  showBenefits?: boolean;
  showStats?: boolean;
  dismissible?: boolean;
  className?: string;
  onDismiss?: () => void;
}

const SignInPrompt: React.FC<SignInPromptProps> = ({
  variant = 'card',
  trigger = 'general',
  title,
  description,
  benefits,
  showBenefits = true,
  showStats = false,
  dismissible = false,
  className = '',
  onDismiss
}) => {
  const { isAuthenticated } = useContext(AuthContext);
  const { translate, language } = useAppContext();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  // Don't show if user is authenticated or prompt is dismissed
  if (isAuthenticated || isDismissed) {
    return null;
  }

  const handleDismiss = () => {
    setIsDismissed(true);
    if (onDismiss) {
      onDismiss();
    }
  };

  const handleSignIn = () => {
    setShowAuthModal(true);
  };

  // Get trigger-specific content
  const getTriggerContent = () => {
    const configs = {
      search: {
        icon: Search,
        title: translate('Sign in to search all properties', 'ሁሉንም ንብረቶች ለመፈለግ ይግቡ'),
        description: translate('Access our complete database of properties with advanced search filters', 'የላቀ የፍለጋ ማጣሪያዎች ያለው ሙሉ የንብረት መረጃ ቋታችንን ይድረሱ'),
        benefits: [
          translate('Search all properties', 'ሁሉንም ንብረቶች ይፈልጉ'),
          translate('Advanced filters', 'የላቀ ማጣሪያዎች'),
          translate('Save searches', 'ፍለጋዎችን ያስቀምጡ'),
          translate('Get alerts', 'ማንቂያዎችን ይቀበሉ')
        ]
      },
      view: {
        icon: Eye,
        title: translate('Sign in to view property details', 'የንብረት ዝርዝሮችን ለማየት ይግቡ'),
        description: translate('Get full access to property information, photos, and contact details', 'ሙሉ የንብረት መረጃ፣ ፎቶዎች እና የመገናኛ ዝርዝሮችን ያግኙ'),
        benefits: [
          translate('View all photos', 'ሁሉንም ፎቶዎች ይመልከቱ'),
          translate('Contact information', 'የመገናኛ መረጃ'),
          translate('Exact addresses', 'ትክክለኛ አድራሻዎች'),
          translate('Property history', 'የንብረት ታሪክ')
        ]
      },
      contact: {
        icon: Users,
        title: translate('Sign in to contact agents', 'ወኪሎችን ለማግኘት ይግቡ'),
        description: translate('Connect directly with property agents and schedule viewings', 'ከንብረት ወኪሎች ጋር በቀጥታ ይገናኙ እና ጉብኝቶችን ያስይዙ'),
        benefits: [
          translate('Direct agent contact', 'ቀጥተኛ ወኪል ግንኙነት'),
          translate('Schedule viewings', 'ጉብኝቶችን ያስይዙ'),
          translate('Get expert advice', 'የባለሙያ ምክር ያግኙ'),
          translate('Priority support', 'ቅድሚያ ድጋፍ')
        ]
      },
      save: {
        icon: Heart,
        title: translate('Sign in to save favorites', 'ተወዳጆችን ለማስቀመጥ ይግቡ'),
        description: translate('Create your personal wishlist and track property updates', 'የግል የምኞት ዝርዝርዎን ይፍጠሩ እና የንብረት ዝመናዎችን ይከታተሉ'),
        benefits: [
          translate('Save favorites', 'ተወዳጆችን ያስቀምጡ'),
          translate('Track updates', 'ዝመናዎችን ይከታተሉ'),
          translate('Compare properties', 'ንብረቶችን ያወዳድሩ'),
          translate('Get notifications', 'ማሳወቂያዎችን ይቀበሉ')
        ]
      },
      filter: {
        icon: TrendingUp,
        title: translate('Sign in for advanced filters', 'ለላቀ ማጣሪያዎች ይግቡ'),
        description: translate('Use powerful search filters to find your perfect property', 'ፍጹም ንብረትዎን ለማግኘት ኃይለኛ የፍለጋ ማጣሪያዎችን ይጠቀሙ'),
        benefits: [
          translate('Price range filters', 'የዋጋ ክልል ማጣሪያዎች'),
          translate('Location filters', 'የአካባቢ ማጣሪያዎች'),
          translate('Property type filters', 'የንብረት አይነት ማጣሪያዎች'),
          translate('Custom searches', 'ብጁ ፍለጋዎች')
        ]
      },
      general: {
        icon: Star,
        title: translate('Join Nibret Real Estate', 'ኒብረት ሪል እስቴትን ይቀላቀሉ'),
        description: translate('Discover your dream home with Ethiopia\'s leading real estate platform', 'በኢትዮጵያ ግንባር ቀደም የሪል እስቴት መድረክ የህልም ቤትዎን ያግኙ'),
        benefits: [
          translate('Access all properties', 'ሁሉንም ንብረቶች ይድረሱ'),
          translate('Expert guidance', 'የባለሙያ መመሪያ'),
          translate('Market insights', 'የገበያ ግንዛቤዎች'),
          translate('Exclusive deals', 'ልዩ ዲሎች')
        ]
      }
    };

    return configs[trigger] || configs.general;
  };

  const content = getTriggerContent();
  const defaultBenefits = benefits || content.benefits;

  // Stats data
  const stats = [
    { label: translate('Properties', 'ንብረቶች'), value: '10,000+' },
    { label: translate('Happy Clients', 'ደስተኛ ደንበኞች'), value: '5,000+' },
    { label: translate('Expert Agents', 'የባለሙያ ወኪሎች'), value: '100+' },
    { label: translate('Cities', 'ከተሞች'), value: '50+' }
  ];

  const renderContent = () => (
    <>
      <div className="text-center mb-6">
        <div className="mx-auto w-16 h-16 bg-nibret-blue/10 rounded-full flex items-center justify-center mb-4">
          <content.icon className="w-8 h-8 text-nibret-blue" />
        </div>
        <h3 className={`text-xl md:text-2xl font-bold text-gray-900 mb-2 ${language === 'am' ? 'amharic' : ''}`}>
          {title || content.title}
        </h3>
        <p className={`text-gray-600 ${language === 'am' ? 'amharic' : ''}`}>
          {description || content.description}
        </p>
      </div>

      {showStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-2xl font-bold text-nibret-blue">{stat.value}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>
      )}

      {showBenefits && defaultBenefits && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
          {defaultBenefits.map((benefit, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              </div>
              <span className={`text-sm text-gray-700 ${language === 'am' ? 'amharic' : ''}`}>
                {benefit}
              </span>
            </div>
          ))}
        </div>
      )}

      <div className="space-y-3">
        <Button 
          onClick={handleSignIn}
          className="w-full bg-nibret-blue hover:bg-nibret-blue/90 text-white"
          size="lg"
        >
          {translate('Sign In / Sign Up', 'ይግቡ / ይመዝገቡ')}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
        
        <p className={`text-xs text-center text-gray-500 ${language === 'am' ? 'amharic' : ''}`}>
          {translate('Free to join • No hidden fees • Trusted by thousands', 'ነፃ ለመቀላቀል • ምንም የተደበቁ ክፍያዎች • በሺዎች የሚታመን')}
        </p>
      </div>
    </>
  );

  // Render based on variant
  switch (variant) {
    case 'banner':
      return (
        <div className={`bg-gradient-to-r from-nibret-blue to-blue-600 text-white p-4 ${className}`}>
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <content.icon className="w-6 h-6" />
              <div>
                <h4 className="font-semibold">{title || content.title}</h4>
                <p className="text-sm opacity-90">{description || content.description}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button 
                onClick={handleSignIn}
                variant="secondary"
                size="sm"
              >
                {translate('Sign In', 'ይግቡ')}
              </Button>
              {dismissible && (
                <Button 
                  onClick={handleDismiss}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:text-gray-200"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      );

    case 'inline':
      return (
        <div className={`flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg ${className}`}>
          <div className="flex items-center space-x-3">
            <Lock className="w-5 h-5 text-blue-600" />
            <span className="text-sm text-blue-800">{title || content.title}</span>
          </div>
          <Button 
            onClick={handleSignIn}
            size="sm"
            className="bg-nibret-blue hover:bg-nibret-blue/90"
          >
            {translate('Sign In', 'ይግቡ')}
          </Button>
        </div>
      );

    case 'floating':
      return (
        <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
          <Card className="w-80 shadow-xl border-0 bg-white">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{translate('Sign In', 'ይግቡ')}</CardTitle>
                {dismissible && (
                  <Button 
                    onClick={handleDismiss}
                    variant="ghost"
                    size="sm"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {renderContent()}
            </CardContent>
          </Card>
        </div>
      );

    case 'card':
    default:
      return (
        <Card className={`border-0 shadow-lg ${className}`}>
          <CardContent className="p-6">
            {dismissible && (
              <div className="flex justify-end mb-2">
                <Button 
                  onClick={handleDismiss}
                  variant="ghost"
                  size="sm"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            )}
            {renderContent()}
          </CardContent>
        </Card>
      );
  }

  return (
    <>
      {/* Auth Modal */}
      <AuthModal 
        isOpen={showAuthModal} 
        onClose={() => setShowAuthModal(false)} 
        defaultTab="login"
      />
    </>
  );
};

export default SignInPrompt;
