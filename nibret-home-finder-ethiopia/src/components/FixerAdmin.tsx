import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { fixerAPI } from '@/lib/api/fixer';
import { 
  RefreshCw, 
  Settings, 
  TrendingUp, 
  Clock, 
  AlertTriangle, 
  CheckCircle,
  DollarSign,
  Globe,
  Zap,
  Database,
  Activity
} from 'lucide-react';

interface HealthStatus {
  available: boolean;
  responseTime?: number;
  error?: string;
}

interface ExchangeRateInfo {
  rate: number;
  lastUpdated: string;
  source: string;
  responseTime: number;
  timestamp?: number;
}

const FixerAdmin: React.FC = () => {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [exchangeRate, setExchangeRate] = useState<ExchangeRateInfo | null>(null);
  const [supportedCurrencies, setSupportedCurrencies] = useState<Record<string, string> | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [cacheStats, setCacheStats] = useState<{ size: number; keys: string[] }>({ size: 0, keys: [] });

  // Check if API key is configured
  const isApiKeyConfigured = () => {
    const apiKey = import.meta.env.VITE_FIXER_API_KEY;
    return apiKey && apiKey !== 'your_fixer_api_key_here';
  };

  const fetchHealthStatus = async () => {
    setIsLoading(true);
    try {
      const health = await fixerAPI.healthCheck();
      setHealthStatus(health);
      
      if (health.available) {
        setMessage({ type: 'success', text: 'Fixer.io API is working properly' });
      } else {
        setMessage({ type: 'error', text: `Fixer.io API unavailable: ${health.error}` });
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setHealthStatus({ available: false, error: errorMsg });
      setMessage({ type: 'error', text: `Health check failed: ${errorMsg}` });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchExchangeRate = async () => {
    setIsLoading(true);
    try {
      const rate = await fixerAPI.getExchangeRate('USD', 'ETB');
      setExchangeRate(rate);
      setMessage({ type: 'success', text: `Exchange rate fetched: 1 USD = ${rate.rate} ETB` });
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setMessage({ type: 'error', text: `Failed to fetch exchange rate: ${errorMsg}` });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSupportedCurrencies = async () => {
    setIsLoading(true);
    try {
      const currencies = await fixerAPI.getSupportedCurrencies();
      setSupportedCurrencies(currencies);
      setMessage({ type: 'success', text: `Loaded ${Object.keys(currencies).length} supported currencies` });
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setMessage({ type: 'error', text: `Failed to fetch currencies: ${errorMsg}` });
    } finally {
      setIsLoading(false);
    }
  };

  const clearCache = () => {
    fixerAPI.clearCache();
    updateCacheStats();
    setMessage({ type: 'success', text: 'Cache cleared successfully' });
  };

  const updateCacheStats = () => {
    const stats = fixerAPI.getCacheStats();
    setCacheStats(stats);
  };

  const testMultipleRates = async () => {
    setIsLoading(true);
    try {
      const rates = await fixerAPI.getMultipleRates('USD', ['ETB', 'EUR', 'GBP', 'JPY']);
      const rateCount = Object.keys(rates).length;
      setMessage({ type: 'success', text: `Fetched ${rateCount} exchange rates successfully` });
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setMessage({ type: 'error', text: `Multi-rate test failed: ${errorMsg}` });
    } finally {
      setIsLoading(false);
    }
  };

  const testCurrencyConversion = async () => {
    setIsLoading(true);
    try {
      const result = await fixerAPI.convertCurrency(100, 'USD', 'ETB');
      setMessage({ 
        type: 'success', 
        text: `Conversion test: 100 USD = ${result.convertedAmount.toFixed(2)} ETB` 
      });
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setMessage({ type: 'error', text: `Conversion test failed: ${errorMsg}` });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    updateCacheStats();
    if (isApiKeyConfigured()) {
      fetchHealthStatus();
    }
  }, []);

  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  const formatTime = (isoString: string) => {
    try {
      return new Date(isoString).toLocaleString();
    } catch {
      return 'N/A';
    }
  };

  const getStatusBadge = (available: boolean) => {
    return (
      <Badge variant={available ? "default" : "destructive"} className="ml-2">
        {available ? (
          <>
            <CheckCircle className="w-3 h-3 mr-1" />
            Online
          </>
        ) : (
          <>
            <AlertTriangle className="w-3 h-3 mr-1" />
            Offline
          </>
        )}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* API Configuration Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            Fixer.io API Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">API Key Status:</span>
              {isApiKeyConfigured() ? (
                <Badge variant="default">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Configured
                </Badge>
              ) : (
                <Badge variant="destructive">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Not Configured
                </Badge>
              )}
            </div>
            
            {!isApiKeyConfigured() && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Please set your Fixer.io API key in the VITE_FIXER_API_KEY environment variable.
                  Get your free API key at <a href="https://fixer.io" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">fixer.io</a>
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* API Health Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Activity className="w-5 h-5 mr-2" />
              API Health Status
            </span>
            <Button 
              onClick={fetchHealthStatus} 
              disabled={isLoading || !isApiKeyConfigured()}
              size="sm"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Check Health
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {healthStatus ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="font-medium">Status:</span>
                {getStatusBadge(healthStatus.available)}
              </div>
              
              {healthStatus.responseTime && (
                <div className="flex items-center justify-between">
                  <span className="font-medium">Response Time:</span>
                  <Badge variant="outline">
                    <Zap className="w-3 h-3 mr-1" />
                    {healthStatus.responseTime}ms
                  </Badge>
                </div>
              )}
              
              {healthStatus.error && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  <strong>Error:</strong> {healthStatus.error}
                </div>
              )}
            </div>
          ) : (
            <div className="text-gray-500">Click "Check Health" to test API connectivity</div>
          )}
        </CardContent>
      </Card>

      {/* Exchange Rate Testing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Exchange Rate Testing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button 
                onClick={fetchExchangeRate} 
                disabled={isLoading || !isApiKeyConfigured()}
                variant="outline"
              >
                <DollarSign className="w-4 h-4 mr-2" />
                Get USD/ETB Rate
              </Button>
              
              <Button 
                onClick={testCurrencyConversion} 
                disabled={isLoading || !isApiKeyConfigured()}
                variant="outline"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Test Conversion
              </Button>
              
              <Button 
                onClick={testMultipleRates} 
                disabled={isLoading || !isApiKeyConfigured()}
                variant="outline"
              >
                <Globe className="w-4 h-4 mr-2" />
                Test Multi-Rates
              </Button>
              
              <Button 
                onClick={fetchSupportedCurrencies} 
                disabled={isLoading || !isApiKeyConfigured()}
                variant="outline"
              >
                <Database className="w-4 h-4 mr-2" />
                Load Currencies
              </Button>
            </div>

            {exchangeRate && (
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">Latest Exchange Rate</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Rate:</span> 1 USD = {exchangeRate.rate} ETB
                  </div>
                  <div>
                    <span className="font-medium">Source:</span> {exchangeRate.source}
                  </div>
                  <div>
                    <span className="font-medium">Updated:</span> {formatTime(exchangeRate.lastUpdated)}
                  </div>
                  <div>
                    <span className="font-medium">Response Time:</span> {exchangeRate.responseTime}ms
                  </div>
                </div>
              </div>
            )}

            {supportedCurrencies && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">
                  Supported Currencies ({Object.keys(supportedCurrencies).length})
                </h4>
                <div className="text-sm text-blue-700">
                  Including: USD, ETB, EUR, GBP, JPY, CAD, AUD, CHF, and {Object.keys(supportedCurrencies).length - 8} more...
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Cache Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Database className="w-5 h-5 mr-2" />
              Cache Management
            </span>
            <Button onClick={clearCache} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Clear Cache
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium">Cached Entries:</span>
              <Badge variant="outline">{cacheStats.size}</Badge>
            </div>
            
            {cacheStats.keys.length > 0 && (
              <div>
                <span className="font-medium">Cached Pairs:</span>
                <div className="mt-2 flex flex-wrap gap-2">
                  {cacheStats.keys.map(key => (
                    <Badge key={key} variant="secondary" className="text-xs">
                      {key}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            <div className="text-sm text-gray-600">
              <Clock className="w-4 h-4 inline mr-1" />
              Cache expires after 30 minutes
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Messages */}
      {message && (
        <Alert className={message.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
          {message.type === 'error' ? (
            <AlertTriangle className="h-4 w-4" />
          ) : (
            <CheckCircle className="h-4 w-4" />
          )}
          <AlertDescription className={message.type === 'error' ? 'text-red-800' : 'text-green-800'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default FixerAdmin;
