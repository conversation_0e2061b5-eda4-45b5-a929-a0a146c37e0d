import React from 'react';
import { useAppContext } from '@/context/AppContext';
import PricingTags from './PricingTags';

interface PriceDisplayProps {
  price: number;
  originalPrice?: number;
  discountPercentage?: number;
  isNegotiable?: boolean;
  currency?: 'ETB' | 'USD';
  size?: 'sm' | 'default' | 'lg' | 'xl';
  showTags?: boolean;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}

const PriceDisplay: React.FC<PriceDisplayProps> = ({
  price,
  originalPrice,
  discountPercentage,
  isNegotiable = false,
  currency = 'ETB',
  size = 'default',
  showTags = true,
  className = '',
  orientation = 'vertical'
}) => {
  const { translate, convertCurrency, formatCurrency } = useAppContext();

  // Calculate discount if not provided but original price is available
  const calculatedDiscount = discountPercentage ||
    (originalPrice && originalPrice > price ?
      Math.round(((originalPrice - price) / originalPrice) * 100) : 0);

  // Only consider it a discount if it's greater than 0 and we have valid original price
  const hasDiscount = calculatedDiscount > 0 && originalPrice && originalPrice > price;

  // Convert prices to user's selected currency
  const convertedPrice = convertCurrency(price, currency);
  const convertedOriginalPrice = hasDiscount && originalPrice ? convertCurrency(originalPrice, currency) : 0;

  // Format prices
  const formattedPrice = formatCurrency(convertedPrice);
  const formattedOriginalPrice = hasDiscount && convertedOriginalPrice > 0 ? formatCurrency(convertedOriginalPrice) : '';

  // Size variants for price text
  const priceTextSizes = {
    sm: 'text-lg',
    default: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl'
  };

  const originalPriceTextSizes = {
    sm: 'text-sm',
    default: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const containerClasses = orientation === 'horizontal' 
    ? 'flex items-center space-x-3' 
    : 'flex flex-col space-y-2';

  return (
    <div className={`${containerClasses} ${className}`}>
      {/* Price Section */}
      <div className={orientation === 'horizontal' ? 'flex items-center space-x-2' : 'space-y-1'}>
        {/* Current Price */}
        <div className={`font-bold text-nibret-blue ${priceTextSizes[size]}`}>
          {formattedPrice}
          {isNegotiable && (
            <span className="text-sm font-normal text-gray-600 ml-1">
              {translate('(Negotiable)', '(ተደራደር)')}
            </span>
          )}
        </div>

        {/* Original Price (if discounted) */}
        {hasDiscount && formattedOriginalPrice && (
          <div className={`text-gray-500 line-through ${originalPriceTextSizes[size]}`}>
            {formattedOriginalPrice}
          </div>
        )}

        {/* Discount Percentage (inline for horizontal) */}
        {hasDiscount && orientation === 'horizontal' && (
          <div className="text-red-600 font-semibold text-sm">
            -{calculatedDiscount}%
          </div>
        )}
      </div>

      {/* Pricing Tags - Only show if there's a discount or negotiable */}
      {showTags && (hasDiscount || isNegotiable) && (
        <PricingTags
          price={price}
          originalPrice={hasDiscount ? originalPrice : undefined}
          discountPercentage={hasDiscount ? calculatedDiscount : undefined}
          isNegotiable={isNegotiable}
          currency={currency}
          size={size === 'xl' ? 'lg' : size === 'lg' ? 'default' : 'sm'}
        />
      )}

      {/* Savings Information */}
      {hasDiscount && (
        <div className="text-sm text-green-600 font-medium">
          {translate('You save:', 'ይቆጥባሉ:')} {formatCurrency(convertedOriginalPrice - convertedPrice)}
        </div>
      )}
    </div>
  );
};

export default PriceDisplay;
