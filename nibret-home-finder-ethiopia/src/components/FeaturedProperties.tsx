import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { Star, MapPin, BedDouble, Bath, SquareCode, ArrowRight, Eye } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAppContext } from '@/context/AppContext';
import { AuthContext } from '@/App';
import { propertyApi, Property } from '@/lib/api';
import PropertyTags from './PropertyTags';

interface FeaturedPropertiesProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
  autoRefresh?: boolean;
}

const FeaturedProperties: React.FC<FeaturedPropertiesProps> = ({
  limit = 6,
  showHeader = true,
  className = '',
  autoRefresh = false
}) => {
  const { translate, language, convertCurrency, formatCurrency } = useAppContext();
  const { isAuthenticated } = useContext(AuthContext);
  const [featuredProperties, setFeaturedProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Don't render anything if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }

  useEffect(() => {
    const fetchFeaturedProperties = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all properties and filter for featured ones
        const allProperties = await propertyApi.getProperties();
        const featured = allProperties
          .filter(property => property.is_featured)
          .slice(0, limit);

        // If we don't have enough featured properties, add some regular ones
        if (featured.length < limit) {
          const regularProperties = allProperties
            .filter(property => !property.is_featured)
            .slice(0, limit - featured.length);
          featured.push(...regularProperties);
        }

        setFeaturedProperties(featured);
      } catch (error) {
        console.error('Error fetching featured properties:', error);
        setError('Failed to load featured properties');
        
        // Fallback to mock data
        const mockFeaturedProperties: Property[] = [
          {
            id: 'featured-1',
            title: 'Luxury Villa in Bole',
            price: 12500000,
            currency: 'ETB',
            beds: 5,
            baths: 4,
            sqm: 350,
            address: 'Bole, Addis Ababa',
            lat: 8.9806,
            lng: 38.7578,
            propertyType: 'villa',
            status: 'for_sale',
            listing_type: 'sale',
            description: 'Stunning luxury villa with modern amenities',
            images: ['https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=600&h=400&fit=crop'],
            is_featured: true,
            views: 245
          },
          {
            id: 'featured-2',
            title: 'Modern Apartment in Kazanchis',
            price: 8500000,
            currency: 'ETB',
            beds: 3,
            baths: 2,
            sqm: 180,
            address: 'Kazanchis, Addis Ababa',
            lat: 9.0054,
            lng: 38.7636,
            propertyType: 'apartment',
            status: 'for_sale',
            listing_type: 'sale',
            description: 'Beautiful modern apartment with city views',
            images: ['https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=600&h=400&fit=crop'],
            is_featured: true,
            views: 189
          },
          {
            id: 'featured-3',
            title: 'Commercial Space in Piazza',
            price: 15000000,
            currency: 'ETB',
            beds: 0,
            baths: 2,
            sqm: 250,
            address: 'Piazza, Addis Ababa',
            lat: 9.0348,
            lng: 38.7297,
            propertyType: 'commercial',
            status: 'for_sale',
            listing_type: 'sale',
            description: 'Prime commercial space in busy area',
            images: ['https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=600&h=400&fit=crop'],
            is_featured: true,
            views: 156
          }
        ];
        setFeaturedProperties(mockFeaturedProperties.slice(0, limit));
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProperties();

    // Auto-refresh if enabled
    if (autoRefresh) {
      const interval = setInterval(fetchFeaturedProperties, 5 * 60 * 1000); // 5 minutes
      return () => clearInterval(interval);
    }
  }, [limit, autoRefresh]);

  const formatPrice = (property: Property) => {
    const sourceCurrency = property.currency || 'ETB';
    const convertedPrice = convertCurrency(property.price, sourceCurrency);
    return formatCurrency(convertedPrice);
  };

  if (loading) {
    return (
      <div className={`py-12 ${className}`}>
        {showHeader && (
          <div className="container mx-auto px-4 mb-8">
            <div className="text-center">
              <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
            </div>
          </div>
        )}
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: limit }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error && featuredProperties.length === 0) {
    return (
      <div className={`py-12 ${className}`}>
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-500">{translate('Unable to load featured properties', 'ተመራጭ ንብረቶችን መጫን አልተቻለም')}</p>
        </div>
      </div>
    );
  }

  return (
    <section className={`py-12 bg-gradient-to-b from-gray-50 to-white ${className}`}>
      {showHeader && (
        <div className="container mx-auto px-4 mb-12">
          <div className="text-center">
            <h2 className={`text-3xl md:text-4xl font-bold text-nibret-blue mb-4 ${language === 'am' ? 'amharic' : ''}`}>
              {translate('Featured Properties', 'ተመራጭ ንብረቶች')}
            </h2>
            <p className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'am' ? 'amharic' : ''}`}>
              {translate(
                'Discover our handpicked selection of premium properties',
                'የተመረጡ ፕሪሚየም ንብረቶቻችንን ይመልከቱ'
              )}
            </p>
          </div>
        </div>
      )}

      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredProperties.map((property) => (
            <Card key={property.id} className="group overflow-hidden hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
              <div className="relative h-48 overflow-hidden">
                <img
                  src={property.images?.[0] || 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=600&h=400&fit=crop'}
                  alt={property.title}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                  onError={(e) => {
                    e.currentTarget.src = 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=600&h=400&fit=crop';
                  }}
                />
                
                {/* Featured Badge */}
                {property.is_featured && (
                  <div className="absolute top-3 left-3">
                    <Badge className="bg-yellow-500 text-white shadow-lg">
                      <Star className="h-3 w-3 mr-1 fill-current" />
                      {translate('Featured', 'ተመራጭ')}
                    </Badge>
                  </div>
                )}

                {/* Views Badge */}
                {property.views && (
                  <div className="absolute top-3 right-3">
                    <Badge variant="secondary" className="bg-black/70 text-white">
                      <Eye className="h-3 w-3 mr-1" />
                      {property.views}
                    </Badge>
                  </div>
                )}

                {/* Price Overlay */}
                <div className="absolute bottom-3 left-3">
                  <Badge className="bg-nibret-blue text-white text-lg font-bold px-3 py-1">
                    {formatPrice(property)}
                  </Badge>
                </div>
              </div>

              <CardContent className="p-6">
                <div className="mb-3">
                  <h3 className={`text-xl font-semibold text-gray-900 mb-2 line-clamp-2 ${language === 'am' ? 'amharic' : ''}`}>
                    {property.title}
                  </h3>
                  <div className="flex items-center text-gray-600 mb-3">
                    <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                    <span className={`text-sm truncate ${language === 'am' ? 'amharic' : ''}`}>
                      {property.address}
                    </span>
                  </div>
                </div>

                {/* Property Tags */}
                <div className="mb-4">
                  <PropertyTags
                    propertyType={property.propertyType}
                    status={property.status}
                    listing_type={property.listing_type}
                    is_featured={property.is_featured}
                    size="sm"
                  />
                </div>

                {/* Property Stats */}
                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <div className="flex items-center">
                    <BedDouble className="h-4 w-4 mr-1" />
                    <span>{property.beds} {translate('beds', 'አልጋዎች')}</span>
                  </div>
                  <div className="flex items-center">
                    <Bath className="h-4 w-4 mr-1" />
                    <span>{property.baths} {translate('baths', 'መታጠቢያዎች')}</span>
                  </div>
                  <div className="flex items-center">
                    <SquareCode className="h-4 w-4 mr-1" />
                    <span>{property.sqm} {translate('sqm', 'ስኩየር ሜትር')}</span>
                  </div>
                </div>

                {/* View Details Button */}
                <Link to={`/property/${property.id}`}>
                  <Button className="w-full bg-nibret-blue hover:bg-nibret-blue/90 text-white group">
                    {translate('View Details', 'ዝርዝር ይመልከቱ')}
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Button */}
        {showHeader && (
          <div className="text-center mt-12">
            <Link to="/buy">
              <Button variant="outline" size="lg" className="border-nibret-blue text-nibret-blue hover:bg-nibret-blue hover:text-white">
                {translate('View All Properties', 'ሁሉንም ንብረቶች ይመልከቱ')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default FeaturedProperties;
