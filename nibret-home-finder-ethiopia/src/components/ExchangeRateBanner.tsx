import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, RefreshCw, Clock } from 'lucide-react';
import { currencyAPI, ExchangeRate } from '@/lib/api/currency';
import { useAppContext } from '@/context/AppContext';

const ExchangeRateBanner: React.FC = () => {
  const [exchangeRate, setExchangeRate] = useState<ExchangeRate | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastRate, setLastRate] = useState<number | null>(null);
  const { translate } = useAppContext();

  const fetchExchangeRate = async () => {
    try {
      setIsLoading(true);
      const rate = await currencyAPI.getExchangeRate();

      // Store previous rate for trend indication
      if (exchangeRate) {
        setLastRate(exchangeRate.rate);
      }

      setExchangeRate(rate);
    } catch (error) {
      console.error('Failed to fetch exchange rate:', error);
      // Don't show fallback rate - hide banner instead when rate is unavailable
      if (!exchangeRate) {
        setExchangeRate(null);
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchExchangeRate();
    
    // Refresh every 30 minutes
    const interval = setInterval(fetchExchangeRate, 30 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  const getTrendIcon = () => {
    if (!lastRate || !exchangeRate) return null;
    
    if (exchangeRate.rate > lastRate) {
      return <TrendingUp className="w-3 h-3 text-green-400" />;
    } else if (exchangeRate.rate < lastRate) {
      return <TrendingDown className="w-3 h-3 text-red-400" />;
    }
    return null;
  };

  const formatTime = (isoString: string) => {
    try {
      const date = new Date(isoString);
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } catch {
      return 'N/A';
    }
  };

  // Don't show banner if no valid exchange rate or if using fallback rate
  if (!exchangeRate || exchangeRate.source === 'Fallback Rate' || exchangeRate.source === 'Last Known Rate (Fixer.io)') {
    return null;
  }

  return (
    <div className="bg-black text-white py-1 px-4 border-b border-gray-800">
      <div className="container mx-auto flex items-center justify-between">
        {/* Exchange Rate Info */}
        <div className="flex items-center space-x-4 text-xs">
          <div className="flex items-center space-x-2">
            <span className="text-gray-300">
              {translate('USD/ETB:', 'ዶላር/ብር:')}
            </span>
            <span className="font-mono font-semibold text-white">
              {exchangeRate.rate.toFixed(2)}
            </span>
            {getTrendIcon()}
          </div>
          
          <div className="hidden sm:flex items-center space-x-1 text-gray-400">
            <Clock className="w-3 h-3" />
            <span>{formatTime(exchangeRate.lastUpdated)}</span>
          </div>

          <div className="hidden md:flex items-center space-x-1 text-gray-400">
            <span className="text-xs">
              {translate('Source:', 'ምንጭ:')} {exchangeRate.source}
            </span>
          </div>
          
          <div className="hidden md:flex items-center text-gray-400">
            <span className="text-xs">
              {translate('Source:', 'ምንጭ:')} {exchangeRate.source}
            </span>
          </div>
        </div>

        {/* Quick Conversion Examples */}
        <div className="hidden lg:flex items-center space-x-6 text-xs">
          <div className="flex items-center space-x-2">
            <span className="text-gray-300">$1 =</span>
            <span className="font-mono text-white">
              {exchangeRate.rate.toFixed(2)} ETB
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-gray-300">$100 =</span>
            <span className="font-mono text-white">
              {(exchangeRate.rate * 100).toLocaleString()} ETB
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-gray-300">$1,000 =</span>
            <span className="font-mono text-white">
              {(exchangeRate.rate * 1000).toLocaleString()} ETB
            </span>
          </div>
        </div>

        {/* Refresh Button */}
        <button
          onClick={fetchExchangeRate}
          disabled={isLoading}
          className="flex items-center space-x-1 text-gray-400 hover:text-white transition-colors duration-200 text-xs"
          title={translate('Refresh exchange rate', 'የምንዛሬ ተመን አድስ')}
        >
          <RefreshCw className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
          <span className="hidden sm:inline">
            {translate('Refresh', 'አድስ')}
          </span>
        </button>
      </div>
    </div>
  );
};

export default ExchangeRateBanner;
