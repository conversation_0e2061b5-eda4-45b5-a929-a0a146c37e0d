import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { currencyAPI } from '@/lib/api/currency';
import { useAppContext } from '@/context/AppContext';
import FixerAdmin from './FixerAdmin';
import {
  RefreshCw,
  Settings,
  TrendingUp,
  Clock,
  AlertTriangle,
  CheckCircle,
  X,
  Save,
  Zap
} from 'lucide-react';

const ExchangeRateAdmin: React.FC = () => {
  const { translate } = useAppContext();
  const [currentRate, setCurrentRate] = useState<any>(null);
  const [manualRate, setManualRate] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [rateInfo, setRateInfo] = useState<any>(null);
  const [apiHealth, setApiHealth] = useState<{ [key: string]: boolean }>({});

  const fetchCurrentRate = async () => {
    setIsLoading(true);
    try {
      const [rate, info, health] = await Promise.all([
        currencyAPI.getExchangeRate(),
        currencyAPI.getCurrentRateInfo(),
        currencyAPI.checkAPIHealth()
      ]);
      
      setCurrentRate(rate);
      setRateInfo(info);
      setApiHealth(health);
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to fetch exchange rate' });
    } finally {
      setIsLoading(false);
    }
  };

  const setManualExchangeRate = () => {
    const rate = parseFloat(manualRate);
    if (isNaN(rate) || rate <= 0) {
      setMessage({ type: 'error', text: 'Please enter a valid exchange rate' });
      return;
    }

    if (rate < 55 || rate > 85) {
      setMessage({ type: 'error', text: 'Exchange rate seems unrealistic (should be between 55-85 ETB/USD)' });
      return;
    }

    currencyAPI.setManualRate(rate, 'Admin Manual Override');
    setMessage({ type: 'success', text: `Manual exchange rate set to ${rate} ETB/USD` });
    fetchCurrentRate();
    setManualRate('');
  };

  const clearManualRate = () => {
    currencyAPI.clearManualRate();
    setMessage({ type: 'success', text: 'Manual exchange rate override cleared' });
    fetchCurrentRate();
  };

  const refreshRate = async () => {
    currencyAPI.clearCache();
    await fetchCurrentRate();
    setMessage({ type: 'success', text: 'Exchange rate refreshed' });
  };

  useEffect(() => {
    fetchCurrentRate();
  }, []);

  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  const formatTime = (isoString: string) => {
    try {
      return new Date(isoString).toLocaleString();
    } catch {
      return 'N/A';
    }
  };

  const getStatusBadge = () => {
    if (rateInfo?.isManual) {
      return <Badge variant="secondary" className="bg-orange-100 text-orange-800">Manual Override</Badge>;
    }
    if (rateInfo?.isCached) {
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Cached</Badge>;
    }
    return <Badge variant="secondary" className="bg-green-100 text-green-800">Live</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Fixer.io API Management */}
      <FixerAdmin />

      {/* Current Rate Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              {translate('Exchange Rate Status', 'የምንዛሬ ተመን ሁኔታ')}
            </span>
            <Button
              onClick={refreshRate}
              disabled={isLoading}
              size="sm"
              variant="outline"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              {translate('Refresh', 'አድስ')}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {currentRate && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {currentRate.rate} ETB
                </div>
                <div className="text-sm text-gray-600">per 1 USD</div>
                {getStatusBadge()}
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-800">
                  {currentRate.source}
                </div>
                <div className="text-sm text-gray-600">Source</div>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-800 flex items-center justify-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {formatTime(currentRate.lastUpdated)}
                </div>
                <div className="text-sm text-gray-600">Last Updated</div>
              </div>
            </div>
          )}

          {rateInfo && (
            <div className="text-sm text-gray-600 space-y-1">
              <div>Cache Age: {Math.round(rateInfo.cacheAge / 1000 / 60)} minutes</div>
              <div>Manual Override: {rateInfo.isManual ? 'Yes' : 'No'}</div>
              <div>Using Cache: {rateInfo.isCached ? 'Yes' : 'No'}</div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* API Health Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            {translate('API Health Status', 'API ጤንነት ሁኔታ')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {Object.entries(apiHealth).map(([apiName, isHealthy]) => (
              <div key={apiName} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">{apiName}</span>
                <div className="flex items-center">
                  {isHealthy ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <AlertTriangle className="w-5 h-5 text-red-500" />
                  )}
                  <span className={`ml-2 text-sm ${isHealthy ? 'text-green-600' : 'text-red-600'}`}>
                    {isHealthy ? 'Healthy' : 'Down'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Manual Rate Override */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              {translate('Manual Rate Override', 'ተመን በእጅ ማስተካከያ')}
            </span>
            {rateInfo?.isManual && (
              <Button
                onClick={clearManualRate}
                size="sm"
                variant="outline"
                className="text-red-600 hover:text-red-700"
              >
                <X className="w-4 h-4 mr-2" />
                {translate('Clear Override', 'ማስተካከያ አጥፋ')}
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-3">
            <Input
              type="number"
              placeholder="Enter exchange rate (e.g., 67.5)"
              value={manualRate}
              onChange={(e) => setManualRate(e.target.value)}
              step="0.1"
              min="55"
              max="85"
            />
            <Button
              onClick={setManualExchangeRate}
              disabled={!manualRate}
            >
              <Save className="w-4 h-4 mr-2" />
              {translate('Set Rate', 'ተመን ያስቀምጡ')}
            </Button>
          </div>
          
          <div className="text-sm text-gray-600">
            {translate(
              'Use this to manually set the exchange rate when APIs are not working properly. Rate should be between 55-85 ETB/USD.',
              'APIs በትክክል ሲሰሩ ያልሆነ ጊዜ የምንዛሬ ተመንን በእጅ ለማስተካከል ይህንን ይጠቀሙ። ተመኑ ከ55-85 ብር/ዶላር መካከል መሆን አለበት።'
            )}
          </div>
        </CardContent>
      </Card>

      {/* Messages */}
      {message && (
        <Alert className={message.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
          <AlertDescription className={message.type === 'error' ? 'text-red-800' : 'text-green-800'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default ExchangeRateAdmin;
