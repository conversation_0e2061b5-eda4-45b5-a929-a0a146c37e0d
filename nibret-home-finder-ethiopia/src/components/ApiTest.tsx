import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { propertyApi, authApi } from '@/lib/api';
import { Loader2 } from 'lucide-react';

const ApiTest = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Auth test credentials
  const [username, setUsername] = useState('0965789832');
  const [password, setPassword] = useState('nibretadmin');

  const testPropertiesAPI = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing properties API...');
      const properties = await propertyApi.getProperties();
      console.log('Properties response:', properties);
      setResult(properties);
    } catch (err: any) {
      console.error('API Test Error:', err);
      setError(err.message || 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const testLoginAPI = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing login API...');
      const response = await authApi.login({
        username: username,
        password: password
      });
      console.log('Login response:', response);
      setResult(response);
    } catch (err: any) {
      console.error('Login Test Error:', err);
      setError(err.message || 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const testRegistrationAPI = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing registration API...');
      const testUser = {
        first_name: 'Test',
        last_name: 'User',
        email: '<EMAIL>',
        phone: '0911234567',
        password: 'testpassword123'
      };

      const response = await authApi.register(testUser);
      console.log('Registration response:', response);
      setResult(response);
    } catch (err: any) {
      console.error('Registration Test Error:', err);
      setError(err.message || 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>API Connection Test</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="auth" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="auth">Authentication</TabsTrigger>
            <TabsTrigger value="properties">Properties</TabsTrigger>
            <TabsTrigger value="results">Results</TabsTrigger>
          </TabsList>

          <TabsContent value="auth" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Enter username"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={testLoginAPI}
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Testing Login...
                  </>
                ) : (
                  'Test Login API'
                )}
              </Button>

              <Button
                onClick={testRegistrationAPI}
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Testing Registration...
                  </>
                ) : (
                  'Test Registration API'
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="properties" className="space-y-4">
            <Button
              onClick={testPropertiesAPI}
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing Properties API...
                </>
              ) : (
                'Test Properties API'
              )}
            </Button>
          </TabsContent>

          <TabsContent value="results" className="space-y-4">
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <h4 className="font-semibold text-red-800">Error:</h4>
                <p className="text-red-700">{error}</p>
              </div>
            )}

            {result && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                <h4 className="font-semibold text-green-800">Success!</h4>
                <p className="text-green-700">
                  {Array.isArray(result)
                    ? `Received ${result.length} items`
                    : result.access_token
                      ? 'Authentication successful!'
                      : 'Request completed successfully'
                  }
                </p>
                <details className="mt-2">
                  <summary className="cursor-pointer text-green-800 font-medium">
                    View Response Data
                  </summary>
                  <pre className="mt-2 p-2 bg-white border rounded text-xs overflow-auto max-h-40">
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ApiTest;
