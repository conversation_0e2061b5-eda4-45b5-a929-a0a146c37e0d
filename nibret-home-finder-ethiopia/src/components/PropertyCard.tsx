
import React, { useContext, useState } from 'react';
import { useAppContext } from '../context/AppContext';
import { AuthContext } from '../App';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Lock, Star, ChevronLeft, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Property } from '@/lib/api';
import AuthModal from './AuthModal';
import PropertyTags from './PropertyTags';
import PriceDisplay from './PriceDisplay';
import PropertyPreviewGate from './PropertyPreviewGate';

interface PropertyCardProps {
  property: Property;
  showSlideshow?: boolean;
}

const PropertyCard = ({ property, showSlideshow = false }: PropertyCardProps) => {
  const { currency, convertCurrency, formatCurrency, language, translate } = useAppContext();
  const { isAuthenticated } = useContext(AuthContext);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Add a check to prevent destructuring when property is undefined
  if (!property) {
    console.error('Property is undefined in PropertyCard');
    return null;
  }

  const {
    id,
    title,
    price,
    currency: propertyCurrency,
    beds,
    baths,
    sqm,
    sqft, // For backward compatibility
    address,
    images
  } = property;

  // Use images array for slideshow or fallback
  const propertyImages = images && images.length > 0 ? images : ['https://picsum.photos/600/400'];
  const currentImage = propertyImages[currentImageIndex];
  const hasMultipleImages = propertyImages.length > 1;

  // Format price with proper currency conversion
  const formattedPrice = () => {
    // Use property's currency if available, otherwise fall back to ETB
    const sourceCurrency = propertyCurrency || 'ETB';

    // Convert the price from the property's currency to the user's selected currency
    const convertedPrice = convertCurrency(price, sourceCurrency);

    // Format the converted price in the user's selected currency
    return formatCurrency(convertedPrice);
  };

  // Slideshow navigation functions
  const nextImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev + 1) % propertyImages.length);
  };

  const prevImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev - 1 + propertyImages.length) % propertyImages.length);
  };

  const goToImage = (index: number, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex(index);
  };

  // Handle card click
  const handleCardClick = (e: React.MouseEvent) => {
    if (!isAuthenticated) {
      e.preventDefault();
      setShowAuthModal(true);
    }
    // If authenticated, the Link will handle navigation
  };

  // Get display address based on authentication
  const getDisplayAddress = () => {
    if (isAuthenticated) {
      return address;
    }
    // Hide specific address, show only general area
    const parts = address.split(',');
    if (parts.length > 1) {
      return `${translate('General Area', 'አጠቃላይ አካባቢ')}, ${parts[parts.length - 1].trim()}`;
    }
    return translate('Location Hidden - Sign in to view', 'አድራሻ ተደብቋል - ለማየት ይግቡ');
  };

  return (
    <>
      <Link to={`/property/${id}`} onClick={handleCardClick}>
        <Card className="property-card overflow-hidden h-full hover:shadow-lg transition-shadow">
          <div className="relative h-48 overflow-hidden group">
            <img
              src={currentImage}
              alt={title}
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
            />

            {/* Slideshow Controls - Only show if slideshow is enabled and has multiple images */}
            {showSlideshow && hasMultipleImages && (
              <>
                {/* Previous Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* Next Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>

                {/* Image Indicators */}
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                  {propertyImages.map((_, index) => (
                    <button
                      key={index}
                      className={`w-2 h-2 rounded-full transition-all ${
                        index === currentImageIndex
                          ? 'bg-white'
                          : 'bg-white/50 hover:bg-white/75'
                      }`}
                      onClick={(e) => goToImage(index, e)}
                    />
                  ))}
                </div>

                {/* Image Counter */}
                <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded-md text-xs">
                  {currentImageIndex + 1} / {propertyImages.length}
                </div>
              </>
            )}

            {/* Featured Badge */}
            {property.is_featured && (
              <div className={`absolute top-2 ${showSlideshow && hasMultipleImages ? 'right-2' : 'left-2'}`}>
                <Badge className="bg-yellow-500 text-white shadow-lg">
                  <Star className="h-3 w-3 mr-1 fill-current" />
                  {translate('Featured', 'ተመራጭ')}
                </Badge>
              </div>
            )}

            {!isAuthenticated && (
              <div className={`absolute ${showSlideshow && hasMultipleImages ? 'top-12' : 'top-2'} right-2 bg-black/70 text-white px-2 py-1 rounded-md text-xs flex items-center`}>
                <Lock className="h-3 w-3 mr-1" />
                {translate('Sign in to view', 'ለማየት ይግቡ')}
              </div>
            )}
          </div>
          <CardContent className="pt-4">
            <div className="flex justify-between items-start">
              <h3 className={`text-lg font-semibold mb-1 ${language === 'am' ? 'amharic' : ''}`}>
                {title}
              </h3>
              <PriceDisplay
                price={price}
                originalPrice={property.original_price}
                discountPercentage={property.discount_percentage}
                isNegotiable={property.is_negotiable}
                currency={propertyCurrency}
                size="sm"
                orientation="vertical"
                className="text-right"
              />
            </div>
            <div className="flex items-center text-gray-500 mb-2">
              {isAuthenticated ? (
                <MapPin className="h-4 w-4 mr-1" />
              ) : (
                <Lock className="h-4 w-4 mr-1" />
              )}
              <p className={`text-sm ${language === 'am' ? 'amharic' : ''} ${!isAuthenticated ? 'italic text-gray-400' : ''}`}>
                {getDisplayAddress()}
              </p>
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-600">
              {beds && beds > 0 ? (
                <span>{beds} {translate('beds', 'አልጋዎች')}</span>
              ) : (
                <span className="text-gray-400">-</span>
              )}
              {baths && baths > 0 ? (
                <span>{baths} {translate('baths', 'መታጠቢያዎች')}</span>
              ) : (
                <span className="text-gray-400">-</span>
              )}
              {(sqm && sqm > 0) || (sqft && sqft > 0) ? (
                <span>{sqm || sqft} {translate('sqm', 'ስኩየር ሜትር')}</span>
              ) : (
                <span className="text-gray-400">-</span>
              )}
            </div>

            {/* Property Tags */}
            <div className="mt-3">
              <PropertyTags
                propertyType={property.propertyType}
                status={property.status}
                listing_type={property.listing_type}
                is_featured={property.is_featured}
                size="sm"
              />
            </div>

            {!isAuthenticated && (
              <div className="mt-3 text-center">
                <p className="text-xs text-gray-500">
                  {translate('Sign in to view full details', 'ሙሉ ዝርዝሮችን ለማየት ይግቡ')}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </Link>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        defaultTab="login"
      />
    </>
  );
};

export default PropertyCard;
