
import React, { useState, useContext, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAppContext } from '../context/AppContext';
import { Button } from '@/components/ui/button';
import {
  MapPin,
  Menu,
  X,
  LayoutDashboard,
  Upload,
  Gavel,
  User,
  LogOut,
  ChevronDown
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { authApi, User as UserType } from '@/lib/api';
import LanguageSwitcher from './LanguageSwitcher';
import CurrencySwitcher from './CurrencySwitcher';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger
} from "@/components/ui/navigation-menu";
import { cn } from '@/lib/utils';
import { AuthContext } from '../App';

const Navbar = () => {
  const { translate } = useAppContext();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const { isAuthenticated, currentUser, logout } = useContext(AuthContext);

  const handleLogout = () => {
    authApi.logout();
    logout();
    setIsMenuOpen(false);
  };

  const isActive = (path: string) => location.pathname === path;

  const navigationLinks = [
    { name: translate('Home', 'መነሻ'), path: '/' },
    { name: translate('Buy', 'ይግዙ'), path: '/buy' },
    { name: translate('Rent', 'ኪራይ'), path: '/rent' },
    { name: translate('Sell', 'ይሽጡ'), path: '/sell' },
    { name: translate('Auctions', 'ጨረታዎች'), path: '/auctions' },
    { name: translate('Mortgage', 'ብድር'), path: '/loans' }
  ];

  return (
    <nav className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <img src="/logo.png" alt="Nibret" className="h-12 w-12" />
            <span className="text-2xl font-bold text-nibret-blue">Nibret</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:space-x-6">
            <NavigationMenu>
              <NavigationMenuList>
                {navigationLinks.map((link) => (
                  <NavigationMenuItem key={link.path}>
                    <Link to={link.path}>
                      <NavigationMenuLink
                        className={cn(
                          "inline-flex items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium transition-colors",
                          isActive(link.path)
                            ? "text-nibret-blue border-b-2 border-nibret-blue"
                            : "text-gray-700 hover:text-nibret-blue"
                        )}
                      >
                        {link.name}
                      </NavigationMenuLink>
                    </Link>
                  </NavigationMenuItem>
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Right side items */}
          <div className="flex items-center space-x-4">
            <CurrencySwitcher />
            <LanguageSwitcher />

            {/* Desktop buttons */}
            <div className="hidden md:flex md:items-center md:space-x-2">
              {/* Upload dropdown - Only for Admin users */}
              {isAuthenticated && currentUser?.role === 'ADMIN' && (
                <NavigationMenu>
                  <NavigationMenuList>
                    <NavigationMenuItem>
                      <NavigationMenuTrigger className="bg-nibret-gold hover:bg-nibret-gold/90 text-white">
                        <Upload className="mr-2 h-4 w-4" />
                        {translate('Upload', 'ይላኩ')}
                      </NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <div className="grid gap-3 p-4 w-64">
                          <Link
                            to="/upload-property"
                            className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          >
                            <div className="text-sm font-medium leading-none">
                              {translate('Upload Property', 'ንብረት ይላኩ')}
                            </div>
                            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              {translate('List your property for sale', 'ንብረትዎን ለሽያጭ ዝርዝር ውስጥ ያስገቡ')}
                            </p>
                          </Link>
                          <Link
                            to="/auction-upload"
                            className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          >
                            <div className="text-sm font-medium leading-none">
                              {translate('Auction Upload', 'መጨረሻ ይላኩ')}
                            </div>
                            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              {translate('List your property for auction', 'ንብረትዎን ለመጨረሻ ዝርዝር ውስጥ ያስገቡ')}
                            </p>
                          </Link>
                        </div>
                      </NavigationMenuContent>
                    </NavigationMenuItem>
                  </NavigationMenuList>
                </NavigationMenu>
              )}

              {isAuthenticated && currentUser ? (
                <>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="font-medium">
                        <User className="mr-2 h-4 w-4" />
                        {currentUser.first_name}
                        <ChevronDown className="ml-2 h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <div className="px-2 py-1.5 text-sm font-medium">
                        {currentUser.first_name} {currentUser.last_name}
                      </div>
                      <div className="px-2 py-1.5 text-xs text-muted-foreground">
                        {currentUser.email}
                      </div>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link to="/dashboard" className="w-full">
                          <User className="mr-2 h-4 w-4" />
                          {translate('Dashboard', 'ዳሽቦርድ')}
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link to="/my-properties" className="w-full">
                          <LayoutDashboard className="mr-2 h-4 w-4" />
                          {translate('My Properties', 'የእኔ ንብረቶች')}
                        </Link>
                      </DropdownMenuItem>
                      {currentUser.role === 'ADMIN' && (
                        <DropdownMenuItem asChild>
                          <Link to="/admin" className="w-full">
                            <LayoutDashboard className="mr-2 h-4 w-4" />
                            {translate('Admin Panel', 'የአስተዳዳሪ ፓነል')}
                          </Link>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                        <LogOut className="mr-2 h-4 w-4" />
                        {translate('Sign Out', 'ይውጡ')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              ) : (
                <>
                  <Button asChild variant="outline" className="font-medium">
                    <Link to="/login">{translate('Sign In', 'ይግቡ')}</Link>
                  </Button>
                  <Button asChild className="bg-nibret-blue hover:bg-nibret-blue/90 font-medium">
                    <Link to="/register">{translate('Register', 'ይመዝገቡ')}</Link>
                  </Button>
                </>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button variant="ghost" size="icon" onClick={() => setIsMenuOpen(!isMenuOpen)}>
                {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white py-4 px-6 shadow-lg animate-fade-in">
          <div className="flex flex-col space-y-4">
            {navigationLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className={cn(
                  "text-lg py-2 border-b border-gray-100",
                  isActive(link.path) ? "text-nibret-blue font-medium" : "text-gray-700"
                )}
                onClick={() => setIsMenuOpen(false)}
              >
                {link.name}
              </Link>
            ))}

            {/* Upload links - Only for Admin users */}
            {isAuthenticated && currentUser?.role === 'ADMIN' && (
              <div className="border-t pt-4">
                <Link
                  to="/upload-property"
                  className="block text-lg py-2 text-gray-700 border-b border-gray-100"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {translate('Upload Property', 'ንብረት ይላኩ')}
                </Link>
                <Link
                  to="/auction-upload"
                  className="block text-lg py-2 text-gray-700 border-b border-gray-100"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {translate('Auction Upload', 'መጨረሻ ይላኩ')}
                </Link>
              </div>
            )}

            {/* Mobile Currency and Language Switchers */}
            <div className="border-t pt-4 flex justify-center space-x-4">
              <CurrencySwitcher />
              <LanguageSwitcher />
            </div>

            <div className="pt-4 flex flex-col space-y-3">
              {isAuthenticated && currentUser ? (
                <>
                  <div className="px-3 py-2 bg-gray-50 rounded-md">
                    <div className="text-sm font-medium text-gray-900">
                      {currentUser.first_name} {currentUser.last_name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {currentUser.email}
                    </div>
                  </div>
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/dashboard" onClick={() => setIsMenuOpen(false)}>
                      <User className="mr-2 h-4 w-4" />
                      {translate('Dashboard', 'ዳሽቦርድ')}
                    </Link>
                  </Button>
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/my-properties" onClick={() => setIsMenuOpen(false)}>
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      {translate('My Properties', 'የእኔ ንብረቶች')}
                    </Link>
                  </Button>
                  {currentUser.role === 'ADMIN' && (
                    <Button asChild className="w-full bg-nibret-blue hover:bg-nibret-blue/90">
                      <Link to="/admin" onClick={() => setIsMenuOpen(false)}>
                        <LayoutDashboard className="mr-2 h-4 w-4" />
                        {translate('Admin Panel', 'የአስተዳዳሪ ፓነል')}
                      </Link>
                    </Button>
                  )}
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    className="w-full text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    {translate('Sign Out', 'ይውጡ')}
                  </Button>
                </>
              ) : (
                <>
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/login" onClick={() => setIsMenuOpen(false)}>
                      {translate('Sign In', 'ይግቡ')}
                    </Link>
                  </Button>
                  <Button asChild className="w-full bg-nibret-blue hover:bg-nibret-blue/90">
                    <Link to="/register" onClick={() => setIsMenuOpen(false)}>
                      {translate('Register', 'ይመዝገቡ')}
                    </Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
