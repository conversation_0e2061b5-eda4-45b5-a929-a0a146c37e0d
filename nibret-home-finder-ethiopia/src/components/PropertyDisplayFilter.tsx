import React, { useState, useEffect } from 'react';
import { Filter, Eye, Star, TrendingUp, Clock, DollarSign, Home, Building, MapPin, Wrench } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAppContext } from '@/context/AppContext';

export type PropertyDisplayOption =
  | 'latest'
  | 'featured'
  | 'most_viewed'
  | 'price_low_high'
  | 'price_high_low'
  | 'for_sale'
  | 'for_rent'
  | 'luxury'
  | 'affordable'
  | 'apartments_only'
  | 'houses_only'
  | 'commercial'
  | 'new_construction'
  | 'furnished'
  | 'location_bole'
  | 'location_megenagna'
  | 'location_kazanchis';

export interface PropertyDisplayFilterProps {
  selectedFilter: PropertyDisplayOption;
  onFilterChange: (filter: PropertyDisplayOption) => void;
  propertyCount?: number;
  className?: string;
}

const FILTER_OPTIONS: Array<{
  value: PropertyDisplayOption;
  label: string;
  labelAm: string;
  icon: React.ReactNode;
  description: string;
  descriptionAm: string;
}> = [
  {
    value: 'latest',
    label: 'Latest Properties',
    labelAm: 'አዳዲስ ንብረቶች',
    icon: <Clock className="w-4 h-4" />,
    description: 'Recently added properties',
    descriptionAm: 'በቅርቡ የተጨመሩ ንብረቶች'
  },
  {
    value: 'featured',
    label: 'Featured Properties',
    labelAm: 'ተመራጭ ንብረቶች',
    icon: <Star className="w-4 h-4" />,
    description: 'Hand-picked premium properties',
    descriptionAm: 'በእጅ የተመረጡ ፕሪሚየም ንብረቶች'
  },
  {
    value: 'most_viewed',
    label: 'Most Viewed',
    labelAm: 'በብዛት የታዩ',
    icon: <Eye className="w-4 h-4" />,
    description: 'Popular properties with high views',
    descriptionAm: 'ከፍተኛ እይታ ያላቸው ታዋቂ ንብረቶች'
  },
  {
    value: 'price_low_high',
    label: 'Price: Low to High',
    labelAm: 'ዋጋ: ዝቅተኛ ወደ ከፍተኛ',
    icon: <TrendingUp className="w-4 h-4" />,
    description: 'Affordable options first',
    descriptionAm: 'ተመጣጣኝ አማራጮች በመጀመሪያ'
  },
  {
    value: 'price_high_low',
    label: 'Price: High to Low',
    labelAm: 'ዋጋ: ከፍተኛ ወደ ዝቅተኛ',
    icon: <DollarSign className="w-4 h-4" />,
    description: 'Premium properties first',
    descriptionAm: 'ፕሪሚየም ንብረቶች በመጀመሪያ'
  },
  {
    value: 'for_sale',
    label: 'For Sale Only',
    labelAm: 'ለሽያጭ ብቻ',
    icon: <Home className="w-4 h-4" />,
    description: 'Properties available for purchase',
    descriptionAm: 'ለግዢ የሚገኙ ንብረቶች'
  },
  {
    value: 'for_rent',
    label: 'For Rent Only',
    labelAm: 'ለኪራይ ብቻ',
    icon: <Home className="w-4 h-4" />,
    description: 'Properties available for rent',
    descriptionAm: 'ለኪራይ የሚገኙ ንብረቶች'
  },
  {
    value: 'luxury',
    label: 'Luxury Properties',
    labelAm: 'የቅንጦት ንብረቶች',
    icon: <Star className="w-4 h-4" />,
    description: 'High-end luxury properties',
    descriptionAm: 'ከፍተኛ ደረጃ የቅንጦት ንብረቶች'
  },
  {
    value: 'affordable',
    label: 'Affordable Properties',
    labelAm: 'ተመጣጣኝ ንብረቶች',
    icon: <DollarSign className="w-4 h-4" />,
    description: 'Budget-friendly options',
    descriptionAm: 'በበጀት ተመጣጣኝ አማራጮች'
  },
  {
    value: 'apartments_only',
    label: 'Apartments Only',
    labelAm: 'አፓርትመንቶች ብቻ',
    icon: <Building className="w-4 h-4" />,
    description: 'Show only apartment properties',
    descriptionAm: 'የአፓርትመንት ንብረቶችን ብቻ አሳይ'
  },
  {
    value: 'houses_only',
    label: 'Houses & Villas Only',
    labelAm: 'ቤቶች እና ቪላዎች ብቻ',
    icon: <Home className="w-4 h-4" />,
    description: 'Show only houses and villas',
    descriptionAm: 'ቤቶችን እና ቪላዎችን ብቻ አሳይ'
  },
  {
    value: 'commercial',
    label: 'Commercial Properties',
    labelAm: 'የንግድ ንብረቶች',
    icon: <Building className="w-4 h-4" />,
    description: 'Office spaces and commercial buildings',
    descriptionAm: 'የቢሮ ቦታዎች እና የንግድ ህንጻዎች'
  },
  {
    value: 'new_construction',
    label: 'New Construction',
    labelAm: 'አዲስ ግንባታ',
    icon: <Wrench className="w-4 h-4" />,
    description: 'Recently built properties',
    descriptionAm: 'በቅርቡ የተገነቡ ንብረቶች'
  },
  {
    value: 'furnished',
    label: 'Furnished Properties',
    labelAm: 'የተዘጋጁ ንብረቶች',
    icon: <Star className="w-4 h-4" />,
    description: 'Fully furnished and ready to move in',
    descriptionAm: 'ሙሉ በሙሉ የተዘጋጁ እና ለመግባት ዝግጁ'
  },
  {
    value: 'location_bole',
    label: 'Bole Area',
    labelAm: 'ቦሌ አካባቢ',
    icon: <MapPin className="w-4 h-4" />,
    description: 'Properties in Bole district',
    descriptionAm: 'በቦሌ ወረዳ ያሉ ንብረቶች'
  },
  {
    value: 'location_megenagna',
    label: 'Megenagna Area',
    labelAm: 'መገናኛ አካባቢ',
    icon: <MapPin className="w-4 h-4" />,
    description: 'Properties in Megenagna area',
    descriptionAm: 'በመገናኛ አካባቢ ያሉ ንብረቶች'
  },
  {
    value: 'location_kazanchis',
    label: 'Kazanchis Area',
    labelAm: 'ካዛንቺስ አካባቢ',
    icon: <MapPin className="w-4 h-4" />,
    description: 'Properties in Kazanchis area',
    descriptionAm: 'በካዛንቺስ አካባቢ ያሉ ንብረቶች'
  }
];

const PropertyDisplayFilter: React.FC<PropertyDisplayFilterProps> = ({
  selectedFilter,
  onFilterChange,
  propertyCount = 0,
  className = ''
}) => {
  const { translate, language } = useAppContext();
  const [isOpen, setIsOpen] = useState(false);

  const selectedOption = FILTER_OPTIONS.find(option => option.value === selectedFilter);

  const handleFilterChange = (value: string) => {
    onFilterChange(value as PropertyDisplayOption);
    setIsOpen(false);
  };

  return (
    <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 py-4 px-4 shadow-sm ${className}`}>
      <div className="container mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-100 p-2 rounded-lg">
              <Filter className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800">
                {translate('Property Display Options', 'የንብረት ማሳያ አማራጮች')}
              </h3>
              <p className="text-sm text-gray-600">
                {translate('Choose what properties to show in the top 3 rows', 'በላይኛው 3 ረድፎች ውስጥ የትኞቹን ንብረቶች እንደሚያሳዩ ይምረጡ')}
              </p>
            </div>
          </div>

          {/* Property Count & Status */}
          <div className="flex items-center space-x-3">
            {propertyCount > 0 && (
              <Badge variant="default" className="bg-blue-600 text-white px-3 py-1">
                {propertyCount} {translate('properties', 'ንብረቶች')}
              </Badge>
            )}
            <div className="hidden md:flex items-center space-x-2 text-sm text-blue-700 bg-blue-100 px-3 py-1 rounded-full">
              <Eye className="w-4 h-4" />
              <span>
                {translate('Top 3 rows visible', 'ከላይ 3 ረድፎች ይታያሉ')}
              </span>
            </div>
          </div>
        </div>

        {/* Filter Selection */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700 whitespace-nowrap">
              {translate('Display:', 'አሳይ:')}
            </span>
          </div>

          <Select value={selectedFilter} onValueChange={handleFilterChange}>
            <SelectTrigger className="w-full sm:w-[300px] bg-white border-blue-200 focus:border-blue-400 focus:ring-blue-400">
              <SelectValue>
                <div className="flex items-center space-x-2">
                  {selectedOption?.icon}
                  <span className="text-sm font-medium">
                    {language === 'am' ? selectedOption?.labelAm : selectedOption?.label}
                  </span>
                </div>
              </SelectValue>
            </SelectTrigger>
            <SelectContent className="max-h-[400px]">
              {/* Group by category */}
              <div className="p-2">
                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                  {translate('Sort & Filter', 'ደርድር እና ማጣሪያ')}
                </div>
                {FILTER_OPTIONS.slice(0, 9).map((option) => (
                  <SelectItem key={option.value} value={option.value} className="py-2">
                    <div className="flex items-center space-x-3">
                      <div className="text-blue-600">
                        {option.icon}
                      </div>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">
                          {language === 'am' ? option.labelAm : option.label}
                        </span>
                        <span className="text-xs text-gray-500">
                          {language === 'am' ? option.descriptionAm : option.description}
                        </span>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </div>

              <div className="border-t border-gray-200 p-2">
                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                  {translate('Property Type', 'የንብረት አይነት')}
                </div>
                {FILTER_OPTIONS.slice(9, 13).map((option) => (
                  <SelectItem key={option.value} value={option.value} className="py-2">
                    <div className="flex items-center space-x-3">
                      <div className="text-green-600">
                        {option.icon}
                      </div>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">
                          {language === 'am' ? option.labelAm : option.label}
                        </span>
                        <span className="text-xs text-gray-500">
                          {language === 'am' ? option.descriptionAm : option.description}
                        </span>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </div>

              <div className="border-t border-gray-200 p-2">
                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                  {translate('Location', 'አካባቢ')}
                </div>
                {FILTER_OPTIONS.slice(13).map((option) => (
                  <SelectItem key={option.value} value={option.value} className="py-2">
                    <div className="flex items-center space-x-3">
                      <div className="text-purple-600">
                        {option.icon}
                      </div>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">
                          {language === 'am' ? option.labelAm : option.label}
                        </span>
                        <span className="text-xs text-gray-500">
                          {language === 'am' ? option.descriptionAm : option.description}
                        </span>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </div>
            </SelectContent>
          </Select>

          {/* Quick Filter Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedFilter === 'latest' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleFilterChange('latest')}
              className="text-xs"
            >
              <Clock className="w-3 h-3 mr-1" />
              {translate('Latest', 'አዳዲስ')}
            </Button>
            <Button
              variant={selectedFilter === 'featured' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleFilterChange('featured')}
              className="text-xs"
            >
              <Star className="w-3 h-3 mr-1" />
              {translate('Featured', 'ተመራጭ')}
            </Button>
            <Button
              variant={selectedFilter === 'luxury' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleFilterChange('luxury')}
              className="text-xs"
            >
              <DollarSign className="w-3 h-3 mr-1" />
              {translate('Luxury', 'ቅንጦት')}
            </Button>
          </div>
        </div>

        {/* Selected Filter Description */}
        {selectedOption && (
          <div className="mt-3 p-3 bg-white rounded-lg border border-blue-200">
            <div className="flex items-center space-x-2">
              <div className="text-blue-600">
                {selectedOption.icon}
              </div>
              <span className="font-medium text-gray-800">
                {language === 'am' ? selectedOption.labelAm : selectedOption.label}
              </span>
              <span className="text-gray-600">•</span>
              <span className="text-sm text-gray-600">
                {language === 'am' ? selectedOption.descriptionAm : selectedOption.description}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PropertyDisplayFilter;
