
import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Mail, Phone, Globe, Facebook, Instagram, Twitter, Linkedin } from 'lucide-react';
import { useAppContext } from '../context/AppContext';

const Footer = () => {
  const { translate } = useAppContext();

  return (
    <footer className="bg-nibret-dark text-white">
      {/* Main Footer */}
      <div className="container mx-auto px-6 pt-12 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and About */}
          <div className="md:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <MapPin className="h-8 w-8 text-nibret-gold" />
              <span className="text-2xl font-bold text-white">Nibret</span>
            </div>
            <p className="text-gray-300 mb-6">
              {translate(
                'Find your dream home in Ethiopia with Nibret, the premier real estate platform.',
                'በኢትዮጵያ ውስጥ የህልም ቤትዎን ይፈልጉ ከኒብረት ጋር፣ ቅድሚያ የሪል እስቴት መድረክ።'
              )}
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-nibret-gold transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-nibret-gold transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-nibret-gold transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-nibret-gold transition-colors">
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Globe size={18} className="mr-2 text-nibret-gold" />
              {translate('Quick Links', 'ፈጣን ማገናኛዎች')}
            </h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-nibret-gold transition-colors hover:translate-x-1 inline-block">
                  {translate('Home', 'መነሻ')}
                </Link>
              </li>
              <li>
                <Link to="/buy" className="text-gray-300 hover:text-nibret-gold transition-colors hover:translate-x-1 inline-block">
                  {translate('Buy', 'ይግዙ')}
                </Link>
              </li>
              <li>
                <Link to="/rent" className="text-gray-300 hover:text-nibret-gold transition-colors hover:translate-x-1 inline-block">
                  {translate('Rent', 'ኪራይ')}
                </Link>
              </li>
              <li>
                <Link to="/sell" className="text-gray-300 hover:text-nibret-gold transition-colors hover:translate-x-1 inline-block">
                  {translate('Sell', 'ይሽጡ')}
                </Link>
              </li>
              <li>
                <Link to="/loans" className="text-gray-300 hover:text-nibret-gold transition-colors hover:translate-x-1 inline-block">
                  {translate('Mortgage', 'ብድር')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Mail size={18} className="mr-2 text-nibret-gold" />
              {translate('Resources', 'ምንጮች')}
            </h3>
            <ul className="space-y-2">
              <li>
                <Link to="/guide" className="text-gray-300 hover:text-nibret-gold transition-colors hover:translate-x-1 inline-block">
                  {translate('Home Buying Guide', 'የቤት ግዢ መመሪያ')}
                </Link>
              </li>
              <li>
                <Link to="/market" className="text-gray-300 hover:text-nibret-gold transition-colors hover:translate-x-1 inline-block">
                  {translate('Market Trends', 'የገበያ ዝንባሌዎች')}
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-gray-300 hover:text-nibret-gold transition-colors hover:translate-x-1 inline-block">
                  {translate('FAQ', 'ተደጋግመው የሚጠየቁ')}
                </Link>
              </li>
              <li>
                <Link to="/blog" className="text-gray-300 hover:text-nibret-gold transition-colors hover:translate-x-1 inline-block">
                  {translate('Real Estate Blog', 'የሪል እስቴት ብሎግ')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Phone size={18} className="mr-2 text-nibret-gold" />
              {translate('Contact Us', 'ያግኙን')}
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <MapPin className="h-5 w-5 mr-2 text-nibret-gold flex-shrink-0 mt-0.5" />
                <span className="text-gray-300">
                  {translate('Bole, Addis Ababa', 'ቦሌ፣ አዲስ አበባ')}<br />
                  {translate('Ethiopia', 'ኢትዮጵያ')}
                </span>
              </li>
              <li className="flex items-center">
                <Phone className="h-5 w-5 mr-2 text-nibret-gold flex-shrink-0" />
                <a href="tel:+251111234567" className="text-gray-300 hover:text-nibret-gold transition-colors">
                  +251 11 123 4567
                </a>
              </li>
              <li className="flex items-center">
                <Mail className="h-5 w-5 mr-2 text-nibret-gold flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-nibret-gold transition-colors">
                  <EMAIL>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="bg-black/30 py-4">
        <div className="container mx-auto px-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-center text-gray-400 text-sm">
            &copy; {new Date().getFullYear()} Nibret. {translate('All rights reserved.', 'መብቱ በህግ የተጠበቀ ነው።')}
          </p>
          <div className="flex space-x-4 mt-2 md:mt-0">
            <a href="#" className="text-gray-400 hover:text-nibret-gold text-sm transition-colors">
              {translate('Privacy Policy', 'የግላዊነት ፖሊሲ')}
            </a>
            <a href="#" className="text-gray-400 hover:text-nibret-gold text-sm transition-colors">
              {translate('Terms of Service', 'የአገልግሎት ውሎች')}
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
