
import React from 'react';
import { Button } from '@/components/ui/button';
import { useAppContext } from '../context/AppContext';
import { DollarSign } from 'lucide-react';

const CurrencySwitcher = () => {
  const { currency, setCurrency } = useAppContext();

  const toggleCurrency = () => {
    setCurrency(currency === 'USD' ? 'ETB' : 'USD');
  };

  return (
    <Button 
      variant="ghost" 
      size="icon" 
      onClick={toggleCurrency}
      className="relative"
      title={currency === 'USD' ? 'Switch to Ethiopian Birr' : 'Switch to USD'}
    >
      <DollarSign className="h-5 w-5" />
      <span className="absolute -bottom-1 right-0 text-xs font-bold">
        {currency === 'USD' ? '$' : 'BR'}
      </span>
    </Button>
  );
};

export default CurrencySwitcher;
