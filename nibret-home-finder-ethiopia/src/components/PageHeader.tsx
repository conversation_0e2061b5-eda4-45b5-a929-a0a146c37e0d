
import React, { ReactNode } from 'react';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  children?: ReactNode;
  backgroundImage?: string;
}

const PageHeader = ({ title, subtitle, children, backgroundImage }: PageHeaderProps) => {
  return (
    <div 
      className="bg-nibret-blue text-white py-16 relative overflow-hidden"
      style={backgroundImage ? { 
        backgroundImage: `linear-gradient(rgba(26, 54, 93, 0.9), rgba(26, 54, 93, 0.85)), url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      } : {}}
    >
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <h1 className="text-3xl md:text-5xl font-bold mb-4">
          {title}
        </h1>
        {subtitle && (
          <p className="text-xl md:text-2xl text-nibret-light-gold/90 max-w-2xl mb-6">
            {subtitle}
          </p>
        )}
        <div className="mt-4">
          {children}
        </div>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute bottom-0 right-0 w-64 h-64 bg-nibret-gold/10 rounded-full -mr-32 -mb-32" />
      <div className="absolute top-0 left-0 w-32 h-32 bg-nibret-gold/10 rounded-full -ml-16 -mt-16" />
    </div>
  );
};

export default PageHeader;
