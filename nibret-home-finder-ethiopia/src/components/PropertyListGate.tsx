import React, { useContext, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AuthContext } from '@/App';
import { useAppContext } from '@/context/AppContext';
import { Property } from '@/lib/api/properties';
import { Lock, Users, Star, TrendingUp, Eye, ArrowRight } from 'lucide-react';
import AuthModal from './AuthModal';
import PropertyPreviewGate from './PropertyPreviewGate';

interface PropertyListGateProps {
  properties: Property[];
  maxPreviewCount?: number;
  showStats?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const PropertyListGate: React.FC<PropertyListGateProps> = ({
  properties,
  maxPreviewCount = 3,
  showStats = true,
  className = '',
  children
}) => {
  const { isAuthenticated } = useContext(AuthContext);
  const { translate, language } = useAppContext();
  const [showAuthModal, setShowAuthModal] = useState(false);

  // If user is authenticated, show full content
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // For non-authenticated users, show limited preview
  const previewProperties = properties.slice(0, maxPreviewCount);
  const hiddenCount = Math.max(0, properties.length - maxPreviewCount);

  const handleSignInPrompt = () => {
    setShowAuthModal(true);
  };

  // Calculate stats for display
  const stats = {
    totalProperties: properties.length,
    featuredProperties: properties.filter(p => p.is_featured).length,
    averageViews: Math.round(properties.reduce((sum, p) => sum + (p.views || 0), 0) / properties.length) || 0,
    newListings: properties.filter(p => {
      const createdDate = new Date(p.created_at || '');
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return createdDate > weekAgo;
    }).length
  };

  return (
    <div className={className}>
      {/* Stats Section */}
      {showStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-nibret-blue">{stats.totalProperties}</div>
            <div className="text-sm text-gray-600">{translate('Total Properties', 'ጠቅላላ ንብረቶች')}</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-yellow-600">{stats.featuredProperties}</div>
            <div className="text-sm text-gray-600">{translate('Featured', 'ተመራጭ')}</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-green-600">{stats.newListings}</div>
            <div className="text-sm text-gray-600">{translate('New This Week', 'በዚህ ሳምንት አዲስ')}</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-purple-600">{stats.averageViews}</div>
            <div className="text-sm text-gray-600">{translate('Avg. Views', 'አማካይ እይታዎች')}</div>
          </Card>
        </div>
      )}

      {/* Preview Properties */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {previewProperties.map((property) => (
          <PropertyPreviewGate key={property.id} property={property} />
        ))}
      </div>

      {/* Sign-in Prompt Card */}
      {hiddenCount > 0 && (
        <Card className="bg-gradient-to-r from-nibret-blue to-blue-600 text-white border-0 shadow-xl">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4">
              <Lock className="w-8 h-8 text-white" />
            </div>
            <CardTitle className={`text-2xl md:text-3xl font-bold ${language === 'am' ? 'amharic' : ''}`}>
              {translate('Unlock All Properties', 'ሁሉንም ንብረቶች ክፈት')}
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="space-y-2">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto">
                  <Eye className="w-6 h-6" />
                </div>
                <h4 className="font-semibold">
                  {translate('View All Properties', 'ሁሉንም ንብረቶች ይመልከቱ')}
                </h4>
                <p className="text-sm opacity-90">
                  {translate('Access our complete database', 'ሙሉ የመረጃ ቋታችንን ይድረሱ')}
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto">
                  <Star className="w-6 h-6" />
                </div>
                <h4 className="font-semibold">
                  {translate('Save Favorites', 'ተወዳጆችን አስቀምጥ')}
                </h4>
                <p className="text-sm opacity-90">
                  {translate('Create your wishlist', 'የምኞት ዝርዝርዎን ይፍጠሩ')}
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto">
                  <TrendingUp className="w-6 h-6" />
                </div>
                <h4 className="font-semibold">
                  {translate('Get Alerts', 'ማንቂያዎችን ይቀበሉ')}
                </h4>
                <p className="text-sm opacity-90">
                  {translate('Never miss new listings', 'አዲስ ዝርዝሮችን አያመልጡ')}
                </p>
              </div>
            </div>

            <div className="bg-white/10 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-4 text-lg font-semibold">
                <Users className="w-5 h-5" />
                <span>
                  {translate(
                    `${hiddenCount} more properties waiting for you!`,
                    `${hiddenCount} ተጨማሪ ንብረቶች እርስዎን እየጠበቁ ነው!`
                  )}
                </span>
              </div>
            </div>

            <div className="space-y-3">
              <Button 
                onClick={handleSignInPrompt}
                size="lg"
                className="w-full bg-white text-nibret-blue hover:bg-gray-100 font-semibold"
              >
                {translate('Sign In to View All Properties', 'ሁሉንም ንብረቶች ለማየት ይግቡ')}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              
              <p className="text-sm opacity-90">
                {translate(
                  'Join thousands of users who found their dream homes with us',
                  'ከእኛ ጋር የህልም ቤታቸውን ያገኙ በሺዎች የሚቆጠሩ ተጠቃሚዎችን ይቀላቀሉ'
                )}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Auth Modal */}
      <AuthModal 
        isOpen={showAuthModal} 
        onClose={() => setShowAuthModal(false)} 
      />
    </div>
  );
};

export default PropertyListGate;
