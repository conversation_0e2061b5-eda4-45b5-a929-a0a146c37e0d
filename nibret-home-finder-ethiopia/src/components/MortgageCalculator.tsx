
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { useAppContext } from '../context/AppContext';

const MortgageCalculator = () => {
  const { currency, convertCurrency, translate } = useAppContext();
  const [homePrice, setHomePrice] = useState(200000);
  const [downPayment, setDownPayment] = useState(40000);
  const [loanTerm, setLoanTerm] = useState(30);
  const [interestRate, setInterestRate] = useState(7);
  const [monthlyPayment, setMonthlyPayment] = useState(0);

  // Calculate monthly payment
  useEffect(() => {
    const principal = homePrice - downPayment;
    const monthlyRate = interestRate / 100 / 12;
    const numberOfPayments = loanTerm * 12;
    
    if (monthlyRate === 0) {
      setMonthlyPayment(principal / numberOfPayments);
    } else {
      const payment = principal * monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments) / 
                     (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
      setMonthlyPayment(payment);
    }
  }, [homePrice, downPayment, loanTerm, interestRate]);

  // Format currency
  const formatCurrency = (value: number) => {
    const convertedValue = convertCurrency(value);
    if (currency === 'USD') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        maximumFractionDigits: 0
      }).format(convertedValue);
    } else {
      return new Intl.NumberFormat('en-ET', {
        style: 'currency',
        currency: 'ETB',
        maximumFractionDigits: 0
      }).format(convertedValue);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="bg-nibret-blue text-white">
        <CardTitle>{translate('Mortgage Calculator', 'የብድር ማስያ')}</CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-6">
          {/* Home Price Input */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <Label htmlFor="homePrice">{translate('Home Price', 'የቤት ዋጋ')}</Label>
              <span className="font-medium">{formatCurrency(homePrice)}</span>
            </div>
            <Slider 
              id="homePrice"
              min={50000} 
              max={1000000} 
              step={10000}
              value={[homePrice]}
              onValueChange={(value) => setHomePrice(value[0])} 
              className="mb-2"
            />
            <Input 
              type="number" 
              value={homePrice} 
              onChange={(e) => setHomePrice(Number(e.target.value))}
              className="mt-2" 
            />
          </div>

          {/* Down Payment Input */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <Label htmlFor="downPayment">{translate('Down Payment', 'የመጀመሪያ ክፍያ')}</Label>
              <span className="font-medium">{formatCurrency(downPayment)}</span>
            </div>
            <Slider 
              id="downPayment"
              min={0} 
              max={homePrice * 0.5} 
              step={5000}
              value={[downPayment]}
              onValueChange={(value) => setDownPayment(value[0])} 
              className="mb-2"
            />
            <Input 
              type="number" 
              value={downPayment} 
              onChange={(e) => setDownPayment(Number(e.target.value))}
              className="mt-2" 
            />
          </div>

          {/* Loan Term Input */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <Label htmlFor="loanTerm">{translate('Loan Term (years)', 'የብድር ጊዜ (ዓመታት)')}</Label>
              <span className="font-medium">{loanTerm} {translate('years', 'ዓመታት')}</span>
            </div>
            <Slider 
              id="loanTerm"
              min={5} 
              max={30} 
              step={5}
              value={[loanTerm]}
              onValueChange={(value) => setLoanTerm(value[0])} 
              className="mb-2"
            />
            <Input 
              type="number" 
              value={loanTerm} 
              onChange={(e) => setLoanTerm(Number(e.target.value))}
              className="mt-2" 
            />
          </div>

          {/* Interest Rate Input */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <Label htmlFor="interestRate">{translate('Interest Rate', 'የወለድ መጠን')}</Label>
              <span className="font-medium">{interestRate}%</span>
            </div>
            <Slider 
              id="interestRate"
              min={1} 
              max={20} 
              step={0.1}
              value={[interestRate]}
              onValueChange={(value) => setInterestRate(value[0])} 
              className="mb-2"
            />
            <Input 
              type="number" 
              value={interestRate} 
              onChange={(e) => setInterestRate(Number(e.target.value))}
              step="0.1"
              className="mt-2" 
            />
          </div>

          {/* Monthly Payment Result */}
          <div className="pt-4 border-t">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">{translate('Monthly Payment', 'ወርሃዊ ክፍያ')}</h3>
              <p className="text-2xl font-bold text-nibret-blue">{formatCurrency(monthlyPayment)}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MortgageCalculator;
