import React, { useContext, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AuthContext } from '@/App';
import { useAppContext } from '@/context/AppContext';
import { Property } from '@/lib/api/properties';
import { Lock, Eye, Star, MapPin, BedDouble, Bath, SquareCode, ArrowRight } from 'lucide-react';
import AuthModal from './AuthModal';
import PropertyTags from './PropertyTags';

interface PropertyPreviewGateProps {
  property: Property;
  showFullDetails?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const PropertyPreviewGate: React.FC<PropertyPreviewGateProps> = ({
  property,
  showFullDetails = false,
  className = '',
  children
}) => {
  const { isAuthenticated } = useContext(AuthContext);
  const { translate, language, convertCurrency, formatCurrency } = useAppContext();
  const [showAuthModal, setShowAuthModal] = useState(false);

  // If user is authenticated, show full content
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // For non-authenticated users, show preview with sign-in prompt
  const handleSignInPrompt = () => {
    setShowAuthModal(true);
  };

  const formatPrice = () => {
    const sourceCurrency = (property as any).currency || 'ETB';
    const convertedPrice = convertCurrency(property.price, sourceCurrency);
    return formatCurrency(convertedPrice);
  };

  // Show blurred preview for non-authenticated users
  return (
    <>
      <Card className={`group overflow-hidden hover:shadow-xl transition-all duration-300 border-0 shadow-lg relative ${className}`}>
        {/* Overlay for non-authenticated users */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent z-10 flex items-center justify-center">
          <div className="text-center text-white p-6">
            <Lock className="w-12 h-12 mx-auto mb-4 text-white" />
            <h3 className="text-xl font-bold mb-2">
              {translate('Sign in to view property details', 'የንብረት ዝርዝሮችን ለማየት ይግቡ')}
            </h3>
            <p className="text-sm mb-4 opacity-90">
              {translate('Join thousands of users finding their dream homes', 'የህልም ቤታቸውን የሚያገኙ በሺዎች የሚቆጠሩ ተጠቃሚዎችን ይቀላቀሉ')}
            </p>
            <Button 
              onClick={handleSignInPrompt}
              className="bg-nibret-blue hover:bg-nibret-blue/90 text-white"
            >
              <Eye className="w-4 h-4 mr-2" />
              {translate('Sign In to View', 'ለማየት ይግቡ')}
            </Button>
          </div>
        </div>

        {/* Blurred content preview */}
        <div className="filter blur-sm">
          <div className="relative h-48 overflow-hidden">
            <img
              src={property.images?.[0] || 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=600&h=400&fit=crop'}
              alt={property.title}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.src = 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=600&h=400&fit=crop';
              }}
            />
            
            {/* Featured Badge */}
            {property.is_featured && (
              <div className="absolute top-3 left-3">
                <Badge className="bg-yellow-500 text-white shadow-lg">
                  <Star className="h-3 w-3 mr-1 fill-current" />
                  {translate('Featured', 'ተመራጭ')}
                </Badge>
              </div>
            )}

            {/* Views Badge */}
            {property.views && (
              <div className="absolute top-3 right-3">
                <Badge variant="secondary" className="bg-black/70 text-white">
                  <Eye className="h-3 w-3 mr-1" />
                  {property.views}
                </Badge>
              </div>
            )}

            {/* Price Overlay */}
            <div className="absolute bottom-3 left-3">
              <Badge className="bg-nibret-blue text-white text-lg font-bold px-3 py-1">
                {formatPrice()}
              </Badge>
            </div>
          </div>

          <CardContent className="p-6">
            <div className="mb-3">
              <h3 className={`text-xl font-semibold text-gray-900 mb-2 line-clamp-2 ${language === 'am' ? 'amharic' : ''}`}>
                {property.title}
              </h3>
              <div className="flex items-center text-gray-600 mb-3">
                <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                <span className={`text-sm truncate ${language === 'am' ? 'amharic' : ''}`}>
                  {property.address}
                </span>
              </div>
            </div>

            {/* Property Tags */}
            <div className="mb-4">
              <PropertyTags
                propertyType={property.propertyType}
                status={property.status}
                listing_type={property.listing_type}
                is_featured={property.is_featured}
                size="sm"
              />
            </div>

            {/* Property Stats */}
            <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
              <div className="flex items-center">
                <BedDouble className="h-4 w-4 mr-1" />
                <span>{property.beds} {translate('beds', 'አልጋዎች')}</span>
              </div>
              <div className="flex items-center">
                <Bath className="h-4 w-4 mr-1" />
                <span>{property.baths} {translate('baths', 'መታጠቢያዎች')}</span>
              </div>
              <div className="flex items-center">
                <SquareCode className="h-4 w-4 mr-1" />
                <span>{property.sqm} {translate('sqm', 'ስኩየር ሜትር')}</span>
              </div>
            </div>

            {/* Sign In Button */}
            <Button 
              onClick={handleSignInPrompt}
              className="w-full bg-nibret-blue hover:bg-nibret-blue/90 text-white group"
            >
              {translate('Sign In to View Details', 'ዝርዝር ለማየት ይግቡ')}
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </CardContent>
        </div>
      </Card>

      {/* Auth Modal */}
      <AuthModal 
        isOpen={showAuthModal} 
        onClose={() => setShowAuthModal(false)} 
      />
    </>
  );
};

export default PropertyPreviewGate;
