import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Percent, HandHeart, Tag } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';

interface PricingTagsProps {
  price: number;
  originalPrice?: number;
  discountPercentage?: number;
  isNegotiable?: boolean;
  currency?: 'ETB' | 'USD';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

const PricingTags: React.FC<PricingTagsProps> = ({
  price,
  originalPrice,
  discountPercentage,
  isNegotiable = false,
  currency = 'ETB',
  size = 'default',
  className = ''
}) => {
  const { translate, convertCurrency, formatCurrency } = useAppContext();

  // Calculate discount if not provided but original price is available
  const calculatedDiscount = discountPercentage ||
    (originalPrice && originalPrice > price ?
      Math.round(((originalPrice - price) / originalPrice) * 100) : 0);

  // Only consider it a discount if it's greater than 0 and we have valid original price
  const hasDiscount = calculatedDiscount > 0 && originalPrice && originalPrice > price;

  // Size variants
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    default: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    default: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      {/* Discount Badge */}
      {hasDiscount && (
        <Badge 
          variant="destructive" 
          className={`bg-red-500 text-white ${sizeClasses[size]} font-bold animate-pulse`}
        >
          <Percent className={`${iconSizes[size]} mr-1`} />
          {calculatedDiscount}% {translate('OFF', 'ቅናሽ')}
        </Badge>
      )}

      {/* Negotiable Badge */}
      {isNegotiable && (
        <Badge 
          variant="outline" 
          className={`border-green-500 text-green-700 bg-green-50 ${sizeClasses[size]} font-medium`}
        >
          <HandHeart className={`${iconSizes[size]} mr-1`} />
          {translate('Negotiable', 'ተደራደር')}
        </Badge>
      )}

      {/* Best Deal Badge (for high discounts) */}
      {hasDiscount && calculatedDiscount >= 20 && (
        <Badge 
          variant="default" 
          className={`bg-orange-500 text-white ${sizeClasses[size]} font-bold`}
        >
          <Tag className={`${iconSizes[size]} mr-1`} />
          {translate('Best Deal', 'ምርጥ ዋጋ')}
        </Badge>
      )}

      {/* Limited Time Badge (for very high discounts) */}
      {hasDiscount && calculatedDiscount >= 30 && (
        <Badge 
          variant="default" 
          className={`bg-purple-500 text-white ${sizeClasses[size]} font-bold animate-bounce`}
        >
          ⚡ {translate('Limited Time', 'የተወሰነ ጊዜ')}
        </Badge>
      )}
    </div>
  );
};

export default PricingTags;
