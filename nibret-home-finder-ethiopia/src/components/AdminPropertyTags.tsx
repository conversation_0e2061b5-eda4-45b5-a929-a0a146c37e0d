import React from 'react';
import { Badge } from '@/components/ui/badge';
import { useAppContext } from '../context/AppContext';
import { 
  Flame, 
  TrendingDown, 
  Sparkles, 
  Home, 
  Video, 
  Crown, 
  TrendingUp, 
  Zap, 
  Heart, 
  FileText, 
  Clock, 
  Star, 
  Waves, 
  Square, 
  Wrench, 
  CheckCircle,
  AlertTriangle,
  Circle,
  ArrowUp
} from 'lucide-react';

interface AdminPropertyTagsProps {
  adminTags?: string[];
  priorityLevel?: 'low' | 'normal' | 'high' | 'urgent';
  className?: string;
  size?: 'sm' | 'default' | 'lg';
  showPriority?: boolean;
}

const AdminPropertyTags: React.FC<AdminPropertyTagsProps> = ({
  adminTags = [],
  priorityLevel = 'normal',
  className = '',
  size = 'default',
  showPriority = true
}) => {
  const { translate } = useAppContext();

  // Admin tag configurations
  const getAdminTagConfig = (tag: string) => {
    const configs = {
      hot_deal: {
        icon: Flame,
        label: translate('Hot Deal', 'ሙቅ ዲል'),
        color: 'bg-red-100 text-red-800 border-red-300'
      },
      price_reduced: {
        icon: TrendingDown,
        label: translate('Price Reduced', 'ዋጋ ቀንሷል'),
        color: 'bg-orange-100 text-orange-800 border-orange-300'
      },
      new_listing: {
        icon: Sparkles,
        label: translate('New Listing', 'አዲስ ዝርዝር'),
        color: 'bg-green-100 text-green-800 border-green-300'
      },
      open_house: {
        icon: Home,
        label: translate('Open House', 'ክፍት ቤት'),
        color: 'bg-blue-100 text-blue-800 border-blue-300'
      },
      virtual_tour: {
        icon: Video,
        label: translate('Virtual Tour', 'ቨርቹዋል ጉብኝት'),
        color: 'bg-purple-100 text-purple-800 border-purple-300'
      },
      luxury: {
        icon: Crown,
        label: translate('Luxury', 'ቅንጦት'),
        color: 'bg-yellow-100 text-yellow-800 border-yellow-300'
      },
      investment_opportunity: {
        icon: TrendingUp,
        label: translate('Investment', 'ኢንቨስትመንት'),
        color: 'bg-indigo-100 text-indigo-800 border-indigo-300'
      },
      quick_sale: {
        icon: Zap,
        label: translate('Quick Sale', 'ፈጣን ሽያጭ'),
        color: 'bg-pink-100 text-pink-800 border-pink-300'
      },
      motivated_seller: {
        icon: Heart,
        label: translate('Motivated Seller', 'ተነሳሽ ሻጭ'),
        color: 'bg-teal-100 text-teal-800 border-teal-300'
      },
      under_contract: {
        icon: FileText,
        label: translate('Under Contract', 'በኮንትራት ስር'),
        color: 'bg-gray-100 text-gray-800 border-gray-300'
      },
      coming_soon: {
        icon: Clock,
        label: translate('Coming Soon', 'በቅርቡ'),
        color: 'bg-cyan-100 text-cyan-800 border-cyan-300'
      },
      exclusive: {
        icon: Star,
        label: translate('Exclusive', 'ልዩ'),
        color: 'bg-amber-100 text-amber-800 border-amber-300'
      },
      waterfront: {
        icon: Waves,
        label: translate('Waterfront', 'የውሃ ዳርቻ'),
        color: 'bg-blue-100 text-blue-800 border-blue-300'
      },
      corner_lot: {
        icon: Square,
        label: translate('Corner Lot', 'ጥግ ቦታ'),
        color: 'bg-slate-100 text-slate-800 border-slate-300'
      },
      renovated: {
        icon: Wrench,
        label: translate('Renovated', 'ታድሷል'),
        color: 'bg-emerald-100 text-emerald-800 border-emerald-300'
      },
      move_in_ready: {
        icon: CheckCircle,
        label: translate('Move-in Ready', 'ለመግባት ዝግጁ'),
        color: 'bg-lime-100 text-lime-800 border-lime-300'
      }
    };

    return configs[tag as keyof typeof configs] || {
      icon: Circle,
      label: tag.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      color: 'bg-gray-100 text-gray-800 border-gray-300'
    };
  };

  // Priority level configurations
  const getPriorityConfig = (priority: string) => {
    const configs = {
      low: {
        icon: Circle,
        label: translate('Low Priority', 'ዝቅተኛ ቅድሚያ'),
        color: 'bg-gray-100 text-gray-600 border-gray-300'
      },
      normal: {
        icon: Circle,
        label: translate('Normal', 'መደበኛ'),
        color: 'bg-blue-100 text-blue-600 border-blue-300'
      },
      high: {
        icon: ArrowUp,
        label: translate('High Priority', 'ከፍተኛ ቅድሚያ'),
        color: 'bg-orange-100 text-orange-600 border-orange-300'
      },
      urgent: {
        icon: AlertTriangle,
        label: translate('Urgent', 'አስቸኳይ'),
        color: 'bg-red-100 text-red-600 border-red-300'
      }
    };

    return configs[priority as keyof typeof configs] || configs.normal;
  };

  // Size variants
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    default: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    default: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  const priorityConfig = getPriorityConfig(priorityLevel);

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {/* Priority Badge */}
      {showPriority && priorityLevel !== 'normal' && (
        <Badge 
          variant="outline" 
          className={`${priorityConfig.color} ${sizeClasses[size]} border font-medium`}
        >
          <priorityConfig.icon className={`${iconSizes[size]} mr-1`} />
          {priorityConfig.label}
        </Badge>
      )}

      {/* Admin Tags */}
      {adminTags.map((tag, index) => {
        const tagConfig = getAdminTagConfig(tag);
        return (
          <Badge 
            key={index}
            variant="outline" 
            className={`${tagConfig.color} ${sizeClasses[size]} border font-medium`}
          >
            <tagConfig.icon className={`${iconSizes[size]} mr-1`} />
            {tagConfig.label}
          </Badge>
        );
      })}
    </div>
  );
};

export default AdminPropertyTags;
