import React, { useState, useEffect } from 'react';
import { Star, StarOff, Eye, DollarSign, Home, MapPin, Calendar, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { propertyApi, Property } from '@/lib/api';
import { useAppContext } from '@/context/AppContext';
import PropertyTags from './PropertyTags';

interface AdminFeaturedManagerProps {
  className?: string;
}

const AdminFeaturedManager: React.FC<AdminFeaturedManagerProps> = ({ className = '' }) => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const { translate, convertCurrency, formatCurrency } = useAppContext();
  const { toast } = useToast();

  // Fetch all properties
  const fetchProperties = async () => {
    try {
      setIsLoading(true);
      const allProperties = await propertyApi.getProperties();
      setProperties(allProperties);
    } catch (error) {
      console.error('Error fetching properties:', error);
      toast({
        title: translate('Error', 'ስህተት'),
        description: translate('Failed to fetch properties', 'ንብረቶችን ማምጣት አልተሳካም'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProperties();
  }, []);

  // Toggle featured status for a single property
  const toggleSingleFeatured = async (propertyId: string) => {
    try {
      setIsUpdating(true);
      await propertyApi.toggleFeatured(propertyId);
      
      // Update local state
      setProperties(prev => prev.map(p => 
        p.id === propertyId 
          ? { ...p, is_featured: !p.is_featured }
          : p
      ));

      toast({
        title: translate('Success', 'ተሳክቷል'),
        description: translate('Featured status updated', 'የተመራጭ ሁኔታ ተዘምኗል'),
      });
    } catch (error) {
      console.error('Error toggling featured status:', error);
      toast({
        title: translate('Error', 'ስህተት'),
        description: translate('Failed to update featured status', 'የተመራጭ ሁኔታን ማዘመን አልተሳካም'),
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Bulk toggle featured status
  const bulkToggleFeatured = async (isFeatured: boolean) => {
    if (selectedProperties.length === 0) {
      toast({
        title: translate('No Selection', 'ምንም አልተመረጠም'),
        description: translate('Please select properties first', 'እባክዎ በመጀመሪያ ንብረቶችን ይምረጡ'),
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsUpdating(true);
      await propertyApi.bulkToggleFeatured(selectedProperties, isFeatured);
      
      // Update local state
      setProperties(prev => prev.map(p => 
        selectedProperties.includes(p.id)
          ? { ...p, is_featured: isFeatured }
          : p
      ));

      setSelectedProperties([]);

      toast({
        title: translate('Success', 'ተሳክቷል'),
        description: translate(
          `${selectedProperties.length} properties ${isFeatured ? 'featured' : 'unfeatured'}`,
          `${selectedProperties.length} ንብረቶች ${isFeatured ? 'ተመራጭ ሆነዋል' : 'ተመራጭ አልሆኑም'}`
        ),
      });
    } catch (error) {
      console.error('Error bulk toggling featured status:', error);
      toast({
        title: translate('Error', 'ስህተት'),
        description: translate('Failed to update properties', 'ንብረቶችን ማዘመን አልተሳካም'),
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle property selection
  const togglePropertySelection = (propertyId: string) => {
    setSelectedProperties(prev => 
      prev.includes(propertyId)
        ? prev.filter(id => id !== propertyId)
        : [...prev, propertyId]
    );
  };

  // Select all properties
  const selectAll = () => {
    setSelectedProperties(properties.map(p => p.id));
  };

  // Clear selection
  const clearSelection = () => {
    setSelectedProperties([]);
  };

  const featuredProperties = properties.filter(p => p.is_featured);
  const nonFeaturedProperties = properties.filter(p => !p.is_featured);

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">{translate('Loading properties...', 'ንብረቶች በመጫን ላይ...')}</span>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {translate('Featured Properties Manager', 'የተመራጭ ንብረቶች አስተዳዳሪ')}
          </h2>
          <p className="text-gray-600">
            {translate('Manage which properties appear in the top 3 rows', 'በላይኛው 3 ረድፎች ውስጥ የትኞቹ ንብረቶች እንደሚታዩ ያስተዳድሩ')}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            {featuredProperties.length} {translate('Featured', 'ተመራጭ')}
          </Badge>
          <Badge variant="outline">
            {properties.length} {translate('Total', 'ጠቅላላ')}
          </Badge>
        </div>
      </div>

      {/* Bulk Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            {translate('Bulk Actions', 'የጅምላ እርምጃዎች')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={selectAll}
                disabled={isUpdating}
              >
                {translate('Select All', 'ሁሉንም ምረጥ')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearSelection}
                disabled={isUpdating || selectedProperties.length === 0}
              >
                {translate('Clear Selection', 'ምርጫን አጽዳ')}
              </Button>
              <span className="text-sm text-gray-600">
                {selectedProperties.length} {translate('selected', 'ተመርጠዋል')}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                onClick={() => bulkToggleFeatured(true)}
                disabled={isUpdating || selectedProperties.length === 0}
                size="sm"
              >
                <Star className="w-4 h-4 mr-1" />
                {translate('Feature Selected', 'የተመረጡትን አስተዋውቅ')}
              </Button>
              <Button
                variant="outline"
                onClick={() => bulkToggleFeatured(false)}
                disabled={isUpdating || selectedProperties.length === 0}
                size="sm"
              >
                <StarOff className="w-4 h-4 mr-1" />
                {translate('Unfeature Selected', 'የተመረጡትን አታስተዋውቅ')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Properties List */}
      <div className="grid gap-4">
        {properties.map((property) => (
          <Card key={property.id} className={`${property.is_featured ? 'ring-2 ring-yellow-400 bg-yellow-50' : ''}`}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Checkbox
                    checked={selectedProperties.includes(property.id)}
                    onCheckedChange={() => togglePropertySelection(property.id)}
                    disabled={isUpdating}
                  />
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-lg">{property.title}</h3>
                      {property.is_featured && (
                        <Badge className="bg-yellow-500 text-white">
                          <Star className="w-3 h-3 mr-1" />
                          {translate('Featured', 'ተመራጭ')}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="w-4 h-4" />
                        <span>{formatCurrency(convertCurrency(property.price, property.currency || 'ETB'))}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Home className="w-4 h-4" />
                        <span>{property.beds} bed, {property.baths} bath</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{property.address}</span>
                      </div>
                      {property.views && (
                        <div className="flex items-center space-x-1">
                          <Eye className="w-4 h-4" />
                          <span>{property.views} views</span>
                        </div>
                      )}
                    </div>

                    {/* Property Tags */}
                    <div className="mt-2">
                      <PropertyTags
                        propertyType={property.propertyType}
                        status={property.status}
                        listing_type={property.listing_type}
                        is_featured={property.is_featured}
                        size="sm"
                      />
                    </div>
                  </div>
                </div>

                <Button
                  variant={property.is_featured ? "default" : "outline"}
                  size="sm"
                  onClick={() => toggleSingleFeatured(property.id)}
                  disabled={isUpdating}
                >
                  {property.is_featured ? (
                    <>
                      <Star className="w-4 h-4 mr-1 fill-current" />
                      {translate('Featured', 'ተመራጭ')}
                    </>
                  ) : (
                    <>
                      <StarOff className="w-4 h-4 mr-1" />
                      {translate('Feature', 'አስተዋውቅ')}
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {properties.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">
            {translate('No properties found', 'ምንም ንብረቶች አልተገኙም')}
          </p>
        </div>
      )}
    </div>
  );
};

export default AdminFeaturedManager;
