/* Custom styles for Leaflet Map */

/* Property marker styles */
.property-marker {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.property-marker:hover {
  transform: scale(1.1);
  z-index: 1000;
}

/* Popup styles */
.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
}

.leaflet-popup-content {
  margin: 0;
  padding: 0;
  border-radius: 8px;
  overflow: hidden;
}

.leaflet-popup-tip {
  background: white;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.property-popup .leaflet-popup-close-button {
  color: #6b7280;
  font-size: 18px;
  padding: 4px 8px;
  top: 8px;
  right: 8px;
}

.property-popup .leaflet-popup-close-button:hover {
  color: #374151;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

/* Map container styles */
.leaflet-container {
  font-family: inherit;
  width: 100% !important;
  height: 100% !important;
  z-index: 1;
}

/* Attribution styles */
.leaflet-control-attribution {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  border-radius: 4px;
  font-size: 11px;
}

/* Zoom control styles */
.leaflet-control-zoom {
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.leaflet-control-zoom a {
  background: white;
  color: #374151;
  border: none;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.2s ease;
}

.leaflet-control-zoom a:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.leaflet-control-zoom a:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.leaflet-control-zoom a:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

/* Loading state */
.map-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f9fafb;
  color: #6b7280;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .leaflet-popup-content-wrapper {
    max-width: 250px;
  }

  .property-marker {
    transform: scale(0.8);
  }

  .property-marker:hover {
    transform: scale(0.9);
  }

  .leaflet-container {
    min-height: 200px !important;
  }
}

@media (max-width: 480px) {
  .property-marker {
    transform: scale(0.7);
  }

  .property-marker:hover {
    transform: scale(0.8);
  }

  .leaflet-popup-content-wrapper {
    max-width: 200px;
  }

  .leaflet-control-zoom {
    transform: scale(0.8);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .leaflet-control-attribution {
    background: rgba(31, 41, 55, 0.8);
    color: #d1d5db;
  }
  
  .leaflet-control-zoom a {
    background: #374151;
    color: #f9fafb;
  }
  
  .leaflet-control-zoom a:hover {
    background: #4b5563;
  }
}

/* Custom marker animations */
@keyframes markerBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.property-marker.animate-bounce {
  animation: markerBounce 1s ease-in-out;
}

/* Cluster styles (if needed in future) */
.marker-cluster {
  background: rgba(37, 99, 235, 0.8);
  border: 2px solid white;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.marker-cluster-small {
  width: 40px;
  height: 40px;
  line-height: 36px;
  font-size: 12px;
}

.marker-cluster-medium {
  width: 50px;
  height: 50px;
  line-height: 46px;
  font-size: 14px;
}

.marker-cluster-large {
  width: 60px;
  height: 60px;
  line-height: 56px;
  font-size: 16px;
}
