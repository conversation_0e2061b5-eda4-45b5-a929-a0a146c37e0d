import React, { useState, useEffect } from 'react';
import { currencyAPI } from '../lib/api/currency';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { RefreshCw, AlertCircle, CheckCircle } from 'lucide-react';

interface TestResult {
  test: string;
  status: 'pending' | 'success' | 'error';
  result?: any;
  error?: string;
  duration?: number;
}

const ExchangeRateTest: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (index: number, update: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => i === index ? { ...test, ...update } : test));
  };

  const runTests = async () => {
    setIsRunning(true);
    const testCases: TestResult[] = [
      { test: 'Get Exchange Rate', status: 'pending' },
      { test: 'Convert $100 USD to ETB', status: 'pending' },
      { test: 'Convert 10,000 ETB to USD', status: 'pending' },
      { test: 'Check API Health', status: 'pending' },
      { test: 'Get Detailed Rate Info', status: 'pending' }
    ];
    
    setTests(testCases);

    // Test 1: Get Exchange Rate
    try {
      const start = Date.now();
      const rate = await currencyAPI.getExchangeRate();
      const duration = Date.now() - start;
      updateTest(0, {
        status: 'success',
        result: `${rate.rate} ETB/USD (${rate.source})`,
        duration
      });
    } catch (error) {
      updateTest(0, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 2: Convert USD to ETB
    try {
      const start = Date.now();
      const etbAmount = await currencyAPI.convertUSDToETB(100);
      const duration = Date.now() - start;
      updateTest(1, {
        status: 'success',
        result: `${etbAmount.toFixed(2)} ETB`,
        duration
      });
    } catch (error) {
      updateTest(1, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 3: Convert ETB to USD
    try {
      const start = Date.now();
      const usdAmount = await currencyAPI.convertETBToUSD(10000);
      const duration = Date.now() - start;
      updateTest(2, {
        status: 'success',
        result: `$${usdAmount.toFixed(2)} USD`,
        duration
      });
    } catch (error) {
      updateTest(2, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 4: Check API Health
    try {
      const start = Date.now();
      const health = await currencyAPI.checkAPIHealth();
      const duration = Date.now() - start;
      const healthSummary = Object.entries(health)
        .map(([api, status]) => `${api}: ${status ? '✅' : '❌'}`)
        .join(', ');
      updateTest(3, {
        status: 'success',
        result: healthSummary,
        duration
      });
    } catch (error) {
      updateTest(3, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 5: Get Detailed Rate Info
    try {
      const start = Date.now();
      const details = await currencyAPI.getExchangeRateWithDetails();
      const duration = Date.now() - start;
      updateTest(4, {
        status: 'success',
        result: `Rate: ${details.rate.rate}, Sources: ${details.sources.length}, APIs: ${Object.keys(details.health).length}`,
        duration
      });
    } catch (error) {
      updateTest(4, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <RefreshCw className="w-4 h-4 text-gray-400 animate-spin" />;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Exchange Rate API Test</span>
          <Button
            onClick={runTests}
            disabled={isRunning}
            size="sm"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isRunning ? 'animate-spin' : ''}`} />
            Run Tests
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {tests.map((test, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-3 border rounded-lg"
            >
              <div className="flex items-center space-x-3">
                {getStatusIcon(test.status)}
                <span className="font-medium">{test.test}</span>
              </div>
              <div className="text-right">
                {test.status === 'success' && (
                  <div>
                    <div className="text-sm text-green-600">{test.result}</div>
                    {test.duration && (
                      <div className="text-xs text-gray-500">{test.duration}ms</div>
                    )}
                  </div>
                )}
                {test.status === 'error' && (
                  <div className="text-sm text-red-600">{test.error}</div>
                )}
                {test.status === 'pending' && isRunning && (
                  <div className="text-sm text-gray-500">Running...</div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {tests.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            Click "Run Tests" to test the exchange rate functionality
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ExchangeRateTest;
