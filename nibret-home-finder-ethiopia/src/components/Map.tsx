
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import './Map.css';
import { useAppContext } from '../context/AppContext';
import { getLocationCoordinates, getRandomCoordinatesInArea } from '../utils/locationMapping';

// Location obfuscation utility
const obfuscateLocation = (lat: number, lng: number, propertyId: string): [number, number] => {
  // Use property ID as seed for consistent obfuscation
  const seed = propertyId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);

  // Create deterministic random generator based on property ID
  const random = (seed: number) => {
    const x = Math.sin(seed) * 10000;
    return x - Math.floor(x);
  };

  // Generate random offset within 50-100 meter radius for better accuracy
  const minRadius = 50; // meters
  const maxRadius = 100; // meters

  // Random angle (0 to 2π)
  const angle = random(seed) * 2 * Math.PI;

  // Random radius between min and max
  const radius = minRadius + random(seed + 1) * (maxRadius - minRadius);

  // Convert meters to degrees (approximate)
  // 1 degree latitude ≈ 111,000 meters
  // 1 degree longitude ≈ 111,000 * cos(latitude) meters
  const latOffset = (radius * Math.cos(angle)) / 111000;
  const lngOffset = (radius * Math.sin(angle)) / (111000 * Math.cos(lat * Math.PI / 180));

  return [lat + latOffset, lng + lngOffset];
};

// Fix for default markers in React Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface MapProperty {
  id: string;
  lat: number;
  lng: number;
  price: number;
  title: string;
  titleAmharic?: string;
  address: string;
  addressAmharic?: string;
  image: string;
}

// Function to get accurate coordinates based on address
const getPropertyCoordinates = (property: MapProperty): [number, number] => {
  // First, try to get coordinates from the address
  const locationFromAddress = getLocationCoordinates(property.address);
  if (locationFromAddress) {
    return getRandomCoordinatesInArea(locationFromAddress, property.id);
  }

  // Try to extract location from title
  const locationFromTitle = getLocationCoordinates(property.title);
  if (locationFromTitle) {
    return getRandomCoordinatesInArea(locationFromTitle, property.id);
  }

  // If property already has valid coordinates, use them with slight obfuscation
  if (property.lat && property.lng &&
      property.lat >= -90 && property.lat <= 90 &&
      property.lng >= -180 && property.lng <= 180) {
    return obfuscateLocation(property.lat, property.lng, property.id);
  }

  // Default to Addis Ababa center with obfuscation
  return obfuscateLocation(9.0320, 38.7469, property.id);
};

interface MapProps {
  properties: MapProperty[];
  center?: [number, number];
  zoom?: number;
  className?: string;
  selectedPropertyId?: string;
}

// Custom property marker icon
const createPropertyIcon = (price: number, isFeatured?: boolean, isSelected?: boolean) => {
  let backgroundColor = '#2563eb'; // Default blue
  if (isSelected) {
    backgroundColor = '#dc2626'; // Red for selected
  } else if (isFeatured) {
    backgroundColor = '#f59e0b'; // Orange for featured
  }
  const formattedPrice = price.toLocaleString();

  return L.divIcon({
    html: `
      <div style="
        background: ${backgroundColor};
        color: white;
        padding: 4px 8px;
        border-radius: 16px;
        font-size: 11px;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        white-space: nowrap;
        border: 2px solid white;
        text-align: center;
        min-width: 60px;
      ">
        ${formattedPrice} ETB
      </div>
    `,
    className: 'property-marker',
    iconSize: [80, 30],
    iconAnchor: [40, 30],
    popupAnchor: [0, -30]
  });
};

// Component to fit bounds when properties change
const FitBounds: React.FC<{ properties: any[] }> = ({ properties }) => {
  const map = useMap();

  useEffect(() => {
    if (properties.length > 1) {
      const validProperties = properties.filter(p => p.displayLat && p.displayLng);
      if (validProperties.length > 0) {
        const bounds = L.latLngBounds(
          validProperties.map(p => [p.displayLat, p.displayLng] as [number, number])
        );
        map.fitBounds(bounds, { padding: [20, 20] });
      }
    }
  }, [properties, map]);

  return null;
};

const Map: React.FC<MapProps> = ({
  properties,
  center = [8.9806, 38.7578], // Default to Addis Ababa (lat, lng for Leaflet)
  zoom = 12,
  className = '',
  selectedPropertyId
}) => {
  const { language, translate, currency, convertCurrency } = useAppContext();
  const [mapError, setMapError] = useState(false);

  // Process properties to get accurate coordinates based on location
  const validProperties = properties.map(property => {
    // Get accurate coordinates based on address/title or use existing coordinates
    const [displayLat, displayLng] = getPropertyCoordinates(property);

    return {
      ...property,
      displayLat,
      displayLng,
      originalLat: property.lat,
      originalLng: property.lng
    };
  }).filter(p =>
    // Ensure we have valid display coordinates
    p.displayLat && p.displayLng &&
    !isNaN(p.displayLat) && !isNaN(p.displayLng) &&
    p.displayLat >= -90 && p.displayLat <= 90 &&
    p.displayLng >= -180 && p.displayLng <= 180
  );

  if (mapError) {
    return (
      <div className={`w-full h-full flex items-center justify-center bg-gray-100 ${className}`}>
        <div className="text-center p-8">
          <div className="text-4xl mb-4">🗺️</div>
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            {translate('Map View', 'የካርታ እይታ')}
          </h3>
          <p className="text-gray-600 mb-4">
            {translate('Interactive map showing property locations', 'የንብረት አካባቢዎችን የሚያሳይ በይነተገናኝ ካርታ')}
          </p>
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-700">
              {validProperties.length} {translate('properties in this area', 'ንብረቶች በዚህ አካባቢ')}
            </p>
          </div>
        </div>
      </div>
    );
  }

  try {
    return (
      <div className={`w-full h-full ${className}`} style={{ minHeight: '250px' }}>
        <MapContainer
          center={center}
          zoom={zoom}
          style={{ height: '100%', width: '100%' }}
          className="rounded-lg z-0"
          attributionControl={true}
        >
          {/* Multiple tile layer options for better reliability */}
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            errorTileUrl="https://via.placeholder.com/256x256/f3f4f6/9ca3af?text=Map+Tile"
          />

          {/* Alternative tile layer as fallback */}
          {/*
          <TileLayer
            attribution='&copy; <a href="https://carto.com/">CARTO</a>'
            url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png"
          />
          */}

          {/* Property markers */}
          {validProperties.map((property) => {
            const isSelected = selectedPropertyId === property.id;
            const isFeatured = property.is_featured || false;

            return (
              <Marker
                key={property.id}
                position={[property.displayLat, property.displayLng]}
                icon={createPropertyIcon(property.price, isFeatured, isSelected)}
              >
              <Popup maxWidth={250} className="property-popup">
                <div className="p-2">
                  <div className="mb-3">
                    <img
                      src={property.image}
                      alt={property.title}
                      className="w-full h-24 object-cover rounded"
                      onError={(e) => {
                        e.currentTarget.src = 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=200&h=150&fit=crop';
                      }}
                    />
                  </div>

                  <h3 className="font-semibold text-sm mb-1 text-gray-800">
                    {translate(property.title, property.titleAmharic || property.title)}
                  </h3>

                  <p className="text-xs text-gray-600 mb-2">
                    📍 {translate(property.address, property.addressAmharic || property.address)}
                  </p>
                  <p className="text-xs text-gray-500 mb-2">
                    📍 {translate('Coordinates:', 'መጋጠሚያዎች:')} {property.displayLat.toFixed(4)}, {property.displayLng.toFixed(4)}
                  </p>

                  <div className="flex items-center justify-between mb-2">
                    <p className="font-bold text-blue-600">
                      {convertCurrency ? convertCurrency(property.price).toLocaleString() : property.price.toLocaleString()} {currency === 'USD' ? 'USD' : 'ETB'}
                    </p>
                    <button
                      onClick={() => window.open(`/property/${property.id}`, '_blank')}
                      className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700 transition-colors"
                    >
                      {translate('View Details', 'ዝርዝር ይመልከቱ')}
                    </button>
                  </div>

                  <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                    <span className="flex items-center">
                      🔒 {translate('Location within 100m for privacy', 'ለግላዊነት ቦታው በ100ሜ ውስጥ')}
                    </span>
                  </div>
                </div>
              </Popup>
            </Marker>
            );
          })}

          {/* Fit bounds to show all properties */}
          <FitBounds properties={validProperties} />
        </MapContainer>

        {/* Property count and privacy indicator */}
        {validProperties.length > 0 && (
          <div className="absolute bottom-4 left-4 space-y-2">
            <div className="bg-white/90 backdrop-blur-sm px-3 py-2 rounded-lg shadow-lg text-sm">
              <span className="font-medium text-gray-700">
                {validProperties.length} {translate('properties', 'ንብረቶች')}
              </span>
            </div>
            <div className="bg-blue-50/90 backdrop-blur-sm px-3 py-2 rounded-lg shadow-lg text-xs">
              <span className="flex items-center text-blue-700">
                🔒 {translate('Locations within 100m for privacy', 'ቦታዎች ለግላዊነት በ100ሜ ውስጥ')}
              </span>
            </div>
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error('Map rendering error:', error);
    setMapError(true);
    return null;
  }
};

export default Map;
