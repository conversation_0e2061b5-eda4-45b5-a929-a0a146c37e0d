import React from 'react';
import { Badge } from '@/components/ui/badge';
import { useAppContext } from '../context/AppContext';
import { 
  Home, 
  Building, 
  Building2, 
  Castle, 
  Warehouse, 
  Briefcase,
  Tag,
  DollarSign,
  Key,
  CheckCircle,
  XCircle,
  Clock,
  Eye
} from 'lucide-react';

interface PropertyTagsProps {
  propertyType: string;
  status: string;
  listing_type?: string;
  is_featured?: boolean;
  admin_tags?: string[];
  priority_level?: 'low' | 'normal' | 'high' | 'urgent';
  showAdminTags?: boolean;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
}

const PropertyTags: React.FC<PropertyTagsProps> = ({
  propertyType,
  status,
  listing_type,
  is_featured,
  admin_tags = [],
  priority_level = 'normal',
  showAdminTags = false,
  className = '',
  size = 'default'
}) => {
  const { translate } = useAppContext();

  // Property type configurations
  const getPropertyTypeConfig = (type: string) => {
    const configs = {
      house: {
        icon: Home,
        label: translate('House', 'ቤት'),
        color: 'bg-blue-100 text-blue-800 border-blue-200'
      },
      apartment: {
        icon: Building,
        label: translate('Apartment', 'አፓርትመንት'),
        color: 'bg-green-100 text-green-800 border-green-200'
      },
      condo: {
        icon: Building2,
        label: translate('Condo', 'ኮንዶ'),
        color: 'bg-purple-100 text-purple-800 border-purple-200'
      },
      villa: {
        icon: Castle,
        label: translate('Villa', 'ቪላ'),
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200'
      },
      townhouse: {
        icon: Home,
        label: translate('Townhouse', 'ታውን ሃውስ'),
        color: 'bg-indigo-100 text-indigo-800 border-indigo-200'
      },
      studio: {
        icon: Warehouse,
        label: translate('Studio', 'ስቱዲዮ'),
        color: 'bg-pink-100 text-pink-800 border-pink-200'
      },
      office: {
        icon: Briefcase,
        label: translate('Office', 'ቢሮ'),
        color: 'bg-gray-100 text-gray-800 border-gray-200'
      },
      other: {
        icon: Tag,
        label: translate('Other', 'ሌላ'),
        color: 'bg-slate-100 text-slate-800 border-slate-200'
      }
    };
    return configs[type as keyof typeof configs] || configs.other;
  };

  // Status configurations
  const getStatusConfig = (status: string) => {
    const configs = {
      for_sale: {
        icon: DollarSign,
        label: translate('For Sale', 'ለሽያጭ'),
        color: 'bg-emerald-100 text-emerald-800 border-emerald-200'
      },
      for_rent: {
        icon: Key,
        label: translate('For Rent', 'ለኪራይ'),
        color: 'bg-orange-100 text-orange-800 border-orange-200'
      },
      sold: {
        icon: CheckCircle,
        label: translate('Sold', 'ተሽጧል'),
        color: 'bg-red-100 text-red-800 border-red-200'
      },
      rented: {
        icon: CheckCircle,
        label: translate('Rented', 'ተከራይቷል'),
        color: 'bg-amber-100 text-amber-800 border-amber-200'
      },
      off_market: {
        icon: XCircle,
        label: translate('Off Market', 'ከገበያ ውጭ'),
        color: 'bg-gray-100 text-gray-600 border-gray-200'
      }
    };
    return configs[status as keyof typeof configs] || configs.off_market;
  };

  // Listing type configurations
  const getListingTypeConfig = (type: string) => {
    const configs = {
      sale: {
        icon: DollarSign,
        label: translate('Sale', 'ሽያጭ'),
        color: 'bg-blue-50 text-blue-700 border-blue-200'
      },
      rent: {
        icon: Key,
        label: translate('Rent', 'ኪራይ'),
        color: 'bg-orange-50 text-orange-700 border-orange-200'
      },
      both: {
        icon: Tag,
        label: translate('Sale/Rent', 'ሽያጭ/ኪራይ'),
        color: 'bg-purple-50 text-purple-700 border-purple-200'
      }
    };
    return configs[type as keyof typeof configs] || configs.sale;
  };

  const propertyTypeConfig = getPropertyTypeConfig(propertyType);
  const statusConfig = getStatusConfig(status);
  const listingTypeConfig = listing_type ? getListingTypeConfig(listing_type) : null;

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    default: 'text-sm px-2.5 py-1.5',
    lg: 'text-base px-3 py-2'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    default: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {/* Property Type Tag */}
      <Badge 
        variant="outline" 
        className={`${propertyTypeConfig.color} ${sizeClasses[size]} border font-medium`}
      >
        <propertyTypeConfig.icon className={`${iconSizes[size]} mr-1`} />
        {propertyTypeConfig.label}
      </Badge>

      {/* Status Tag */}
      <Badge 
        variant="outline" 
        className={`${statusConfig.color} ${sizeClasses[size]} border font-medium`}
      >
        <statusConfig.icon className={`${iconSizes[size]} mr-1`} />
        {statusConfig.label}
      </Badge>

      {/* Listing Type Tag (if different from status) */}
      {listingTypeConfig && listing_type !== status.replace('for_', '') && (
        <Badge 
          variant="outline" 
          className={`${listingTypeConfig.color} ${sizeClasses[size]} border font-medium`}
        >
          <listingTypeConfig.icon className={`${iconSizes[size]} mr-1`} />
          {listingTypeConfig.label}
        </Badge>
      )}

      {/* Featured Tag */}
      {is_featured && (
        <Badge
          variant="outline"
          className={`bg-yellow-100 text-yellow-800 border-yellow-300 ${sizeClasses[size]} border font-medium`}
        >
          <Eye className={`${iconSizes[size]} mr-1`} />
          {translate('Featured', 'ተመራጭ')}
        </Badge>
      )}

      {/* Admin Tags (only show if enabled) */}
      {showAdminTags && admin_tags.length > 0 && (
        <>
          {admin_tags.slice(0, 2).map((tag, index) => (
            <Badge
              key={index}
              variant="outline"
              className={`bg-purple-100 text-purple-800 border-purple-300 ${sizeClasses[size]} border font-medium`}
            >
              <Tag className={`${iconSizes[size]} mr-1`} />
              {tag.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </Badge>
          ))}
          {admin_tags.length > 2 && (
            <Badge
              variant="outline"
              className={`bg-gray-100 text-gray-600 border-gray-300 ${sizeClasses[size]} border font-medium`}
            >
              +{admin_tags.length - 2} {translate('more', 'ተጨማሪ')}
            </Badge>
          )}
        </>
      )}
    </div>
  );
};

export default PropertyTags;
