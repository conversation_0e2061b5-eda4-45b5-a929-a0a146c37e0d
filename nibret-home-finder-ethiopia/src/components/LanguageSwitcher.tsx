
import React from 'react';
import { Button } from '@/components/ui/button';
import { useAppContext } from '../context/AppContext';
import { Languages } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const LanguageSwitcher = () => {
  const { language, setLanguage } = useAppContext();

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'am' : 'en');
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={toggleLanguage}
            className="relative"
          >
            <Languages className="h-5 w-5" />
            <span className="absolute -bottom-1 right-0 text-xs font-bold">
              {language === 'en' ? 'EN' : 'አማ'}
            </span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          {language === 'en' ? 'Switch to Amharic' : 'Switch to English'}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default LanguageSwitcher;
