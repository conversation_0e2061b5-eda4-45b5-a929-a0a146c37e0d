
import React, { useEffect, useState, useContext } from 'react';
import { useParams } from 'react-router-dom';
import { useAppContext } from '../context/AppContext';
import { AuthContext } from '../App';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { MapPin, Home, BedDouble, Bath, SquareCode, ChartBar, Users, ArrowLeft, ArrowRight, Star, Calendar, Ruler, Car, Shield, Wifi, Zap, Droplets, TreePine, Eye, Phone, Mail } from 'lucide-react';
import { AreaChart, Area, XAxis, YAxis, ResponsiveContainer } from 'recharts';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@/hooks/use-toast';
import Navbar from '../components/Navbar';
import ExchangeRateBanner from '../components/ExchangeRateBanner';
import PropertyTags from '../components/PropertyTags';
import Map from '../components/Map';
import PhotoSlideshow from '../components/PhotoSlideshow';
import PriceDisplay from '../components/PriceDisplay';
import AuthModal from '../components/AuthModal';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

// Import ETB_EXCHANGE_RATE from AppContext
import { FALLBACK_ETB_EXCHANGE_RATE } from '../context/AppContext';
import { propertyApi, Property as ApiProperty } from '@/lib/api';

interface MarketData {
  name: string;
  value: number;
}

// Use the API Property type with additional fields for compatibility
interface Property extends ApiProperty {
  titleAmharic?: string;
  descriptionAmharic?: string;
  addressAmharic?: string;
  propertyTypeAmharic?: string;
  image?: string;
  yearBuilt?: number;
  lotSize?: number;
}

// Form schema for user information
const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  phone: z.string().min(10, { message: "Please enter a valid phone number." }),
  message: z.string().optional(),
});

const PropertyDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { translate, language, currency, convertCurrency, formatCurrency } = useAppContext();
  const { isAuthenticated } = useContext(AuthContext);
  const [property, setProperty] = useState<Property | null>(null);
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [loading, setLoading] = useState(true);
  const [showContactForm, setShowContactForm] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const { toast } = useToast();

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      message: "",
    },
  });

  useEffect(() => {
    const fetchPropertyDetails = async () => {
      if (!id) return;

      setLoading(true);
      try {
        // Try to fetch from API first
        const property = await propertyApi.getProperty(id);
        setProperty(property);

        // Generate mock market data for now
        const generateMarketData = () => {
          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return months.map(month => ({
            name: month,
            value: Math.floor(Math.random() * 100000) + property.price
          }));
        };

        setMarketData(generateMarketData());
      } catch (error) {
        console.error('Error fetching property:', error);

        // Check if it's a 404 error (property not found)
        if (error.message?.includes('Route not found') || error.message?.includes('Property not found')) {
          // Show a user-friendly error message
          toast({
            variant: "destructive",
            title: translate("Property Not Found", "ንብረት አልተገኘም"),
            description: translate("The property you're looking for doesn't exist or has been removed.", "የሚፈልጉት ንብረት የለም ወይም ተወግዷል።"),
          });

          // Redirect to home page after a delay
          setTimeout(() => {
            window.location.href = '/';
          }, 3000);
          return;
        }

        // For other errors, fallback to mock data
        const mockProperty = {
          id: id,
          title: 'Beautiful Modern Villa',
          price: 8500000, // ETB price
          currency: 'ETB',
          beds: 4,
          baths: 3,
          sqm: 260,
          address: 'Bole, Addis Ababa, Ethiopia',
          lat: 8.9806,
          lng: 38.7578,
          propertyType: 'villa',
          status: 'for_sale',
          description: 'This stunning modern villa offers the perfect blend of luxury and comfort. Located in the prestigious Bole area, this property features spacious rooms, modern amenities, and beautiful finishes throughout. Perfect for families looking for a premium living experience in Addis Ababa.',
          images: [
            'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&h=600&fit=crop',
            'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop',
            'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800&h=600&fit=crop',
            'https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=800&h=600&fit=crop'
          ],
          yearBuilt: 2020,
          lotSize: 0.45,
          features: [
            'Modern Kitchen',
            'Swimming Pool',
            'Garden',
            'Parking for 2 cars',
            'Security System',
            'High-speed Internet',
            'Backup Generator'
          ],
          contact_info: {
            agent_name: 'Nibret Real Estate',
            phone: '+251911234567',
            email: '<EMAIL>'
          },
          views: 156,
          is_featured: true
        };
        setProperty(mockProperty);

        // Mock market data for the past 12 months
        const generateMarketData = () => {
          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return months.map(month => ({
            name: month,
            value: Math.floor(Math.random() * 1000000) + 7500000 // ETB prices
          }));
        };

        setMarketData(generateMarketData());
      } finally {
        setLoading(false);
      }
    };

    fetchPropertyDetails();
  }, [id]);

  const handleAuthRequired = () => {
    setShowAuthModal(true);
  };

  const onContactSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      // Create a lead with the contact information
      const leadData = {
        name: values.name,
        email: values.email,
        phone: values.phone,
        property: property?.title || 'Unknown Property',
        message: values.message || `Interested in property: ${property?.title}`,
      };

      // In a real implementation, you would call leadApi.createLead(leadData)
      // For now, we'll just log it and show success
      console.log("Contact form submitted:", leadData);

      // Show success toast
      toast({
        title: translate("Thank you for your interest!", "ለፍላጎትዎ እናመሰግናለን!"),
        description: translate("A real estate agent will contact you soon.", "የሪል እስቴት ኤጀንት በቅርቡ ያገኝዎታል።"),
      });

      // Reset form and hide it
      form.reset();
      setShowContactForm(false);
    } catch (error) {
      console.error('Error submitting contact form:', error);
      toast({
        variant: "destructive",
        title: translate("Error", "ስህተት"),
        description: translate("Failed to submit your request. Please try again.", "ጥያቄዎን ማስገባት አልተቻለም። እባክዎ እንደገና ይሞክሩ።"),
      });
    }
  };

  if (loading || !property) {
    return (
      <div>
        <Navbar />
        <div className="container mx-auto py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-80 bg-gray-200 rounded-lg w-full"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="flex space-x-4">
              <div className="h-12 bg-gray-200 rounded w-1/3"></div>
              <div className="h-12 bg-gray-200 rounded w-1/3"></div>
              <div className="h-12 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Format price with proper currency conversion
  const formattedPrice = () => {
    // Use property's currency if available, otherwise fall back to ETB
    const sourceCurrency = (property as any).currency || 'ETB';

    // Convert the price from the property's currency to the user's selected currency
    const convertedPrice = convertCurrency(property.price, sourceCurrency);

    // Format the converted price in the user's selected currency
    return formatCurrency(convertedPrice);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      <ExchangeRateBanner />

      {/* Page Header */}
      <div className="bg-nibret-blue text-white py-12">
        <div className="container mx-auto px-4 md:px-8">
          <h1 className={`text-3xl md:text-4xl font-bold ${language === 'am' ? 'amharic' : ''}`}>
            {translate(property.title, property.titleAmharic || property.title)}
          </h1>
          <div className="flex items-center mt-4">
            <MapPin className="h-5 w-5 mr-2" />
            <span className={`${language === 'am' ? 'amharic' : ''}`}>
              {isAuthenticated
                ? translate(property.address, property.addressAmharic || property.address)
                : translate('General Area - Sign in for exact address', 'አጠቃላይ አካባቢ - ትክክለኛ አድራሻ ለማየት ይግቡ')
              }
            </span>
            {!isAuthenticated && (
              <Button
                variant="link"
                size="sm"
                onClick={handleAuthRequired}
                className="ml-2 text-white underline hover:text-gray-200"
              >
                {translate('Sign In', 'ይግቡ')}
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="container mx-auto py-8 px-4 md:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Property Photo Slideshow */}
          <div className="md:col-span-2">
            <PhotoSlideshow
              images={property.images && property.images.length > 0
                ? property.images
                : property.image
                  ? [property.image]
                  : ['https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&h=600&fit=crop']
              }
              title={translate(property.title, property.titleAmharic || property.title)}
              className="w-full shadow-lg"
              showThumbnails={true}
              showControls={true}
              autoPlay={true}
              autoPlayInterval={6000}
            />
          </div>

          {/* Property Price and Quick Info */}
          <div>
            <Card className="h-full bg-white shadow-xl border-0">
              <CardHeader className="bg-nibret-gold/10 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <PriceDisplay
                      price={property.price}
                      originalPrice={property.original_price}
                      discountPercentage={property.discount_percentage}
                      isNegotiable={property.is_negotiable}
                      currency={(property as any).currency}
                      size="xl"
                      orientation="vertical"
                    />
                  </div>
                  {property.is_featured && (
                    <Badge className="bg-yellow-500 text-white">
                      <Star className="w-3 h-3 mr-1 fill-current" />
                      {translate('Featured', 'ተመራጭ')}
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {translate('Status:', 'ሁኔታ:')} <span className="font-medium capitalize">{property.status.replace('_', ' ')}</span>
                </p>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div className="flex items-center justify-between">
                  <h2 className={`text-xl font-semibold ${language === 'am' ? 'amharic' : ''}`}>
                    {translate(property.propertyType, property.propertyTypeAmharic || property.propertyType)}
                  </h2>
                  {property.views && (
                    <div className="flex items-center text-sm text-gray-500">
                      <Eye className="w-4 h-4 mr-1" />
                      {property.views} {translate('views', 'እይታዎች')}
                    </div>
                  )}
                </div>

                {/* Property Tags */}
                <div className="mt-4">
                  <PropertyTags
                    propertyType={property.propertyType}
                    status={property.status}
                    listing_type={property.listing_type}
                    is_featured={property.is_featured}
                    size="default"
                  />
                </div>

                <div className="grid grid-cols-3 gap-3 mt-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg shadow-sm">
                    <BedDouble className="h-5 w-5 mx-auto text-nibret-blue" />
                    <p className="mt-1 text-sm font-medium">{property.beds}</p>
                    <p className="text-xs text-gray-500">{translate('beds', 'አልጋዎች')}</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg shadow-sm">
                    <Bath className="h-5 w-5 mx-auto text-nibret-blue" />
                    <p className="mt-1 text-sm font-medium">{property.baths}</p>
                    <p className="text-xs text-gray-500">{translate('baths', 'መታጠቢያዎች')}</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg shadow-sm">
                    <SquareCode className="h-5 w-5 mx-auto text-nibret-blue" />
                    <p className="mt-1 text-sm font-medium">{property.sqm || property.sqft}</p>
                    <p className="text-xs text-gray-500">{translate('sqm', 'ስኩየር ሜትር')}</p>
                  </div>
                </div>

                {/* Additional Property Info */}
                <div className="space-y-3 mt-6 pt-4 border-t">
                  {property.yearBuilt && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-2 text-gray-500" />
                        <span className="text-sm text-gray-600">{translate('Year Built', 'የተገነባበት ዓመት')}</span>
                      </div>
                      <span className="text-sm font-medium">{property.yearBuilt}</span>
                    </div>
                  )}
                  {property.lotSize && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Ruler className="w-4 h-4 mr-2 text-gray-500" />
                        <span className="text-sm text-gray-600">{translate('Lot Size', 'የቦታ መጠን')}</span>
                      </div>
                      <span className="text-sm font-medium">{property.lotSize} {translate('acres', 'ኤክር')}</span>
                    </div>
                  )}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-2 text-gray-500" />
                      <span className="text-sm text-gray-600">{translate('Property ID', 'የንብረት መለያ')}</span>
                    </div>
                    <span className="text-sm font-medium">#{property.id.slice(-6).toUpperCase()}</span>
                  </div>
                </div>

                {!showContactForm ? (
                  <Button
                    onClick={() => isAuthenticated ? setShowContactForm(true) : handleAuthRequired()}
                    className="w-full mt-6 bg-nibret-blue hover:bg-nibret-blue/90 text-white"
                  >
                    <Users className="mr-2 h-4 w-4" />
                    {isAuthenticated
                      ? translate('Request Information', 'መረጃ ይጠይቁ')
                      : translate('Sign In to Contact', 'ለመገናኘት ይግቡ')
                    }
                  </Button>
                ) : (
                  <Card className="border-0 shadow-sm mt-4">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-semibold">
                        {translate('Contact Us', 'ያግኙን')}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Form {...form}>
                        <form onSubmit={form.handleSubmit(onContactSubmit)} className="space-y-3">
                          <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>{translate('Name', 'ስም')}</FormLabel>
                                <FormControl>
                                  <Input placeholder={translate('Your name', 'ስምዎን ያስገቡ')} {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>{translate('Email', 'ኢሜይል')}</FormLabel>
                                <FormControl>
                                  <Input type="email" placeholder={translate('Your email', 'ኢሜይልዎን ያስገቡ')} {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>{translate('Phone', 'ስልክ')}</FormLabel>
                                <FormControl>
                                  <Input placeholder={translate('Your phone number', 'የስልክ ቁጥርዎን ያስገቡ')} {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="message"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>{translate('Message (optional)', 'መልእክት (አማራጭ)')}</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder={translate('Your questions about this property', 'ስለዚህ ንብረት ያለዎት ጥያቄዎች')}
                                    className="resize-none"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <div className="flex space-x-2 pt-2">
                            <Button
                              type="submit"
                              className="flex-1 bg-nibret-blue hover:bg-nibret-blue/90"
                            >
                              {translate('Submit', 'አስገባ')}
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setShowContactForm(false)}
                              className="flex-1"
                            >
                              {translate('Cancel', 'ሰርዝ')}
                            </Button>
                          </div>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Property Details Tabs */}
        <div className="mt-12">
          <Tabs defaultValue="details" className="bg-white rounded-xl shadow-lg">
            <TabsList className="grid grid-cols-3 mb-6 p-1 rounded-t-xl bg-gray-100">
              <TabsTrigger value="details" className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm">
                <Home className="mr-2 h-4 w-4" />
                {translate('Details', 'ዝርዝሮች')}
              </TabsTrigger>
              <TabsTrigger value="map" className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm">
                <MapPin className="mr-2 h-4 w-4" />
                {translate('Map', 'ካርታ')}
              </TabsTrigger>
              <TabsTrigger value="market" className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm">
                <ChartBar className="mr-2 h-4 w-4" />
                {translate('Market Data', 'የገበያ ውሂብ')}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="p-6">
              <div className="space-y-8">
                {/* Property Description */}
                <div>
                  <h2 className="text-2xl font-bold text-nibret-blue mb-4">
                    {translate('Property Description', 'የንብረት መግለጫ')}
                  </h2>
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <p className={`text-gray-700 leading-relaxed ${language === 'am' ? 'amharic' : ''}`}>
                      {translate(property.description, property.descriptionAmharic || property.description)}
                    </p>
                  </div>
                </div>

                {/* Property Specifications */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="bg-white border rounded-lg p-6 shadow-sm">
                    <h3 className="font-semibold text-lg mb-6 text-nibret-blue flex items-center">
                      <Home className="w-5 h-5 mr-2" />
                      {translate('Property Specifications', 'የንብረት ዝርዝሮች')}
                    </h3>
                    <div className="space-y-4">
                      <div className="flex justify-between py-3 border-b border-gray-100">
                        <span className="text-gray-600 flex items-center">
                          <Home className="w-4 h-4 mr-2" />
                          {translate('Property Type', 'የንብረት አይነት')}
                        </span>
                        <span className="font-medium capitalize">
                          {translate(property.propertyType, property.propertyTypeAmharic || property.propertyType)}
                        </span>
                      </div>
                      <div className="flex justify-between py-3 border-b border-gray-100">
                        <span className="text-gray-600 flex items-center">
                          <BedDouble className="w-4 h-4 mr-2" />
                          {translate('Bedrooms', 'መኝታ ክፍሎች')}
                        </span>
                        <span className="font-medium">{property.beds}</span>
                      </div>
                      <div className="flex justify-between py-3 border-b border-gray-100">
                        <span className="text-gray-600 flex items-center">
                          <Bath className="w-4 h-4 mr-2" />
                          {translate('Bathrooms', 'መታጠቢያ ክፍሎች')}
                        </span>
                        <span className="font-medium">{property.baths}</span>
                      </div>
                      <div className="flex justify-between py-3 border-b border-gray-100">
                        <span className="text-gray-600 flex items-center">
                          <SquareCode className="w-4 h-4 mr-2" />
                          {translate('Floor Area', 'የወለል ስፋት')}
                        </span>
                        <span className="font-medium">{property.sqm || property.sqft} {translate('sqm', 'ስኩየር ሜትር')}</span>
                      </div>
                      {property.yearBuilt && (
                        <div className="flex justify-between py-3 border-b border-gray-100">
                          <span className="text-gray-600 flex items-center">
                            <Calendar className="w-4 h-4 mr-2" />
                            {translate('Year Built', 'የተገነባበት ዓመት')}
                          </span>
                          <span className="font-medium">{property.yearBuilt}</span>
                        </div>
                      )}
                      {property.lotSize && (
                        <div className="flex justify-between py-3 border-b border-gray-100">
                          <span className="text-gray-600 flex items-center">
                            <Ruler className="w-4 h-4 mr-2" />
                            {translate('Lot Size', 'የቦታ መጠን')}
                          </span>
                          <span className="font-medium">{property.lotSize} {translate('acres', 'ኤክር')}</span>
                        </div>
                      )}
                      <div className="flex justify-between py-3">
                        <span className="text-gray-600 flex items-center">
                          <MapPin className="w-4 h-4 mr-2" />
                          {translate('Property ID', 'የንብረት መለያ')}
                        </span>
                        <span className="font-medium">#{property.id.slice(-8).toUpperCase()}</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white border rounded-lg p-6 shadow-sm">
                    <h3 className="font-semibold text-lg mb-6 text-nibret-blue flex items-center">
                      <Star className="w-5 h-5 mr-2" />
                      {translate('Features & Amenities', 'ባህሪያት እና አገልግሎቶች')}
                    </h3>
                    <div className="grid grid-cols-1 gap-4">
                      {/* Dynamic features from property data */}
                      {property.features && property.features.length > 0 ? (
                        property.features.map((feature, index) => (
                          <div key={index} className="flex items-center py-2">
                            <div className="w-2 h-2 bg-nibret-blue rounded-full mr-3"></div>
                            <span className="text-gray-700">{feature}</span>
                          </div>
                        ))
                      ) : (
                        // Default features if none provided
                        <>
                          <div className="flex items-center py-2">
                            <Car className="w-4 h-4 mr-3 text-nibret-blue" />
                            <span className="text-gray-700">{translate('Parking Available', 'የመኪና ማቆሚያ አለ')}</span>
                          </div>
                          <div className="flex items-center py-2">
                            <Shield className="w-4 h-4 mr-3 text-nibret-blue" />
                            <span className="text-gray-700">{translate('Security System', 'የደህንነት ስርዓት')}</span>
                          </div>
                          <div className="flex items-center py-2">
                            <Wifi className="w-4 h-4 mr-3 text-nibret-blue" />
                            <span className="text-gray-700">{translate('Internet Ready', 'ለኢንተርኔት ዝግጁ')}</span>
                          </div>
                          <div className="flex items-center py-2">
                            <Zap className="w-4 h-4 mr-3 text-nibret-blue" />
                            <span className="text-gray-700">{translate('Electricity', 'ኤሌክትሪክ')}</span>
                          </div>
                          <div className="flex items-center py-2">
                            <Droplets className="w-4 h-4 mr-3 text-nibret-blue" />
                            <span className="text-gray-700">{translate('Water Supply', 'የውሃ አቅርቦት')}</span>
                          </div>
                          <div className="flex items-center py-2">
                            <TreePine className="w-4 h-4 mr-3 text-nibret-blue" />
                            <span className="text-gray-700">{translate('Garden/Green Space', 'የአትክልት ስፍራ/አረንጓዴ ቦታ')}</span>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                {property.contact_info && (
                  <div className="bg-nibret-blue/5 border border-nibret-blue/20 rounded-lg p-6">
                    <h3 className="font-semibold text-lg mb-4 text-nibret-blue flex items-center">
                      <Users className="w-5 h-5 mr-2" />
                      {translate('Contact Information', 'የመገናኛ መረጃ')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {property.contact_info.agent_name && (
                        <div className="flex items-center">
                          <Users className="w-4 h-4 mr-2 text-nibret-blue" />
                          <div>
                            <p className="text-sm text-gray-600">{translate('Agent', 'ወኪል')}</p>
                            <p className="font-medium">{property.contact_info.agent_name}</p>
                          </div>
                        </div>
                      )}
                      {property.contact_info.phone && (
                        <div className="flex items-center">
                          <Phone className="w-4 h-4 mr-2 text-nibret-blue" />
                          <div>
                            <p className="text-sm text-gray-600">{translate('Phone', 'ስልክ')}</p>
                            <p className="font-medium">{property.contact_info.phone}</p>
                          </div>
                        </div>
                      )}
                      {property.contact_info.email && (
                        <div className="flex items-center">
                          <Mail className="w-4 h-4 mr-2 text-nibret-blue" />
                          <div>
                            <p className="text-sm text-gray-600">{translate('Email', 'ኢሜይል')}</p>
                            <p className="font-medium">{property.contact_info.email}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="map" className="p-6">
              <div className="space-y-6">
                {/* Location Header */}
                <div>
                  <h2 className="text-2xl font-bold text-nibret-blue mb-4">
                    {translate('Location & Neighborhood', 'አካባቢ እና ሰፈር')}
                  </h2>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center text-gray-700 mb-2">
                      <MapPin className="h-5 w-5 mr-2 text-nibret-blue" />
                      <span className={`font-medium ${language === 'am' ? 'amharic' : ''}`}>
                        {translate(property.address, property.addressAmharic || property.address)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 ml-7">
                      {translate('Click on the map marker for property details', 'ለንብረት ዝርዝሮች በካርታው ምልክት ላይ ይጫኑ')}
                    </p>
                  </div>
                </div>

                {/* Interactive Map */}
                <div className="bg-white border rounded-lg overflow-hidden shadow-lg">
                  <div className="bg-gray-50 px-4 py-3 border-b">
                    <h3 className="font-semibold text-gray-800 flex items-center">
                      <MapPin className="w-4 h-4 mr-2 text-nibret-blue" />
                      {translate('Interactive Map', 'በይነተገናኝ ካርታ')}
                    </h3>
                  </div>
                  <div className="h-64 sm:h-80 md:h-96 lg:h-[450px]">
                    <Map
                      properties={[{
                        id: property.id,
                        lat: property.lat || 8.9806, // Default to Addis Ababa center if no coordinates
                        lng: property.lng || 38.7578,
                        price: property.price,
                        title: property.title,
                        titleAmharic: property.titleAmharic,
                        address: property.address,
                        addressAmharic: property.addressAmharic,
                        image: property.images?.[0] || property.image || 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=400&h=300&fit=crop'
                      }]}
                      center={[property.lng || 38.7578, property.lat || 8.9806]}
                      zoom={15}
                    />
                  </div>
                </div>

                {/* Neighborhood Information */}
                <div>
                  <h3 className="text-xl font-bold text-nibret-blue mb-6">
                    {translate('Neighborhood Highlights', 'የሰፈር ዋና ዋና ነጥቦች')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Education */}
                    <div className="bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center mb-4">
                        <div className="bg-blue-100 p-2 rounded-lg mr-3">
                          <Users className="w-5 h-5 text-blue-600" />
                        </div>
                        <h4 className="font-semibold text-lg text-gray-800">
                          {translate('Education', 'ትምህርት')}
                        </h4>
                      </div>
                      <ul className="space-y-3 text-sm text-gray-600">
                        <li className="flex items-center justify-between">
                          <span>{translate('International Community School', 'ዓለም አቀፍ ማህበረሰብ ትምህርት ቤት')}</span>
                          <span className="text-xs text-gray-500">2km</span>
                        </li>
                        <li className="flex items-center justify-between">
                          <span>{translate('Addis Ababa University', 'አዲስ አበባ ዩኒቨርሲቲ')}</span>
                          <span className="text-xs text-gray-500">5km</span>
                        </li>
                        <li className="flex items-center justify-between">
                          <span>{translate('British International School', 'የብሪታንያ ዓለም አቀፍ ትምህርት ቤት')}</span>
                          <span className="text-xs text-gray-500">3km</span>
                        </li>
                      </ul>
                    </div>

                    {/* Shopping & Dining */}
                    <div className="bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center mb-4">
                        <div className="bg-green-100 p-2 rounded-lg mr-3">
                          <Home className="w-5 h-5 text-green-600" />
                        </div>
                        <h4 className="font-semibold text-lg text-gray-800">
                          {translate('Shopping & Dining', 'ግዢ እና ምግብ')}
                        </h4>
                      </div>
                      <ul className="space-y-3 text-sm text-gray-600">
                        <li className="flex items-center justify-between">
                          <span>{translate('Edna Mall', 'እድና ሞል')}</span>
                          <span className="text-xs text-gray-500">1km</span>
                        </li>
                        <li className="flex items-center justify-between">
                          <span>{translate('Bole Medhanialem', 'ቦሌ መድሃኒዓለም')}</span>
                          <span className="text-xs text-gray-500">2km</span>
                        </li>
                        <li className="flex items-center justify-between">
                          <span>{translate('Friendship Mall', 'ወዳጅነት ሞል')}</span>
                          <span className="text-xs text-gray-500">3km</span>
                        </li>
                      </ul>
                    </div>

                    {/* Transportation */}
                    <div className="bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center mb-4">
                        <div className="bg-purple-100 p-2 rounded-lg mr-3">
                          <Car className="w-5 h-5 text-purple-600" />
                        </div>
                        <h4 className="font-semibold text-lg text-gray-800">
                          {translate('Transportation', 'መጓጓዣ')}
                        </h4>
                      </div>
                      <ul className="space-y-3 text-sm text-gray-600">
                        <li className="flex items-center justify-between">
                          <span>{translate('Bole International Airport', 'ቦሌ ዓለም አቀፍ አውሮፕላን ማረፊያ')}</span>
                          <span className="text-xs text-gray-500">10km</span>
                        </li>
                        <li className="flex items-center justify-between">
                          <span>{translate('Light Rail Station', 'የብርሃን ባቡር ጣቢያ')}</span>
                          <span className="text-xs text-gray-500">2km</span>
                        </li>
                        <li className="flex items-center justify-between">
                          <span>{translate('Main Bus Terminal', 'ዋና የአውቶቡስ ተርሚናል')}</span>
                          <span className="text-xs text-gray-500">5km</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Location Benefits */}
                <div className="bg-nibret-blue/5 border border-nibret-blue/20 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-nibret-blue mb-4">
                    {translate('Location Benefits', 'የአካባቢ ጥቅሞች')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center">
                      <Shield className="w-4 h-4 mr-2 text-nibret-blue" />
                      <span className="text-sm text-gray-700">{translate('Safe neighborhood with 24/7 security', 'በ24/7 ደህንነት ያለው ደህንነቱ የተጠበቀ ሰፈር')}</span>
                    </div>
                    <div className="flex items-center">
                      <Car className="w-4 h-4 mr-2 text-nibret-blue" />
                      <span className="text-sm text-gray-700">{translate('Easy access to main roads', 'ወደ ዋና መንገዶች ቀላል መዳረሻ')}</span>
                    </div>
                    <div className="flex items-center">
                      <TreePine className="w-4 h-4 mr-2 text-nibret-blue" />
                      <span className="text-sm text-gray-700">{translate('Green spaces and parks nearby', 'አቅራቢያ ያሉ አረንጓዴ ቦታዎች እና ፓርኮች')}</span>
                    </div>
                    <div className="flex items-center">
                      <Wifi className="w-4 h-4 mr-2 text-nibret-blue" />
                      <span className="text-sm text-gray-700">{translate('High-speed internet coverage', 'ከፍተኛ ፍጥነት ያለው የኢንተርኔት ሽፋን')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="market" className="p-6">
              <h2 className="text-2xl font-bold text-nibret-blue mb-4">
                {translate('Market Data', 'የገበያ ውሂብ')}
              </h2>
              <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
                <p className="mb-6 text-gray-700">
                  {translate(
                    'Average home prices in this area over the last 12 months',
                    'በዚህ አካባቢ ባለፉት 12 ወራት ውስጥ የቤት ዋጋ አማካይ'
                  )}
                </p>
                <div className="h-64 sm:h-80 md:h-96">
                  <ChartContainer
                    config={{
                      area: {
                        label: "Price",
                        theme: {
                          light: "#8B5CF6",
                          dark: "#8B5CF6",
                        },
                      },
                    }}
                  >
                    <AreaChart data={marketData}>
                      <defs>
                        <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#1A365D" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#1A365D" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <XAxis dataKey="name" />
                      <YAxis
                        tickFormatter={(value) => {
                          if (currency === 'USD') {
                            return `$${(value / 1000)}k`;
                          } else {
                            return `${((value * FALLBACK_ETB_EXCHANGE_RATE) / 1000000)}M`;
                          }
                        }}
                      />
                      <ChartTooltip
                        content={({ active, payload }) => {
                          if (active && payload && payload.length) {
                            const value = payload[0].value as number;
                            const convertedValue = currency === 'USD'
                              ? value
                              : value * ETB_EXCHANGE_RATE;

                            const formattedValue = new Intl.NumberFormat(
                              currency === 'USD' ? 'en-US' : 'en-ET',
                              {
                                style: 'currency',
                                currency: currency,
                                maximumFractionDigits: 0
                              }
                            ).format(convertedValue);

                            return (
                              <ChartTooltipContent>
                                <div className="px-2 py-1">
                                  <p className="text-gray-600">{payload[0].payload.name}</p>
                                  <p className="font-semibold">{formattedValue}</p>
                                </div>
                              </ChartTooltipContent>
                            );
                          }
                          return null;
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="value"
                        stroke="#1A365D"
                        fillOpacity={1}
                        fill="url(#colorValue)"
                      />
                    </AreaChart>
                  </ChartContainer>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        defaultTab="login"
      />
    </div>
  );
};

export default PropertyDetails;
