
import React from 'react';
import { useAppContext } from '../context/AppContext';
import Navbar from '../components/Navbar';
import PageHeader from '../components/PageHeader';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, TrendingUp, Home, Users } from 'lucide-react';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import Footer from '@/components/Footer';

const Sell = () => {
  const { translate } = useAppContext();
  const { toast } = useToast();

  // Form schema
  const formSchema = z.object({
    name: z.string().min(2, { message: "Name must be at least 2 characters." }),
    email: z.string().email({ message: "Please enter a valid email address." }),
    phone: z.string().min(10, { message: "Please enter a valid phone number." }),
    address: z.string().min(5, { message: "Please enter your property address." }),
    propertyType: z.string().min(2, { message: "Please specify your property type." }),
    message: z.string().optional(),
  });

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      address: "",
      propertyType: "",
      message: "",
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    console.log("Form submitted:", values);
    
    toast({
      title: translate("Request Received", "ጥያቄ ተቀብሏል"),
      description: translate("Our team will contact you shortly to discuss your property.", "ቡድናችን ስለ ንብረትዎ ለመወያየት በቅርቡ ያገኝዎታል።"),
    });
    
    form.reset();
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      
      <PageHeader 
        title={translate("Sell Your Property", "ንብረትዎን ይሽጡ")}
        subtitle={translate("Get the best value for your property with our experienced agents", "ከልምድ ባለው ወኪሎቻችን ለንብረትዎ ምርጥ ዋጋ ያግኙ")}
        backgroundImage="https://picsum.photos/id/1031/1600/800"
      />
      
      <div className="container mx-auto py-12 px-4 md:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-12">
          <div className="lg:col-span-3">
            <h2 className="text-3xl font-bold text-nibret-blue mb-6">
              {translate("Why Sell With Nibret?", "ለምን በኒብረት ይሸጣሉ?")}
            </h2>
            
            <div className="space-y-8">
              {[
                {
                  icon: <TrendingUp className="h-8 w-8 text-nibret-gold" />,
                  title: translate("Maximum Value", "ከፍተኛ ዋጋ"),
                  description: translate(
                    "Our marketing strategies and negotiation expertise ensure you get the best possible price for your property.",
                    "የመሸጫ ስልቶቻችንና የድርድር ችሎታችን ለንብረትዎ የተቻለውን ከፍተኛ ዋጋ እንደሚያገኙ ያረጋግጣሉ።"
                  )
                },
                {
                  icon: <Home className="h-8 w-8 text-nibret-gold" />,
                  title: translate("Premium Marketing", "ፕሪሚየም ማርኬቲንግ"),
                  description: translate(
                    "Professional photography, virtual tours, and targeted advertisements to showcase your property to the right buyers.",
                    "ንብረትዎን ለትክክለኛ ገዢዎች ለማሳየት ሙያዊ ፎቶግራፍ፣ ቨርቹዋል ጉብኝት እና የተለየ ማስታወቂያ።"
                  )
                },
                {
                  icon: <Users className="h-8 w-8 text-nibret-gold" />,
                  title: translate("Expert Guidance", "የመምህራን መመሪያ"),
                  description: translate(
                    "Personalized support from local market experts throughout the entire selling process.",
                    "በሙሉ የመሸጫ ሂደት ውስጥ ከአካባቢው የገበያ ባለሙያዎች የግል ድጋፍ።"
                  )
                }
              ].map((feature, index) => (
                <div key={index} className="flex gap-4">
                  <div className="flex-shrink-0 bg-nibret-blue/5 p-3 rounded-full h-14 w-14 flex items-center justify-center">
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-nibret-blue mb-2">{feature.title}</h3>
                    <p className="text-gray-700">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-10 bg-white rounded-xl shadow-lg p-8 border border-gray-100">
              <h3 className="text-2xl font-bold text-nibret-blue mb-6">
                {translate("Our Selling Process", "የእኛ የመሸጫ ሂደት")}
              </h3>
              
              <div className="space-y-6">
                {[
                  {
                    step: "01",
                    title: translate("Property Valuation", "የንብረት ዋጋ ግመታ"),
                    description: translate("We'll evaluate your property to determine its market value.", "የገበያውን ዋጋ ለመወሰን ንብረቱን እንገመግማለን።")
                  },
                  {
                    step: "02",
                    title: translate("Marketing Strategy", "የሽያጭ ስልት"),
                    description: translate("Custom marketing plan tailored to your property's unique features.", "ለንብረትዎ ልዩ ባህሪዎች የተበጀ ልዩ የሽያጭ ዕቅድ።")
                  },
                  {
                    step: "03",
                    title: translate("Listing & Showings", "ዝርዝር እና ትዕይንቶች"),
                    description: translate("Property listing across key platforms and coordinated viewings.", "በዋና መድረኮች የንብረት ዝርዝር እና የተቀናጁ የምልከታዎች።")
                  },
                  {
                    step: "04",
                    title: translate("Offers & Negotiation", "ቅናሾች እና ድርድር"),
                    description: translate("Expert negotiation to secure the best terms and price.", "ምርጥ ውሎችንና ዋጋን ለማረጋገጥ የባለሙያ ድርድር።")
                  },
                  {
                    step: "05",
                    title: translate("Closing", "መዝጊያ"),
                    description: translate("Seamless transaction management through to completion.", "እስከ መጨረሻው ድረስ ያለችግር የግብይት አስተዳደር።")
                  }
                ].map((step, index) => (
                  <div key={index} className="flex items-start">
                    <div className="flex-shrink-0 bg-nibret-gold/10 p-4 rounded-full mr-4 text-nibret-blue font-bold">
                      {step.step}
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-nibret-blue mb-1">{step.title}</h4>
                      <p className="text-gray-700">{step.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          <div className="lg:col-span-2">
            <Card className="border-0 shadow-xl bg-white sticky top-24">
              <CardHeader className="bg-nibret-blue text-white rounded-t-xl">
                <CardTitle className="text-xl font-semibold">
                  {translate("Get Your Property Valuation", "የንብረትዎን ዋጋ ይቀበሉ")}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate("Full Name", "ሙሉ ስም")}</FormLabel>
                          <FormControl>
                            <Input placeholder={translate("Enter your name", "ስምዎን ያስገቡ")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate("Email", "ኢሜይል")}</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder={translate("Enter your email", "ኢሜይልዎን ያስገቡ")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate("Phone", "ስልክ")}</FormLabel>
                          <FormControl>
                            <Input placeholder={translate("Enter your phone number", "የስልክ ቁጥርዎን ያስገቡ")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate("Property Address", "የንብረት አድራሻ")}</FormLabel>
                          <FormControl>
                            <Input placeholder={translate("Enter property address", "የንብረት አድራሻ ያስገቡ")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="propertyType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate("Property Type", "የንብረት አይነት")}</FormLabel>
                          <FormControl>
                            <Input placeholder={translate("e.g. Apartment, Villa, etc.", "ለምሳሌ፡ አፓርታማ፣ ቪላ፣ ወዘተ።")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="message"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate("Additional Information", "ተጨማሪ መረጃ")}</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder={translate("Tell us more about your property", "ስለ ንብረትዎ ተጨማሪ ይንገሩን")} 
                              className="resize-none min-h-[120px]" 
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <Button type="submit" className="w-full bg-nibret-blue hover:bg-nibret-blue/90">
                      {translate("Request Valuation", "ዋጋ መገመት ይጠይቁ")}
                    </Button>
                  </form>
                </Form>
                
                <div className="mt-6 pt-6 border-t border-gray-100">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-green-500 mr-2" />
                    <p className="text-sm text-gray-600">
                      {translate("Free, no-obligation valuation", "ነፃ፣ ያለ ግዴታ ዋጋ ግመታ")}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-2" />
                    <p className="text-sm text-gray-600">
                      {translate("Expert local market knowledge", "የባለሙያ የአካባቢ ገበያ እውቀት")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default Sell;
