import React, { useState, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAppContext } from '../context/AppContext';
import { AuthContext } from '../App';
import Navbar from '../components/Navbar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, MapPin, DollarSign, Home, Bed, Bath, Square, Save, Eye, Archive, FileText, Plus, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { propertyApi } from '@/lib/api';
import Footer from '@/components/Footer';
import LocationPicker from '@/components/LocationPicker';
import ImageUpload from '@/components/ImageUpload';

// Form validation schema
const propertySchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters'),
  description: z.string().optional(),
  price: z.number().min(1, 'Price must be greater than 0'),
  currency: z.enum(['ETB', 'USD']).default('ETB'),
  beds: z.number().min(0, 'Bedrooms cannot be negative').max(20, 'Maximum 20 bedrooms'),
  baths: z.number().min(0, 'Bathrooms cannot be negative').max(20, 'Maximum 20 bathrooms'),
  sqm: z.number().min(1, 'Square meters must be at least 1'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  lat: z.number().min(-90).max(90).optional(),
  lng: z.number().min(-180).max(180).optional(),
  propertyType: z.enum(['house', 'apartment', 'condo', 'villa', 'townhouse', 'studio', 'office', 'other']),
  status: z.enum(['for_sale', 'for_rent', 'sold', 'rented', 'off_market']).default('for_sale'),
  listing_type: z.enum(['sale', 'rent', 'both']).default('sale'),
  yearBuilt: z.number().min(1800).max(new Date().getFullYear() + 5).optional(),
  lotSize: z.number().min(0).optional(),
  features: z.array(z.string()).default([]),
  images: z.array(z.string().url()).default([]),
  contact_info: z.object({
    phone: z.string().optional(),
    email: z.string().email().optional(),
    agent_name: z.string().optional(),
  }).optional(),
  is_negotiable: z.boolean().default(false),
  discount_percentage: z.number().min(0).max(90).default(0),
  original_price: z.number().min(0).optional(),
});

type PropertyFormData = z.infer<typeof propertySchema>;

const PropertyUpload = () => {
  const { translate } = useAppContext();
  const { isAuthenticated } = useContext(AuthContext);
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [newFeature, setNewFeature] = useState('');

  const form = useForm<PropertyFormData>({
    resolver: zodResolver(propertySchema),
    defaultValues: {
      title: '',
      description: '',
      price: 1, // Start with minimum valid value
      currency: 'ETB',
      beds: 1, // Start with minimum valid value
      baths: 1, // Start with minimum valid value
      sqm: 1, // Start with minimum valid value
      address: '',
      propertyType: 'house',
      status: 'for_sale',
      listing_type: 'sale',
      features: [],
      images: [],
      contact_info: {
        phone: '',
        email: '',
        agent_name: '',
      },
      is_negotiable: false,
      discount_percentage: 0,
      original_price: undefined,
    },
  });

  // Check authentication and admin role
  React.useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    // Get current user from localStorage
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        setCurrentUser(user);

        // Check if user is admin
        if (user.role !== 'ADMIN') {
          toast({
            variant: "destructive",
            title: "Access Denied",
            description: "Only administrators can upload properties. Customers can contact us to list their properties.",
          });
          navigate('/dashboard');
          return;
        }
      } catch (error) {
        console.error('Error parsing user data:', error);
        navigate('/login');
      }
    } else {
      navigate('/login');
    }
  }, [isAuthenticated, navigate, toast]);

  const onSubmit = async (data: PropertyFormData, publishStatus: 'draft' | 'published' = 'draft') => {
    setIsLoading(true);
    try {
      // Ensure all numeric fields are properly converted
      const propertyData = {
        ...data,
        price: Number(data.price) || 0,
        beds: Number(data.beds) || 0,
        baths: Number(data.baths) || 0,
        sqm: Number(data.sqm) || 1, // Ensure minimum value of 1
        lat: data.lat ? Number(data.lat) : undefined,
        lng: data.lng ? Number(data.lng) : undefined,
        yearBuilt: data.yearBuilt ? Number(data.yearBuilt) : undefined,
        lotSize: data.lotSize ? Number(data.lotSize) : undefined,
        publish_status: publishStatus,
      };

      // Clean up contact_info - remove empty strings
      if (propertyData.contact_info) {
        const contactInfo = { ...propertyData.contact_info };

        // Remove empty strings
        if (!contactInfo.phone || contactInfo.phone.trim() === '') {
          delete contactInfo.phone;
        }
        if (!contactInfo.email || contactInfo.email.trim() === '') {
          delete contactInfo.email;
        }
        if (!contactInfo.agent_name || contactInfo.agent_name.trim() === '') {
          delete contactInfo.agent_name;
        }

        // If all fields are empty, remove contact_info entirely
        if (Object.keys(contactInfo).length === 0) {
          delete propertyData.contact_info;
        } else {
          propertyData.contact_info = contactInfo;
        }
      }

      // Validate required numeric fields
      if (propertyData.price <= 0) {
        toast({
          variant: "destructive",
          title: "Validation Error",
          description: "Price must be greater than 0",
        });
        setIsLoading(false);
        return;
      }

      if (propertyData.sqm < 1) {
        toast({
          variant: "destructive",
          title: "Validation Error",
          description: "Square meters must be at least 1",
        });
        setIsLoading(false);
        return;
      }

      const response = await propertyApi.createProperty(propertyData);

      // Check if response indicates success (either has success field or has data/id)
      const isSuccess = response.success || response.data || response.id;

      if (isSuccess) {
        toast({
          title: publishStatus === 'published' ? 'Property Published!' : 'Property Saved as Draft!',
          description: publishStatus === 'published'
            ? 'Your property has been published and is now live.'
            : 'Your property has been saved as a draft. You can publish it later.',
        });

        navigate('/my-properties');
      } else {
        throw new Error(response.error || 'Failed to create property');
      }
    } catch (error: any) {
      console.error('Property creation error:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to create property. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addFeature = () => {
    if (newFeature.trim()) {
      const currentFeatures = form.getValues('features');
      form.setValue('features', [...currentFeatures, newFeature.trim()]);
      setNewFeature('');
    }
  };

  const removeFeature = (index: number) => {
    const currentFeatures = form.getValues('features');
    form.setValue('features', currentFeatures.filter((_, i) => i !== index));
  };



  if (!isAuthenticated || !currentUser || currentUser.role !== 'ADMIN') {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />

      <div className="container mx-auto py-12 px-4 md:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-nibret-blue mb-4">
              {translate("Upload Property", "ንብረት ይላኩ")}
            </h1>
            <p className="text-gray-600">
              {translate("List your property and reach thousands of potential buyers", "ንብረትዎን ዝርዝር ውስጥ ያስገቡ እና በሺዎች የሚቆጠሩ ገዢዎችን ያግኙ")}
            </p>
          </div>

          <Form {...form}>
            <form className="space-y-8">
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="media">Media</TabsTrigger>
                  <TabsTrigger value="contact">Contact</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Home className="mr-2 h-5 w-5" />
                        {translate("Basic Information", "መሰረታዊ መረጃ")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{translate("Property Title", "የንብረት ርዕስ")}</FormLabel>
                            <FormControl>
                              <Input placeholder="Beautiful 3-bedroom villa with garden" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{translate("Description", "መግለጫ")}</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Describe your property in detail..."
                                rows={4}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="propertyType"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{translate("Property Type", "የንብረት አይነት")}</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select property type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="house">House</SelectItem>
                                  <SelectItem value="apartment">Apartment</SelectItem>
                                  <SelectItem value="condo">Condo</SelectItem>
                                  <SelectItem value="villa">Villa</SelectItem>
                                  <SelectItem value="townhouse">Townhouse</SelectItem>
                                  <SelectItem value="studio">Studio</SelectItem>
                                  <SelectItem value="office">Office</SelectItem>
                                  <SelectItem value="other">Other</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="listing_type"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{translate("Listing Type", "የዝርዝር አይነት")}</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select listing type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="sale">For Sale</SelectItem>
                                  <SelectItem value="rent">For Rent</SelectItem>
                                  <SelectItem value="both">Both</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="address"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{translate("Address", "አድራሻ")}</FormLabel>
                            <FormControl>
                              <Input placeholder="123 Main Street, Addis Ababa, Ethiopia" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="details" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Square className="mr-2 h-5 w-5" />
                        {translate("Property Details", "የንብረት ዝርዝሮች")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div className="md:col-span-2 space-y-4">
                          <div className="grid grid-cols-3 gap-2">
                            <div className="col-span-2">
                              <FormField
                                control={form.control}
                                name="price"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="flex items-center text-sm font-medium">
                                      <DollarSign className="mr-1 h-4 w-4" />
                                      {translate("Price", "ዋጋ")}
                                    </FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        placeholder="500000"
                                        className="rounded-lg border-gray-200 focus:border-primary focus:ring-primary"
                                        value={field.value || ''}
                                        onChange={(e) => {
                                          const value = e.target.value;
                                          field.onChange(value === '' ? 1 : parseFloat(value) || 1);
                                        }}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            <FormField
                              control={form.control}
                              name="currency"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-sm font-medium">
                                    {translate("Currency", "ምንዛሬ")}
                                  </FormLabel>
                                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                      <SelectTrigger className="rounded-lg border-gray-200 focus:border-primary focus:ring-primary">
                                        <SelectValue />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="ETB">ETB</SelectItem>
                                      <SelectItem value="USD">USD</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* Pricing Options */}
                          <div className="space-y-4 border-t pt-4">
                            <h4 className="text-sm font-medium text-gray-700">
                              {translate("Pricing Options", "የዋጋ አማራጮች")}
                            </h4>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name="is_negotiable"
                                render={({ field }) => (
                                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                                    <div className="space-y-0.5">
                                      <FormLabel className="text-base">
                                        {translate("Negotiable Price", "ተደራደር ዋጋ")}
                                      </FormLabel>
                                      <FormDescription>
                                        {translate("Allow buyers to negotiate the price", "ገዢዎች ዋጋውን እንዲደራደሩ ፍቀድ")}
                                      </FormDescription>
                                    </div>
                                    <FormControl>
                                      <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                      />
                                    </FormControl>
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="discount_percentage"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>{translate("Discount %", "ቅናሽ %")}</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        min="0"
                                        max="90"
                                        placeholder="0"
                                        value={field.value || ''}
                                        onChange={(e) => {
                                          const value = e.target.value;
                                          field.onChange(value === '' ? 0 : parseFloat(value) || 0);
                                        }}
                                      />
                                    </FormControl>
                                    <FormDescription>
                                      {translate("Optional discount percentage", "አማራጭ ቅናሽ መቶኛ")}
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            <FormField
                              control={form.control}
                              name="original_price"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>{translate("Original Price (if discounted)", "ዋናው ዋጋ (ቅናሽ ካለ)")}</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="600000"
                                      value={field.value || ''}
                                      onChange={(e) => {
                                        const value = e.target.value;
                                        field.onChange(value === '' ? undefined : parseFloat(value) || undefined);
                                      }}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    {translate("Original price before discount", "ከቅናሽ በፊት ዋናው ዋጋ")}
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        <FormField
                          control={form.control}
                          name="beds"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center">
                                <Bed className="mr-1 h-4 w-4" />
                                {translate("Bedrooms", "መኝታ ክፍሎች")}
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="3"
                                  value={field.value || ''}
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    field.onChange(value === '' ? 0 : parseInt(value) || 0);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="baths"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center">
                                <Bath className="mr-1 h-4 w-4" />
                                {translate("Bathrooms", "መታጠቢያ ክፍሎች")}
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="2"
                                  value={field.value || ''}
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    field.onChange(value === '' ? 0 : parseInt(value) || 0);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="sqm"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{translate("Square Meters", "ስኩየር ሜትር")}</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="200"
                                  value={field.value || ''}
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    field.onChange(value === '' ? 1 : parseInt(value) || 1);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="yearBuilt"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{translate("Year Built", "የተሰራበት ዓመት")}</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="2020"
                                  value={field.value || ''}
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    field.onChange(value === '' ? undefined : parseInt(value) || undefined);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="lotSize"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{translate("Lot Size (acres)", "የመሬት መጠን")}</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  step="0.01"
                                  placeholder="0.25"
                                  value={field.value || ''}
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    field.onChange(value === '' ? undefined : parseFloat(value) || undefined);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Features Section */}
                      <div className="space-y-4">
                        <Label>{translate("Features", "ባህሪያት")}</Label>
                        <div className="flex gap-2">
                          <Input
                            value={newFeature}
                            onChange={(e) => setNewFeature(e.target.value)}
                            placeholder="Add a feature (e.g., Swimming Pool)"
                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                          />
                          <Button type="button" onClick={addFeature} size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {form.watch('features').map((feature, index) => (
                            <Badge key={index} variant="secondary" className="flex items-center gap-1">
                              {feature}
                              <X
                                className="h-3 w-3 cursor-pointer"
                                onClick={() => removeFeature(index)}
                              />
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="media" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Upload className="mr-2 h-5 w-5" />
                        {translate("Images & Media", "ምስሎች እና ሚዲያ")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ImageUpload
                        images={form.watch('images')}
                        onImagesChange={(images) => form.setValue('images', images)}
                        maxImages={10}
                        allowUrlInput={true}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="contact" className="space-y-6">
                  {/* Location Section */}
                  <LocationPicker
                    lat={form.watch('lat')}
                    lng={form.watch('lng')}
                    onLocationChange={(lat, lng) => {
                      form.setValue('lat', lat);
                      form.setValue('lng', lng);
                    }}
                  />

                  {/* Contact Information Section */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <MapPin className="mr-2 h-5 w-5" />
                        {translate("Contact Information", "የመገናኛ መረጃ")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="contact_info.agent_name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{translate("Agent Name", "የወኪል ስም")}</FormLabel>
                              <FormControl>
                                <Input placeholder="John Doe" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="contact_info.phone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{translate("Phone Number", "ስልክ ቁጥር")}</FormLabel>
                              <FormControl>
                                <Input placeholder="+251911234567" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="contact_info.email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{translate("Email Address", "ኢሜይል አድራሻ")}</FormLabel>
                            <FormControl>
                              <Input type="email" placeholder="<EMAIL>" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>

              {/* Action Buttons */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-600">
                      {translate("Save as draft to continue editing later, or publish to make it live.", "ለኋላ ለማርትዕ እንደ ረቂቅ ያስቀምጡ፣ ወይም ቀጥታ ለማድረግ ያትሙ።")}
                    </div>
                    <div className="flex gap-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => onSubmit(form.getValues(), 'draft')}
                        disabled={isLoading}
                        className="flex items-center gap-2"
                      >
                        <FileText className="h-4 w-4" />
                        {translate("Save as Draft", "እንደ ረቂቅ ያስቀምጡ")}
                      </Button>
                      <Button
                        type="button"
                        onClick={() => onSubmit(form.getValues(), 'published')}
                        disabled={isLoading}
                        className="bg-nibret-blue hover:bg-nibret-blue/90 flex items-center gap-2"
                      >
                        <Eye className="h-4 w-4" />
                        {translate("Publish Property", "ንብረት ያትሙ")}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </form>
          </Form>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default PropertyUpload;
