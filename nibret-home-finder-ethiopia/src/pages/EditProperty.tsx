import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { useAppContext } from '@/context/AppContext';
import { propertyApi } from '@/lib/api';
import { Property } from '@/lib/api/properties';
import Navbar from '@/components/Navbar';
import ImageUpload from '@/components/ImageUpload';
import LocationPicker from '@/components/LocationPicker';
import { Edit, Save, ArrowLeft, Loader2 } from 'lucide-react';

// Property form schema
const propertySchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().min(10, 'Description must be at least 10 characters').max(2000, 'Description too long'),
  price: z.number().min(1, 'Price must be greater than 0'),
  currency: z.enum(['ETB', 'USD']).default('ETB'),
  beds: z.number().min(0, 'Bedrooms cannot be negative').max(20, 'Maximum 20 bedrooms'),
  baths: z.number().min(0, 'Bathrooms cannot be negative').max(20, 'Maximum 20 bathrooms'),
  sqm: z.number().min(1, 'Square meters must be at least 1'),
  address: z.string().min(1, 'Address is required').max(500, 'Address too long'),
  propertyType: z.enum(['house', 'apartment', 'condo', 'villa', 'townhouse', 'studio', 'office', 'other']),
  lat: z.number().min(-90).max(90).optional(),
  lng: z.number().min(-180).max(180).optional(),
  status: z.enum(['for_sale', 'for_rent', 'sold', 'rented', 'off_market']).default('for_sale'),
  listing_type: z.enum(['sale', 'rent', 'both']).default('sale'),
  yearBuilt: z.number().min(1800).max(new Date().getFullYear() + 5).optional(),
  lotSize: z.number().min(0).optional(),
  features: z.array(z.string()).default([]),
  images: z.array(z.string().url()).default([]),
  contact_info: z.object({
    phone: z.string().optional(),
    email: z.string().email().optional(),
    agent_name: z.string().optional(),
  }).optional(),
  is_negotiable: z.boolean().default(false),
  discount_percentage: z.number().min(0).max(90).default(0),
  original_price: z.number().min(0).optional(),
});

type PropertyFormData = z.infer<typeof propertySchema>;

const EditProperty = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { translate } = useAppContext();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingProperty, setIsLoadingProperty] = useState(true);
  const [property, setProperty] = useState<Property | null>(null);

  const form = useForm<PropertyFormData>({
    resolver: zodResolver(propertySchema),
    defaultValues: {
      title: '',
      description: '',
      price: 1,
      currency: 'ETB',
      beds: 1,
      baths: 1,
      sqm: 1,
      address: '',
      propertyType: 'house',
      status: 'for_sale',
      listing_type: 'sale',
      features: [],
      images: [],
      contact_info: {
        phone: '',
        email: '',
        agent_name: '',
      },
    },
  });

  // Load property data
  useEffect(() => {
    const loadProperty = async () => {
      if (!id) {
        navigate('/my-properties');
        return;
      }

      try {
        setIsLoadingProperty(true);
        const propertyData = await propertyApi.getProperty(id);
        setProperty(propertyData);

        // Populate form with existing data
        form.reset({
          title: propertyData.title || '',
          description: propertyData.description || '',
          price: propertyData.price || 1,
          currency: propertyData.currency || 'ETB',
          beds: propertyData.beds || 1,
          baths: propertyData.baths || 1,
          sqm: propertyData.sqm || 1,
          address: propertyData.address || '',
          propertyType: propertyData.propertyType || 'house',
          lat: propertyData.lat,
          lng: propertyData.lng,
          status: propertyData.status || 'for_sale',
          listing_type: propertyData.listing_type || 'sale',
          yearBuilt: propertyData.yearBuilt,
          lotSize: propertyData.lotSize,
          features: propertyData.features || [],
          images: propertyData.images || [],
          contact_info: propertyData.contact_info || {
            phone: '',
            email: '',
            agent_name: '',
          },
          is_negotiable: propertyData.is_negotiable || false,
          discount_percentage: propertyData.discount_percentage || 0,
          original_price: propertyData.original_price,
        });
      } catch (error: any) {
        console.error('Error loading property:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load property. Please try again.',
        });
        navigate('/my-properties');
      } finally {
        setIsLoadingProperty(false);
      }
    };

    loadProperty();
  }, [id, navigate, form, toast]);

  const onSubmit = async (data: PropertyFormData) => {
    if (!id) return;

    setIsLoading(true);
    try {
      // Ensure all numeric fields are properly converted
      const propertyData = {
        ...data,
        price: Number(data.price) || 0,
        beds: Number(data.beds) || 0,
        baths: Number(data.baths) || 0,
        sqm: Number(data.sqm) || 1,
        lat: data.lat ? Number(data.lat) : undefined,
        lng: data.lng ? Number(data.lng) : undefined,
        yearBuilt: data.yearBuilt ? Number(data.yearBuilt) : undefined,
        lotSize: data.lotSize ? Number(data.lotSize) : undefined,
      };

      console.log('Updating property with data:', propertyData);

      const updatedProperty = await propertyApi.updateProperty(id, propertyData);

      toast({
        title: translate('Success', 'ተሳክቷል'),
        description: translate('Property updated successfully!', 'ንብረት በተሳካ ሁኔታ ተዘምኗል!'),
      });

      // Navigate back to property management
      navigate('/my-properties');
    } catch (error: any) {
      console.error('Error updating property:', error);
      toast({
        variant: 'destructive',
        title: translate('Error', 'ስህተት'),
        description: error.response?.data?.error || translate('Failed to update property. Please try again.', 'ንብረት ማዘመን አልተሳካም። እባክዎ እንደገና ይሞክሩ።'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingProperty) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
        <Navbar />
        <div className="container mx-auto py-12 px-4 md:px-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">{translate('Loading property...', 'ንብረት በመጫን ላይ...')}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
        <Navbar />
        <div className="container mx-auto py-12 px-4 md:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {translate('Property Not Found', 'ንብረት አልተገኘም')}
            </h1>
            <Button onClick={() => navigate('/my-properties')}>
              {translate('Back to Properties', 'ወደ ንብረቶች ተመለስ')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      
      <div className="container mx-auto py-12 px-4 md:px-8">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Button
              variant="ghost"
              onClick={() => navigate('/my-properties')}
              className="mr-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {translate('Back', 'ተመለስ')}
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-nibret-blue mb-2">
                <Edit className="inline mr-2 h-8 w-8" />
                {translate('Edit Property', 'ንብረት አርትዕ')}
              </h1>
              <p className="text-gray-600">
                {translate('Update your property information', 'የንብረት መረጃዎን ያዘምኑ')}
              </p>
            </div>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic">{translate('Basic Info', 'መሰረታዊ መረጃ')}</TabsTrigger>
                <TabsTrigger value="details">{translate('Details', 'ዝርዝሮች')}</TabsTrigger>
                <TabsTrigger value="media">{translate('Images', 'ምስሎች')}</TabsTrigger>
                <TabsTrigger value="contact">{translate('Location & Contact', 'አድራሻ እና ግንኙነት')}</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>{translate('Basic Information', 'መሰረታዊ መረጃ')}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{translate('Property Title', 'የንብረት ርዕስ')}</FormLabel>
                            <FormControl>
                              <Input placeholder={translate('Enter property title', 'የንብረት ርዕስ ያስገቡ')} {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="propertyType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{translate('Property Type', 'የንብረት አይነት')}</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder={translate('Select property type', 'የንብረት አይነት ይምረጡ')} />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="house">{translate('House', 'ቤት')}</SelectItem>
                                <SelectItem value="apartment">{translate('Apartment', 'አፓርታማ')}</SelectItem>
                                <SelectItem value="condo">{translate('Condo', 'ኮንዶ')}</SelectItem>
                                <SelectItem value="villa">{translate('Villa', 'ቪላ')}</SelectItem>
                                <SelectItem value="townhouse">{translate('Townhouse', 'ታውን ሃውስ')}</SelectItem>
                                <SelectItem value="studio">{translate('Studio', 'ስቱዲዮ')}</SelectItem>
                                <SelectItem value="other">{translate('Other', 'ሌላ')}</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate('Description', 'መግለጫ')}</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder={translate('Describe your property...', 'ንብረትዎን ይግለጹ...')}
                              className="min-h-[120px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate('Address', 'አድራሻ')}</FormLabel>
                          <FormControl>
                            <Input placeholder={translate('Enter property address', 'የንብረት አድራሻ ያስገቡ')} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="details" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>{translate('Property Details', 'የንብረት ዝርዝሮች')}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      <FormField
                        control={form.control}
                        name="price"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{translate('Price (ETB)', 'ዋጋ (ብር)')}</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="beds"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{translate('Bedrooms', 'መኝታ ቤቶች')}</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="20"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="baths"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{translate('Bathrooms', 'መታጠቢያ ቤቶች')}</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="20"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="sqft"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{translate('Square Feet', 'ካሬ ሜትር')}</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="media" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>{translate('Property Images', 'የንብረት ምስሎች')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ImageUpload
                      images={form.watch('images')}
                      onImagesChange={(images) => form.setValue('images', images)}
                      maxImages={10}
                      allowUrlInput={true}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="contact" className="space-y-6">
                <LocationPicker
                  lat={form.watch('lat')}
                  lng={form.watch('lng')}
                  onLocationChange={(lat, lng) => {
                    form.setValue('lat', lat);
                    form.setValue('lng', lng);
                  }}
                />
              </TabsContent>
            </Tabs>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/my-properties')}
                disabled={isLoading}
              >
                {translate('Cancel', 'ሰርዝ')}
              </Button>
              <Button type="submit" disabled={isLoading} className="bg-nibret-blue hover:bg-nibret-blue/90">
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {translate('Updating...', 'በማዘመን ላይ...')}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {translate('Update Property', 'ንብረት አዘምን')}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default EditProperty;
