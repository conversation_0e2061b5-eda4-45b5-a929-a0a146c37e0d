import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { useAppContext } from '../context/AppContext';
import { AuthContext } from '../App';
import Navbar from '../components/Navbar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import PropertyCard from '@/components/PropertyCard';
import { 
  Eye, 
  Edit, 
  Archive, 
  FileText, 
  MoreVertical, 
  Plus, 
  Home,
  DollarSign,
  MapPin,
  Calendar,
  Trash2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { propertyApi, Property } from '@/lib/api';
import Footer from '@/components/Footer';
import AdminPropertyCard from '@/components/AdminPropertyCard';

const PropertyManagement = () => {
  const { translate, convertCurrency, formatCurrency } = useAppContext();
  const { isAuthenticated } = useContext(AuthContext);
  const { toast } = useToast();
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    if (isAuthenticated) {
      loadProperties();
    }
  }, [isAuthenticated, activeTab]);

  const loadProperties = async () => {
    setLoading(true);
    try {
      const filters: any = {};
      if (activeTab !== 'all') {
        filters.publish_status = activeTab;
      }

      const response = await propertyApi.getMyProperties(filters);
      setProperties(response.data || []);
    } catch (error: any) {
      console.error('Error loading properties:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load properties. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };



  const handleDelete = async (propertyId: string) => {
    try {
      await propertyApi.deleteProperty(propertyId);
      toast({
        title: 'Success',
        description: 'Property deleted successfully.',
      });
      loadProperties();
    } catch (error: any) {
      console.error('Error deleting property:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete property. Please try again.',
      });
    }
  };

  const handleStatusChange = async (propertyId: string, newStatus: string) => {
    try {
      let response;
      switch (newStatus) {
        case 'published':
          response = await propertyApi.publishProperty(propertyId);
          break;
        case 'draft':
          response = await propertyApi.setPropertyAsDraft(propertyId);
          break;
        case 'archived':
          response = await propertyApi.archiveProperty(propertyId);
          break;
        default:
          throw new Error('Invalid status');
      }

      toast({
        title: 'Success',
        description: `Property ${newStatus} successfully.`,
      });
      loadProperties();
    } catch (error: any) {
      console.error('Error changing property status:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to change property status. Please try again.',
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: 'Draft', variant: 'secondary' as const },
      published: { label: 'Published', variant: 'default' as const },
      archived: { label: 'Archived', variant: 'outline' as const },
      pending_review: { label: 'Pending Review', variant: 'destructive' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const formatPrice = (price: number, sourceCurrency?: string) => {
    const propertySourceCurrency = sourceCurrency || 'ETB';
    const convertedPrice = convertCurrency(price, propertySourceCurrency);
    return formatCurrency(convertedPrice);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
        <Navbar />
        <div className="container mx-auto py-12 px-4 text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-4">Please log in to manage your properties.</p>
          <Button asChild>
            <Link to="/login">Login</Link>
          </Button>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      
      <div className="container mx-auto py-12 px-4 md:px-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-nibret-blue mb-2">
              {translate("My Properties", "የእኔ ንብረቶች")}
            </h1>
            <p className="text-gray-600">
              {translate("Manage your property listings", "የንብረት ዝርዝሮችዎን ያስተዳድሩ")}
            </p>
          </div>
          <Button asChild className="bg-nibret-blue hover:bg-nibret-blue/90">
            <Link to="/upload-property">
              <Plus className="mr-2 h-4 w-4" />
              {translate("Add Property", "ንብረት ይጨምሩ")}
            </Link>
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All Properties</TabsTrigger>
            <TabsTrigger value="published">Published</TabsTrigger>
            <TabsTrigger value="draft">Drafts</TabsTrigger>
            <TabsTrigger value="archived">Archived</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-6">
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-nibret-blue mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading properties...</p>
              </div>
            ) : properties.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Home className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {translate("No properties found", "ምንም ንብረቶች አልተገኙም")}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {translate("Start by adding your first property listing.", "የመጀመሪያ ንብረት ዝርዝርዎን በመጨመር ይጀምሩ።")}
                  </p>
                  <Button asChild>
                    <Link to="/upload-property">
                      <Plus className="mr-2 h-4 w-4" />
                      {translate("Add Property", "ንብረት ይጨምሩ")}
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {properties.map((property) => (
                  <AdminPropertyCard
                    key={property.id}
                    property={property}
                    showSlideshow={true}
                    onDelete={handleDelete}
                    onStatusChange={handleStatusChange}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
      
      <Footer />
    </div>
  );
};

export default PropertyManagement;
