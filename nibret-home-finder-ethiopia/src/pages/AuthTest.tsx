import React, { useContext, useState } from 'react';
import { AuthContext } from '../App';
import { authApi } from '../lib/api/auth';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';

const AuthTest = () => {
  const { isAuthenticated, currentUser, token, login, logout } = useContext(AuthContext);
  const [username, setUsername] = useState('0965789832');
  const [password, setPassword] = useState('nibretadmin');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleLogin = async () => {
    setLoading(true);
    setMessage('');
    try {
      console.log('🔐 Testing login...');
      const response = await authApi.login({ username, password });
      console.log('✅ Login response:', response);
      
      if (response.success && response.access_token) {
        login(response.user, response.access_token);
        setMessage('✅ Login successful!');
      } else {
        setMessage('❌ Login failed: ' + (response.error || 'Unknown error'));
      }
    } catch (error: any) {
      console.error('💥 Login error:', error);
      setMessage('❌ Login error: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
    setMessage('✅ Logged out successfully');
  };

  const checkStorage = () => {
    const storedToken = localStorage.getItem('auth_token');
    const storedUser = localStorage.getItem('user');
    
    console.log('💾 Storage check:', {
      hasToken: !!storedToken,
      hasUser: !!storedUser,
      tokenPreview: storedToken ? storedToken.substring(0, 30) + '...' : 'none',
      userPreview: storedUser ? JSON.parse(storedUser) : 'none'
    });
    
    setMessage(`Storage: Token=${!!storedToken}, User=${!!storedUser}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}
              </div>
              <div>
                <strong>Current User:</strong> {currentUser ? `${currentUser.first_name} (${currentUser.role})` : '❌ None'}
              </div>
              <div>
                <strong>Token:</strong> {token ? token.substring(0, 20) + '...' : '❌ None'}
              </div>
              <div>
                <strong>localStorage Token:</strong> {localStorage.getItem('auth_token') ? '✅ Yes' : '❌ No'}
              </div>
            </div>
            
            {message && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded text-sm">
                {message}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Login Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Input
                placeholder="Username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            
            <div className="flex gap-2">
              <Button onClick={handleLogin} disabled={loading}>
                {loading ? 'Logging in...' : 'Login'}
              </Button>
              <Button onClick={handleLogout} variant="outline">
                Logout
              </Button>
              <Button onClick={checkStorage} variant="outline">
                Check Storage
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AuthTest;
