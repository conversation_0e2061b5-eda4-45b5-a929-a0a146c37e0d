
import React, { useState, useEffect } from 'react';
import { useAppContext } from '../context/AppContext';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Heart, MapPin, Calendar, Settings, User, Home, Gavel, Loader2 } from 'lucide-react';
import PropertyCard from '@/components/PropertyCard';
import { propertyApi, Property } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

const CustomerDashboard = () => {
  const { translate } = useAppContext();
  const { toast } = useToast();
  const [userProperties, setUserProperties] = useState<Property[]>([]);
  const [savedProperties, setSavedProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user's properties from API
  useEffect(() => {
    const fetchUserProperties = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch user's own properties
        const myPropertiesResponse = await propertyApi.getMyProperties();
        setUserProperties(myPropertiesResponse.data || []);

        // For saved properties, we'll fetch all properties and filter later
        // In a real app, you'd have a separate endpoint for saved/favorited properties
        const allProperties = await propertyApi.getProperties();
        setSavedProperties(allProperties?.slice(0, 3) || []); // Show first 3 as "saved" for demo

      } catch (err) {
        console.error('Error fetching user properties:', err);
        setError('Failed to load properties. Please try again later.');
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load your properties. Please try again later.",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUserProperties();
  }, [toast]);

  const auctionBids = [
    {
      id: 'auction-1',
      propertyTitle: 'Family Home with Pool',
      currentBid: 680000,
      yourBid: 650000,
      status: 'active',
      endsIn: '2 days'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />

      <div className="container mx-auto py-12 px-4 md:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-nibret-blue mb-2">
            {translate("My Dashboard", "የእኔ ዳሽቦርድ")}
          </h1>
          <p className="text-gray-600">
            {translate("Manage your properties, saved listings, and account settings", "ንብረቶችዎን፣ የተቀመጡ ዝርዝሮችን እና የመለያ ቅንብሮችን ያስተዳድሩ")}
          </p>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-5">
            <TabsTrigger value="overview">{translate("Overview", "አጠቃላይ እይታ")}</TabsTrigger>
            <TabsTrigger value="properties">{translate("My Properties", "የእኔ ንብረቶች")}</TabsTrigger>
            <TabsTrigger value="wishlist">{translate("Wishlist", "ምኞት ዝርዝር")}</TabsTrigger>
            <TabsTrigger value="auctions">{translate("Auctions", "መጨረሻዎች")}</TabsTrigger>
            <TabsTrigger value="profile">{translate("Profile", "መገለጫ")}</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">{translate("Active Listings", "ንቁ ዝርዝሮች")}</p>
                      <p className="text-2xl font-bold text-nibret-blue">1</p>
                    </div>
                    <Home className="h-8 w-8 text-nibret-blue" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">{translate("Saved Properties", "የተቀመጡ ንብረቶች")}</p>
                      <p className="text-2xl font-bold text-nibret-blue">1</p>
                    </div>
                    <Heart className="h-8 w-8 text-nibret-blue" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">{translate("Active Bids", "ንቁ ጨረታዎች")}</p>
                      <p className="text-2xl font-bold text-nibret-blue">1</p>
                    </div>
                    <Gavel className="h-8 w-8 text-nibret-blue" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>{translate("Recent Activity", "የቅርብ ጊዜ እንቅስቃሴ")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between border-b pb-2">
                    <div>
                      <p className="font-medium">{translate("Property listed successfully", "ንብረት በተሳካ ሁኔታ ተዘርዝሯል")}</p>
                      <p className="text-sm text-gray-500">2 hours ago</p>
                    </div>
                    <Badge variant="outline" className="text-green-600">Active</Badge>
                  </div>
                  <div className="flex items-center justify-between border-b pb-2">
                    <div>
                      <p className="font-medium">{translate("Bid placed on auction property", "በመጨረሻ ንብረት ላይ ጨረታ ተደርጓል")}</p>
                      <p className="text-sm text-gray-500">1 day ago</p>
                    </div>
                    <Badge variant="outline" className="text-blue-600">Bid</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="properties" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-nibret-blue">
                {translate("My Properties", "የእኔ ንብረቶች")}
              </h2>
              <Button className="bg-nibret-blue hover:bg-nibret-blue/90">
                {translate("Add New Property", "አዲስ ንብረት አክል")}
              </Button>
            </div>

            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
                <span className="ml-2 text-gray-600">{translate("Loading properties...", "ንብረቶችን በመጫን ላይ...")}</span>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <p className="text-red-600 text-lg mb-4">{error}</p>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                >
                  {translate("Try Again", "እንደገና ሞክር")}
                </Button>
              </div>
            ) : userProperties.length === 0 ? (
              <div className="text-center py-12">
                <Home className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {translate("No properties found", "ምንም ንብረቶች አልተገኙም")}
                </h3>
                <p className="text-gray-600 mb-4">
                  {translate("Start by adding your first property listing.", "የመጀመሪያ ንብረት ዝርዝርዎን በመጨመር ይጀምሩ።")}
                </p>
                <Button className="bg-nibret-blue hover:bg-nibret-blue/90">
                  {translate("Add New Property", "አዲስ ንብረት አክል")}
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {userProperties.map((property) => (
                  <PropertyCard key={property.id} property={property} showSlideshow={true} />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="wishlist" className="space-y-6">
            <h2 className="text-2xl font-bold text-nibret-blue">
              {translate("My Wishlist", "የእኔ ምኞት ዝርዝር")}
            </h2>

            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
                <span className="ml-2 text-gray-600">{translate("Loading saved properties...", "የተቀመጡ ንብረቶችን በመጫን ላይ...")}</span>
              </div>
            ) : savedProperties.length === 0 ? (
              <div className="text-center py-12">
                <Heart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {translate("No saved properties", "የተቀመጡ ንብረቶች የሉም")}
                </h3>
                <p className="text-gray-600 mb-4">
                  {translate("Properties you save will appear here.", "የሚያስቀምጧቸው ንብረቶች እዚህ ይታያሉ።")}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {savedProperties.map((property) => (
                  <div key={property.id} className="relative">
                    <PropertyCard property={property} showSlideshow={true} />
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute top-2 right-2 bg-white"
                    >
                      <Heart className="h-4 w-4 fill-red-500 text-red-500" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="auctions" className="space-y-6">
            <h2 className="text-2xl font-bold text-nibret-blue">
              {translate("My Auction Bids", "የእኔ የመጨረሻ ጨረታዎች")}
            </h2>

            <div className="space-y-4">
              {auctionBids.map((bid) => (
                <Card key={bid.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold text-lg">{bid.propertyTitle}</h3>
                        <p className="text-gray-600">
                          {translate("Your bid", "የእርስዎ ጨረታ")}: <span className="font-medium">${bid.yourBid.toLocaleString()}</span>
                        </p>
                        <p className="text-gray-600">
                          {translate("Current highest", "አሁን ያለው ከፍተኛ")}: <span className="font-medium">${bid.currentBid.toLocaleString()}</span>
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant={bid.status === 'active' ? 'default' : 'secondary'}>
                          {bid.status === 'active' ? translate("Active", "ንቁ") : translate("Ended", "ተጠናቋል")}
                        </Badge>
                        <p className="text-sm text-gray-500 mt-1">
                          {translate("Ends in", "የሚያበቃው በ")} {bid.endsIn}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  {translate("Profile Settings", "የመገለጫ ቅንብሮች")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      {translate("Full Name", "ሙሉ ስም")}
                    </label>
                    <input
                      type="text"
                      className="w-full p-2 border border-gray-300 rounded-md"
                      placeholder={translate("Enter your full name", "ሙሉ ስምዎን ያስገቡ")}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      {translate("Email", "ኢሜይል")}
                    </label>
                    <input
                      type="email"
                      className="w-full p-2 border border-gray-300 rounded-md"
                      placeholder={translate("Enter your email", "ኢሜይልዎን ያስገቡ")}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      {translate("Phone", "ስልክ")}
                    </label>
                    <input
                      type="tel"
                      className="w-full p-2 border border-gray-300 rounded-md"
                      placeholder={translate("Enter your phone number", "የስልክ ቁጥርዎን ያስገቡ")}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      {translate("Location", "አካባቢ")}
                    </label>
                    <input
                      type="text"
                      className="w-full p-2 border border-gray-300 rounded-md"
                      placeholder={translate("Enter your location", "አካባቢዎን ያስገቡ")}
                    />
                  </div>
                </div>

                <Button className="bg-nibret-blue hover:bg-nibret-blue/90">
                  {translate("Update Profile", "መገለጫ አዘምን")}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <Footer />
    </div>
  );
};

export default CustomerDashboard;
