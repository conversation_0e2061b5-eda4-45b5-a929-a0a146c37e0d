import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import AdminLayout from './AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import {
  ArrowLeft,
  Phone,
  Mail,
  Calendar,
  Clock,
  User,
  MapPin,
  DollarSign,
  MessageSquare,
  Video,
  FileText,
  Plus,
  Edit,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { leadApi, Lead, Interaction } from '@/lib/api';

// Form schemas
const interactionSchema = z.object({
  type: z.enum(['call', 'email', 'meeting', 'property_viewing', 'follow_up', 'note']),
  description: z.string().min(1, 'Description is required'),
  outcome: z.enum(['positive', 'neutral', 'negative', 'no_response']).optional(),
  next_action: z.string().optional(),
  next_action_date: z.string().optional(),
});

const statusUpdateSchema = z.object({
  status: z.enum(['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost']),
  notes: z.string().optional(),
});

type InteractionFormData = z.infer<typeof interactionSchema>;
type StatusUpdateFormData = z.infer<typeof statusUpdateSchema>;

const LeadDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [lead, setLead] = useState<Lead | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAddingInteraction, setIsAddingInteraction] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  const interactionForm = useForm<InteractionFormData>({
    resolver: zodResolver(interactionSchema),
    defaultValues: {
      type: 'call',
      description: '',
      outcome: 'neutral',
    },
  });

  const statusForm = useForm<StatusUpdateFormData>({
    resolver: zodResolver(statusUpdateSchema),
    defaultValues: {
      status: 'new',
      notes: '',
    },
  });

  useEffect(() => {
    if (id) {
      loadLead();
    }
  }, [id]);

  const loadLead = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const leadData = await leadApi.getLead(id);
      setLead(leadData);
      statusForm.setValue('status', leadData.status);
    } catch (error: any) {
      console.error('Error loading lead:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load lead details. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const onAddInteraction = async (data: InteractionFormData) => {
    if (!id) return;

    setIsAddingInteraction(true);
    try {
      const updatedLead = await leadApi.addInteraction(id, data);
      setLead(updatedLead);
      interactionForm.reset();
      toast({
        title: 'Success',
        description: 'Interaction added successfully.',
      });
    } catch (error: any) {
      console.error('Error adding interaction:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to add interaction. Please try again.',
      });
    } finally {
      setIsAddingInteraction(false);
    }
  };

  const onUpdateStatus = async (data: StatusUpdateFormData) => {
    if (!id) return;

    setIsUpdatingStatus(true);
    try {
      const updatedLead = await leadApi.updateLeadStatus(id, data.status, data.notes);
      setLead(updatedLead);
      toast({
        title: 'Success',
        description: 'Lead status updated successfully.',
      });
    } catch (error: any) {
      console.error('Error updating status:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update status. Please try again.',
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'contacted': return 'bg-yellow-100 text-yellow-800';
      case 'qualified': return 'bg-green-100 text-green-800';
      case 'proposal': return 'bg-purple-100 text-purple-800';
      case 'negotiation': return 'bg-orange-100 text-orange-800';
      case 'closed_won': return 'bg-green-100 text-green-800';
      case 'closed_lost': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getInteractionIcon = (type: string) => {
    switch (type) {
      case 'call': return <Phone className="h-4 w-4" />;
      case 'email': return <Mail className="h-4 w-4" />;
      case 'meeting': return <Video className="h-4 w-4" />;
      case 'property_viewing': return <MapPin className="h-4 w-4" />;
      case 'follow_up': return <Calendar className="h-4 w-4" />;
      case 'note': return <FileText className="h-4 w-4" />;
      default: return <MessageSquare className="h-4 w-4" />;
    }
  };

  const getOutcomeIcon = (outcome?: string) => {
    switch (outcome) {
      case 'positive': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'negative': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'no_response': return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      default: return <MessageSquare className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="container mx-auto py-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-nibret-blue mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading lead details...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!lead) {
    return (
      <AdminLayout>
        <div className="container mx-auto py-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Lead Not Found</h1>
            <Button onClick={() => navigate('/admin/crm')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to CRM
            </Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => navigate('/admin/crm')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to CRM
            </Button>
            <div>
              <h1 className="text-3xl font-bold">
                {lead.first_name} {lead.last_name}
              </h1>
              <p className="text-gray-600">Lead Details & Interaction Timeline</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className={getStatusColor(lead.status)}>
              {lead.status.replace('_', ' ').toUpperCase()}
            </Badge>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Edit className="mr-2 h-4 w-4" />
                  Update Status
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Update Lead Status</DialogTitle>
                  <DialogDescription>
                    Change the status of this lead and add notes.
                  </DialogDescription>
                </DialogHeader>
                <Form {...statusForm}>
                  <form onSubmit={statusForm.handleSubmit(onUpdateStatus)} className="space-y-4">
                    <FormField
                      control={statusForm.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="new">New</SelectItem>
                              <SelectItem value="contacted">Contacted</SelectItem>
                              <SelectItem value="qualified">Qualified</SelectItem>
                              <SelectItem value="proposal">Proposal</SelectItem>
                              <SelectItem value="negotiation">Negotiation</SelectItem>
                              <SelectItem value="closed_won">Closed Won</SelectItem>
                              <SelectItem value="closed_lost">Closed Lost</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={statusForm.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea placeholder="Add notes about this status change..." {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <DialogFooter>
                      <Button type="submit" disabled={isUpdatingStatus}>
                        {isUpdatingStatus ? 'Updating...' : 'Update Status'}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Lead Information */}
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{lead.email}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span>{lead.phone}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span>Created: {formatDate(lead.created_at)}</span>
                </div>
                {lead.last_contact_date && (
                  <div className="flex items-center space-x-3">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span>Last Contact: {formatDate(lead.last_contact_date)}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {lead.interested_property && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="mr-2 h-5 w-5" />
                    Interested Property
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <h4 className="font-medium">{lead.interested_property.title}</h4>
                  <p className="text-sm text-gray-600">{lead.interested_property.address}</p>
                  <p className="text-lg font-bold text-nibret-blue mt-2">
                    ${lead.interested_property.price.toLocaleString()}
                  </p>
                </CardContent>
              </Card>
            )}

            {lead.property_preferences && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="mr-2 h-5 w-5" />
                    Preferences
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {lead.property_preferences.budget_min && lead.property_preferences.budget_max && (
                    <div className="flex items-center space-x-3">
                      <DollarSign className="h-4 w-4 text-gray-500" />
                      <span>
                        ${lead.property_preferences.budget_min.toLocaleString()} - 
                        ${lead.property_preferences.budget_max.toLocaleString()}
                      </span>
                    </div>
                  )}
                  {lead.property_preferences.bedrooms && (
                    <div>
                      <span className="text-sm text-gray-600">Bedrooms: </span>
                      <span>{lead.property_preferences.bedrooms}</span>
                    </div>
                  )}
                  {lead.property_preferences.bathrooms && (
                    <div>
                      <span className="text-sm text-gray-600">Bathrooms: </span>
                      <span>{lead.property_preferences.bathrooms}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Interaction Timeline */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <MessageSquare className="mr-2 h-5 w-5" />
                    Interaction Timeline
                  </CardTitle>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Interaction
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add New Interaction</DialogTitle>
                        <DialogDescription>
                          Record a new interaction with this lead.
                        </DialogDescription>
                      </DialogHeader>
                      <Form {...interactionForm}>
                        <form onSubmit={interactionForm.handleSubmit(onAddInteraction)} className="space-y-4">
                          <FormField
                            control={interactionForm.control}
                            name="type"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Interaction Type</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select type" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="call">Phone Call</SelectItem>
                                    <SelectItem value="email">Email</SelectItem>
                                    <SelectItem value="meeting">Meeting</SelectItem>
                                    <SelectItem value="property_viewing">Property Viewing</SelectItem>
                                    <SelectItem value="follow_up">Follow-up</SelectItem>
                                    <SelectItem value="note">Note</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={interactionForm.control}
                            name="description"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Description</FormLabel>
                                <FormControl>
                                  <Textarea placeholder="Describe the interaction..." {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={interactionForm.control}
                            name="outcome"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Outcome</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select outcome" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="positive">Positive</SelectItem>
                                    <SelectItem value="neutral">Neutral</SelectItem>
                                    <SelectItem value="negative">Negative</SelectItem>
                                    <SelectItem value="no_response">No Response</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={interactionForm.control}
                            name="next_action"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Next Action (Optional)</FormLabel>
                                <FormControl>
                                  <Input placeholder="What's the next step?" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={interactionForm.control}
                            name="next_action_date"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Next Action Date (Optional)</FormLabel>
                                <FormControl>
                                  <Input type="datetime-local" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <DialogFooter>
                            <Button type="submit" disabled={isAddingInteraction}>
                              {isAddingInteraction ? 'Adding...' : 'Add Interaction'}
                            </Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {lead.interactions.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <MessageSquare className="mx-auto h-12 w-12 mb-4" />
                      <p>No interactions recorded yet.</p>
                      <p className="text-sm">Add the first interaction to start tracking this lead's timeline.</p>
                    </div>
                  ) : (
                    lead.interactions
                      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                      .map((interaction, index) => (
                        <div key={interaction._id} className="flex space-x-4 p-4 border rounded-lg">
                          <div className="flex-shrink-0">
                            <div className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full">
                              {getInteractionIcon(interaction.type)}
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <h4 className="font-medium capitalize">
                                  {interaction.type.replace('_', ' ')}
                                </h4>
                                {interaction.outcome && getOutcomeIcon(interaction.outcome)}
                              </div>
                              <span className="text-sm text-gray-500">
                                {formatDate(interaction.date)}
                              </span>
                            </div>
                            <p className="text-gray-700 mt-1">{interaction.description}</p>
                            {interaction.next_action && (
                              <div className="mt-2 p-2 bg-blue-50 rounded">
                                <p className="text-sm text-blue-800">
                                  <strong>Next Action:</strong> {interaction.next_action}
                                  {interaction.next_action_date && (
                                    <span className="ml-2">
                                      (Due: {formatDate(interaction.next_action_date)})
                                    </span>
                                  )}
                                </p>
                              </div>
                            )}
                            <div className="mt-2 text-xs text-gray-500">
                              By: {interaction.created_by.first_name} {interaction.created_by.last_name}
                            </div>
                          </div>
                        </div>
                      ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default LeadDetail;
