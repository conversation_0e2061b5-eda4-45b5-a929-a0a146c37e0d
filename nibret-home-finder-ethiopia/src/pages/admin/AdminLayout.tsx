
import React, { useContext } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { 
  LayoutDashboard, 
  Home, 
  Users, 
  LogOut, 
  Settings 
} from "lucide-react";
import { AuthContext } from "../../App";
import { cn } from "@/lib/utils";

const AdminLayout = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();
  const { logout } = useContext(AuthContext);
  const location = useLocation();
  
  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <div className="min-h-screen flex">
      {/* Sidebar */}
      <div className="w-64 bg-nibret-blue text-white flex flex-col">
        <div className="p-4 border-b border-white/10">
          <h1 className="text-xl font-bold">Nibret Admin</h1>
        </div>
        
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            <li>
              <Link 
                to="/admin" 
                className={cn(
                  "flex items-center p-2 rounded-md hover:bg-white/10 transition-colors",
                  isActive("/admin") && "bg-white/10 font-medium"
                )}
              >
                <LayoutDashboard className="mr-3 h-5 w-5" />
                Dashboard
              </Link>
            </li>
            <li>
              <Link 
                to="/admin/crm" 
                className={cn(
                  "flex items-center p-2 rounded-md hover:bg-white/10 transition-colors",
                  isActive("/admin/crm") && "bg-white/10 font-medium"
                )}
              >
                <Users className="mr-3 h-5 w-5" />
                CRM
              </Link>
            </li>
            <li>
              <Link 
                to="/admin/settings" 
                className={cn(
                  "flex items-center p-2 rounded-md hover:bg-white/10 transition-colors",
                  isActive("/admin/settings") && "bg-white/10 font-medium"
                )}
              >
                <Settings className="mr-3 h-5 w-5" />
                Settings
              </Link>
            </li>
            <li>
              <Link 
                to="/" 
                className="flex items-center p-2 rounded-md hover:bg-white/10 transition-colors"
              >
                <Home className="mr-3 h-5 w-5" />
                View Website
              </Link>
            </li>
          </ul>
        </nav>
        
        <div className="p-4 border-t border-white/10">
          <Button 
            variant="outline" 
            className="w-full text-white border-white hover:bg-white/10 hover:text-white"
            onClick={handleLogout}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Logout
          </Button>
        </div>
      </div>
      
      {/* Main content */}
      <div className="flex-1 overflow-auto bg-gray-50">
        {children}
      </div>
    </div>
  );
};

export default AdminLayout;
