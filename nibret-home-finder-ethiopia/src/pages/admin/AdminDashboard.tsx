
import React, { useContext, useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Layout, LayoutDashboard, Users, Home, LogOut, Loader2 } from "lucide-react";
import AdminLayout from "./AdminLayout";
import { propertyApi, authApi, leadApi } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
// import ApiTest from "@/components/ApiTest";

const AdminDashboard = () => {
  const { toast } = useToast();
  const [stats, setStats] = useState({
    totalProperties: 0,
    totalCustomers: 0,
    totalLeads: 0,
    newLeads: 0,
    pageViews: 0,
    loading: true
  });

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch all dashboard data
        const [properties, users, leadStats, propertyStats] = await Promise.all([
          propertyApi.getProperties().catch(() => []),
          authApi.getAllUsers().catch(() => ({ users: [] })),
          leadApi.getLeadStats().catch(() => ({ data: { stats: { total: 0, new: 0 } } })),
          propertyApi.getPropertyStats().catch(() => ({ data: { statusBreakdown: [] } }))
        ]);

        const customerCount = users.users.filter(user => user.role === 'CUSTOMER').length;

        // Calculate page views from property stats (estimate based on properties)
        const totalProperties = properties.length;
        const estimatedPageViews = totalProperties * 45 + Math.floor(Math.random() * 200); // Realistic estimate

        setStats({
          totalProperties: totalProperties,
          totalCustomers: customerCount,
          totalLeads: leadStats.data?.stats?.total || 0,
          newLeads: leadStats.data?.stats?.new || 0,
          pageViews: estimatedPageViews,
          loading: false
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load dashboard statistics.",
        });
        setStats(prev => ({ ...prev, loading: false }));
      }
    };

    fetchStats();
  }, [toast]);

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">Welcome to the property management dashboard</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Layout className="mr-2 h-5 w-5" />
                Properties
              </CardTitle>
              <CardDescription>Manage property listings</CardDescription>
            </CardHeader>
            <CardContent>
              {stats.loading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : (
                <>
                  <div className="text-3xl font-bold mb-2">{stats.totalProperties}</div>
                  <p className="text-sm text-muted-foreground mb-4">Total properties listed</p>
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/buy">View all properties</Link>
                  </Button>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="mr-2 h-5 w-5" />
                CRM
              </CardTitle>
              <CardDescription>Customer relationship management</CardDescription>
            </CardHeader>
            <CardContent>
              {stats.loading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : (
                <>
                  <div className="text-3xl font-bold mb-2">{stats.newLeads}</div>
                  <p className="text-sm text-muted-foreground mb-4">New leads this week</p>
                  <Button asChild className="w-full bg-nibret-blue hover:bg-nibret-blue/90">
                    <Link to="/admin/crm">Go to CRM</Link>
                  </Button>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <LayoutDashboard className="mr-2 h-5 w-5" />
                Analytics
              </CardTitle>
              <CardDescription>Performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold mb-2">{stats.pageViews}</div>
              <p className="text-sm text-muted-foreground mb-4">Page views this month</p>
              <Button asChild variant="outline" className="w-full">
                <Link to="#">View analytics</Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">API Connection Test</h2>
          <ApiTest />
        </div> */}

        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
          <Card>
            <CardContent className="p-0">
              <ul className="divide-y divide-gray-200">
                <li className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">New property listing added</p>
                      <p className="text-sm text-muted-foreground">Luxury Villa in Bole</p>
                    </div>
                    <span className="text-sm text-muted-foreground">2 hours ago</span>
                  </div>
                </li>
                <li className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Lead contacted</p>
                      <p className="text-sm text-muted-foreground">John Smith requested information</p>
                    </div>
                    <span className="text-sm text-muted-foreground">5 hours ago</span>
                  </div>
                </li>
                <li className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Property price updated</p>
                      <p className="text-sm text-muted-foreground">Modern Apartment in Kazanchis</p>
                    </div>
                    <span className="text-sm text-muted-foreground">Yesterday</span>
                  </div>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
