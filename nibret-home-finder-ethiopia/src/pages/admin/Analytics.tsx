import React, { useState, useEffect, useContext } from 'react';
import { useAppContext } from '../../context/AppContext';
import { AuthContext } from '../../App';
import AdminLayout from './AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  Home, 
  TrendingUp, 
  Activity, 
  Eye, 
  Search,
  Phone,
  Mail,
  Calendar,
  BarChart3,
  UserCheck,
  Clock,
  MousePointer,
  Globe
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/utils';

interface DashboardData {
  overview: {
    total_users: number;
    total_properties: number;
    total_leads: number;
    user_growth: number;
  };
  recent_activities: any[];
  popular_properties: any[];
  search_analytics: any[];
  daily_stats: any[];
  user_engagement: any[];
  lead_stats: any[];
  conversion_funnel: any[];
}

const Analytics = () => {
  const { translate } = useAppContext();
  const { isAuthenticated } = useContext(AuthContext);
  const { toast } = useToast();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('30');

  useEffect(() => {
    if (isAuthenticated) {
      loadDashboardData();
    }
  }, [isAuthenticated, timeRange]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const response = await apiRequest(`/analytics/dashboard?days=${timeRange}`);
      if (response.success) {
        setDashboardData(response.data);
      }
    } catch (error: any) {
      console.error('Error loading dashboard data:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load analytics data. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'login': return <UserCheck className="h-4 w-4" />;
      case 'property_view': return <Eye className="h-4 w-4" />;
      case 'property_click': return <MousePointer className="h-4 w-4" />;
      case 'search': return <Search className="h-4 w-4" />;
      case 'phone_call': return <Phone className="h-4 w-4" />;
      case 'email_sent': return <Mail className="h-4 w-4" />;
      case 'page_view': return <Globe className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'login': return 'text-green-600';
      case 'property_view': return 'text-blue-600';
      case 'property_click': return 'text-purple-600';
      case 'search': return 'text-orange-600';
      case 'phone_call': return 'text-red-600';
      case 'email_sent': return 'text-pink-600';
      case 'page_view': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="container mx-auto py-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-nibret-blue mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading analytics...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const stats = [
    {
      title: 'Total Users',
      value: formatNumber(dashboardData?.overview.total_users || 0),
      icon: Users,
      change: `${dashboardData?.overview.user_growth || 0}%`,
      changeType: (dashboardData?.overview.user_growth || 0) >= 0 ? 'positive' : 'negative'
    },
    {
      title: 'Properties Listed',
      value: formatNumber(dashboardData?.overview.total_properties || 0),
      icon: Home,
      change: '+8%',
      changeType: 'positive'
    },
    {
      title: 'Total Leads',
      value: formatNumber(dashboardData?.overview.total_leads || 0),
      icon: TrendingUp,
      change: '+15%',
      changeType: 'positive'
    },
    {
      title: 'Active Sessions',
      value: formatNumber(dashboardData?.user_engagement?.length || 0),
      icon: Activity,
      change: '+3%',
      changeType: 'positive'
    }
  ];

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-nibret-blue">Analytics Dashboard</h1>
            <p className="text-gray-600">Comprehensive user activity and CRM analytics</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant={timeRange === '7' ? 'default' : 'outline'}
              onClick={() => setTimeRange('7')}
              size="sm"
            >
              7 Days
            </Button>
            <Button
              variant={timeRange === '30' ? 'default' : 'outline'}
              onClick={() => setTimeRange('30')}
              size="sm"
            >
              30 Days
            </Button>
            <Button
              variant={timeRange === '90' ? 'default' : 'outline'}
              onClick={() => setTimeRange('90')}
              size="sm"
            >
              90 Days
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-gray-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-nibret-blue">{stat.value}</div>
                <p className={`text-xs ${stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                  {stat.change} from last period
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Detailed Analytics Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="activities">User Activities</TabsTrigger>
            <TabsTrigger value="properties">Property Analytics</TabsTrigger>
            <TabsTrigger value="users">User Engagement</TabsTrigger>
            <TabsTrigger value="crm">CRM Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Activities */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="mr-2 h-5 w-5" />
                    Real-time Activity Feed
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {dashboardData?.recent_activities?.slice(0, 15).map((activity, index) => (
                      <div key={index} className="flex items-center space-x-4 p-2 hover:bg-gray-50 rounded">
                        <div className={`${getActivityColor(activity.type)}`}>
                          {getActivityIcon(activity.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {activity.user?.first_name || 'Anonymous'} {activity.user?.last_name || 'User'} - {activity.action}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(activity.timestamp).toLocaleString()}
                          </p>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {activity.type.replace('_', ' ')}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Popular Properties */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="mr-2 h-5 w-5" />
                    Most Viewed Properties
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData?.popular_properties?.slice(0, 8).map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <p className="text-sm font-medium">Property #{item._id.slice(-6)}</p>
                          <p className="text-xs text-gray-500">{item.views} total views</p>
                        </div>
                        <div className="text-right">
                          <Badge variant="secondary">{item.unique_users} unique users</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="activities" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Detailed User Activity Timeline</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {dashboardData?.recent_activities?.map((activity, index) => (
                    <div key={index} className="flex items-start space-x-4 p-4 border rounded-lg hover:bg-gray-50">
                      <div className={`${getActivityColor(activity.type)} mt-1`}>
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">
                            {activity.user?.first_name || 'Anonymous'} {activity.user?.last_name || 'User'}
                          </p>
                          <Badge variant="outline">{activity.type.replace('_', ' ')}</Badge>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{activity.action}</p>
                        {activity.description && (
                          <p className="text-xs text-gray-500 mt-1">{activity.description}</p>
                        )}
                        {activity.metadata?.page_url && (
                          <p className="text-xs text-blue-600 mt-1">
                            Page: {activity.metadata.page_url}
                          </p>
                        )}
                        <p className="text-xs text-gray-400 mt-2 flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="properties" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Property Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData?.popular_properties?.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <p className="font-medium">Property #{item._id.slice(-8)}</p>
                          <p className="text-sm text-gray-600">{item.views} total views</p>
                          <p className="text-xs text-gray-500">{item.unique_users} unique visitors</p>
                        </div>
                        <div className="text-right">
                          <Badge variant="secondary">
                            {Math.round((item.views / item.unique_users) * 100) / 100} avg views/user
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Search Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData?.search_analytics?.slice(0, 10).map((search, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border-b">
                        <div className="flex-1">
                          <p className="text-sm font-medium">"{search._id}"</p>
                          <p className="text-xs text-gray-500">{search.unique_users} users searched</p>
                        </div>
                        <Badge variant="outline">{search.count} times</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Most Engaged Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData?.user_engagement?.slice(0, 15).map((user, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">
                          {user.user_info?.first_name || 'Anonymous'} {user.user_info?.last_name || 'User'}
                        </p>
                        <p className="text-sm text-gray-600">{user.user_info?.email || 'No email'}</p>
                        <p className="text-xs text-gray-500">
                          {user.total_activities} activities • {user.session_count} sessions
                        </p>
                        <p className="text-xs text-gray-400">
                          Last active: {formatDate(user.last_activity)}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary">{user.activity_diversity} activity types</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="crm" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Lead Conversion Funnel</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData?.conversion_funnel?.map((stage, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <p className="font-medium capitalize">{stage._id.replace('_', ' ')}</p>
                          <p className="text-sm text-gray-600">{stage.count} leads</p>
                        </div>
                        <Badge variant="outline">
                          {((stage.count / (dashboardData?.overview.total_leads || 1)) * 100).toFixed(1)}%
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Lead Activity Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900">Phone Calls</h4>
                      <p className="text-2xl font-bold text-blue-700">
                        {dashboardData?.recent_activities?.filter(a => a.type === 'phone_call').length || 0}
                      </p>
                      <p className="text-sm text-blue-600">This period</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-medium text-green-900">Emails Sent</h4>
                      <p className="text-2xl font-bold text-green-700">
                        {dashboardData?.recent_activities?.filter(a => a.type === 'email_sent').length || 0}
                      </p>
                      <p className="text-sm text-green-600">This period</p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <h4 className="font-medium text-purple-900">Property Inquiries</h4>
                      <p className="text-2xl font-bold text-purple-700">
                        {dashboardData?.recent_activities?.filter(a => a.type === 'property_inquiry').length || 0}
                      </p>
                      <p className="text-sm text-purple-600">This period</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default Analytics;
