
import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import AdminLayout from "./AdminLayout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Mail,
  Phone,
  Check,
  Users,
  Loader2,
  Plus,
  Eye,
  Calendar,
  Clock,
  TrendingUp,
  AlertCircle,
  MessageSquare,
  UserCheck,
  PhoneCall,
  Video,
  FileText
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { authA<PERSON>, User, leadApi, Lead, LeadStats } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";

interface LeadFilterOptions {
  status?: string;
  searchTerm?: string;
  sortBy?: string;
  dateFrom?: string;
  dateTo?: string;
}

const CRM = () => {
  const { toast } = useToast();
  const [leads, setLeads] = useState<Lead[]>([]);
  const [customers, setCustomers] = useState<User[]>([]);
  const [leadStats, setLeadStats] = useState<LeadStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<LeadFilterOptions>({});
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortOption, setSortOption] = useState("newest");
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [pagination, setPagination] = useState({ page: 1, limit: 20, total: 0, pages: 0 });

  // Fetch data
  useEffect(() => {
    loadData();
  }, [activeTab, searchTerm, sortOption, dateFrom, dateTo, pagination.page]);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load leads
      const leadFilters = {
        status: activeTab !== 'all' && activeTab !== 'customers' ? activeTab : undefined,
        search: searchTerm || undefined,
        sort_by: sortOption === 'newest' ? 'created_at' : sortOption === 'oldest' ? 'created_at' : 'first_name',
        sort_order: sortOption === 'newest' ? 'desc' : 'asc',
        page: pagination.page,
        limit: pagination.limit
      };

      const [leadsResponse, statsResponse, customersResponse] = await Promise.all([
        leadApi.getLeads(leadFilters),
        leadApi.getLeadStats(),
        authApi.getAllUsers()
      ]);

      setLeads(leadsResponse.data);
      setPagination(leadsResponse.pagination);
      setLeadStats(statsResponse);

      // Filter only customers
      const customerUsers = customersResponse.users.filter(user => user.role === 'CUSTOMER');
      setCustomers(customerUsers);

    } catch (error: any) {
      console.error('Error loading CRM data:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load CRM data. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter leads based on current tab and search
  const getFilteredLeads = () => {
    if (activeTab === 'customers') {
      return []; // Customers are handled separately
    }
    return leads; // Leads are already filtered by the API based on activeTab
  };

  const filteredLeads = getFilteredLeads();

  const updateLeadStatus = async (id: string, status: Lead["status"]) => {
    try {
      await leadApi.updateLeadStatus(id, status);
      setLeads(
        leads.map((lead) => (lead._id === id ? { ...lead, status } : lead))
      );
      toast({
        title: "Success",
        description: "Lead status updated successfully.",
      });
    } catch (error) {
      console.error('Error updating lead status:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update lead status.",
      });
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new":
        return "bg-blue-100 text-blue-800";
      case "contacted":
        return "bg-yellow-100 text-yellow-800";
      case "qualified":
        return "bg-green-100 text-green-800";
      case "lost":
        return "bg-red-100 text-red-800";
      case "converted":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">Customer Relationship Management</h1>
            <p className="text-muted-foreground">
              Manage your leads and customer interactions
            </p>
          </div>
          <Button className="bg-nibret-blue hover:bg-nibret-blue/90">
            <Users className="mr-2 h-4 w-4" />
            Add New Lead
          </Button>
        </div>

        {/* Lead Statistics */}
        {leadStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Leads</p>
                    <p className="text-2xl font-bold">{leadStats.stats?.total || 0}</p>
                  </div>
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">New Leads</p>
                    <p className="text-2xl font-bold">{leadStats.stats?.new || 0}</p>
                  </div>
                  <AlertCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Qualified</p>
                    <p className="text-2xl font-bold">{leadStats.stats?.qualified || 0}</p>
                  </div>
                  <UserCheck className="h-8 w-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Converted</p>
                    <p className="text-2xl font-bold">{leadStats.stats?.converted || 0}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="col-span-1 md:col-span-2">
                <Label htmlFor="search">Search</Label>
                <Input
                  id="search"
                  placeholder="Search by name, email, property or phone"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="sort">Sort By</Label>
                <Select value={sortOption} onValueChange={setSortOption}>
                  <SelectTrigger id="sort" className="mt-1">
                    <SelectValue placeholder="Sort option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest first</SelectItem>
                    <SelectItem value="oldest">Oldest first</SelectItem>
                    <SelectItem value="name_asc">Name (A to Z)</SelectItem>
                    <SelectItem value="name_desc">Name (Z to A)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Date Range</Label>
                <div className="grid grid-cols-2 gap-2 mt-1">
                  <Input
                    type="date"
                    value={dateFrom}
                    onChange={(e) => setDateFrom(e.target.value)}
                    placeholder="From"
                  />
                  <Input
                    type="date"
                    value={dateTo}
                    onChange={(e) => setDateTo(e.target.value)}
                    placeholder="To"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Leads</TabsTrigger>
            <TabsTrigger value="new">New</TabsTrigger>
            <TabsTrigger value="contacted">Contacted</TabsTrigger>
            <TabsTrigger value="qualified">Qualified</TabsTrigger>
            <TabsTrigger value="lost">Lost</TabsTrigger>
            <TabsTrigger value="converted">Converted</TabsTrigger>
            <TabsTrigger value="customers">Customers</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
                <span className="ml-2 text-gray-600">Loading leads...</span>
              </div>
            ) : filteredLeads.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center text-gray-500">
                  No leads found matching your search.
                </CardContent>
              </Card>
            ) : (
              filteredLeads.map((lead) => (
                <LeadCard
                  key={lead._id}
                  lead={lead}
                  updateStatus={updateLeadStatus}
                  statusColor={getStatusColor}
                />
              ))
            )}
          </TabsContent>

          {["new", "contacted", "qualified", "lost", "converted"].map((status) => (
            <TabsContent key={status} value={status} className="space-y-4">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
                  <span className="ml-2 text-gray-600">Loading {status} leads...</span>
                </div>
              ) : filteredLeads.length === 0 ? (
                <Card>
                  <CardContent className="p-6 text-center text-gray-500">
                    No {status} leads found.
                  </CardContent>
                </Card>
              ) : (
                filteredLeads.map((lead) => (
                  <LeadCard
                    key={lead._id}
                    lead={lead}
                    updateStatus={updateLeadStatus}
                    statusColor={getStatusColor}
                  />
                ))
              )}
            </TabsContent>
          ))}

          <TabsContent value="customers" className="space-y-4">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
                <span className="ml-2 text-gray-600">Loading customers...</span>
              </div>
            ) : customers.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center text-gray-500">
                  No customers found.
                </CardContent>
              </Card>
            ) : (
              customers.map((customer) => (
                <CustomerCard key={customer._id} customer={customer} />
              ))
            )}
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

interface LeadCardProps {
  lead: Lead;
  updateStatus: (id: string, status: Lead["status"]) => void;
  statusColor: (status: string) => string;
}

interface CustomerCardProps {
  customer: User;
}

const LeadCard = ({ lead, updateStatus, statusColor }: LeadCardProps) => {
  const leadName = `${lead.first_name} ${lead.last_name}`;
  const propertyTitle = lead.interested_property?.title || 'No specific property';

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{leadName}</CardTitle>
            <CardDescription>{propertyTitle}</CardDescription>
          </div>
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${statusColor(lead.status)}`}>
            {lead.status.charAt(0).toUpperCase() + lead.status.slice(1)}
          </span>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{lead.email}</span>
          </div>
          <div className="flex items-center">
            <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{lead.phone}</span>
          </div>
        </div>
        {lead.property_preferences && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Preferences</h4>
            <div className="text-xs text-muted-foreground space-y-1">
              {lead.property_preferences.budget_min && lead.property_preferences.budget_max && (
                <div>Budget: ${lead.property_preferences.budget_min?.toLocaleString()} - ${lead.property_preferences.budget_max?.toLocaleString()}</div>
              )}
              {lead.property_preferences.bedrooms && (
                <div>Bedrooms: {lead.property_preferences.bedrooms}</div>
              )}
              {lead.property_preferences.property_type && lead.property_preferences.property_type.length > 0 && (
                <div>Type: {lead.property_preferences.property_type.join(', ')}</div>
              )}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="border-t pt-4 flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          Added on: {new Date(lead.created_at).toLocaleDateString()}
        </div>
        <div className="flex gap-2">
          <Button size="sm" variant="outline">
            <Mail className="mr-1 h-4 w-4" />
            Email
          </Button>
          <Button size="sm" variant="outline">
            <Phone className="mr-1 h-4 w-4" />
            Call
          </Button>
          {lead.status !== "qualified" && lead.status !== "converted" && lead.status !== "lost" && (
            <Button
              size="sm"
              className="bg-green-600 hover:bg-green-700"
              onClick={() => updateStatus(lead._id, "qualified")}
            >
              <Check className="mr-1 h-4 w-4" />
              Mark Qualified
            </Button>
          )}
          {lead.status === "qualified" && (
            <Button
              size="sm"
              className="bg-purple-600 hover:bg-purple-700"
              onClick={() => updateStatus(lead._id, "converted")}
            >
              <Check className="mr-1 h-4 w-4" />
              Mark Converted
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

const CustomerCard = ({ customer }: CustomerCardProps) => {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{customer.first_name} {customer.last_name}</CardTitle>
            <CardDescription>Customer since {new Date(customer.created_at).toLocaleDateString()}</CardDescription>
          </div>
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
            customer.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {customer.is_active ? 'Active' : 'Inactive'}
          </span>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {customer.email && (
            <div className="flex items-center">
              <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{customer.email}</span>
            </div>
          )}
          <div className="flex items-center">
            <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{customer.phone}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t pt-4 flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          Role: {customer.role}
        </div>
        <div className="flex gap-2">
          <Button size="sm" variant="outline">
            <Mail className="mr-1 h-4 w-4" />
            Email
          </Button>
          <Button size="sm" variant="outline">
            <Phone className="mr-1 h-4 w-4" />
            Call
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default CRM;
