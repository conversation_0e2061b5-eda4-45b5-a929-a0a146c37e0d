import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import AdminLayout from "./AdminLayout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Mail, 
  Phone, 
  Check, 
  Users, 
  Loader2, 
  Plus,
  Eye,
  Calendar,
  Clock,
  TrendingUp,
  AlertCircle,
  MessageSquare,
  UserCheck,
  PhoneCall,
  Video,
  FileText,
  Search,
  Filter
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog";
import { authApi, User, leadApi, Lead, LeadStats } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";

const CRMEnhanced = () => {
  const { toast } = useToast();
  const [leads, setLeads] = useState<Lead[]>([]);
  const [customers, setCustomers] = useState<User[]>([]);
  const [leadStats, setLeadStats] = useState<LeadStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortOption, setSortOption] = useState("newest");
  const [pagination, setPagination] = useState({ page: 1, limit: 20, total: 0, pages: 0 });

  // Fetch data
  useEffect(() => {
    loadData();
  }, [activeTab, searchTerm, sortOption, pagination.page]);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load leads
      const leadFilters = {
        status: activeTab !== 'all' && activeTab !== 'customers' ? activeTab : undefined,
        search: searchTerm || undefined,
        sort_by: sortOption === 'newest' ? 'created_at' : sortOption === 'oldest' ? 'created_at' : 'first_name',
        sort_order: sortOption === 'newest' ? 'desc' : 'asc',
        page: pagination.page,
        limit: pagination.limit
      };

      const [leadsResponse, statsResponse, customersResponse] = await Promise.all([
        leadApi.getLeads(leadFilters),
        leadApi.getLeadStats(),
        authApi.getAllUsers()
      ]);

      setLeads(leadsResponse.data);
      setPagination(leadsResponse.pagination);
      setLeadStats(statsResponse);
      
      // Filter only customers
      const customerUsers = customersResponse.users.filter(user => user.role === 'CUSTOMER');
      setCustomers(customerUsers);

    } catch (error: any) {
      console.error('Error loading CRM data:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load CRM data. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'contacted': return 'bg-yellow-100 text-yellow-800';
      case 'qualified': return 'bg-green-100 text-green-800';
      case 'proposal': return 'bg-purple-100 text-purple-800';
      case 'negotiation': return 'bg-orange-100 text-orange-800';
      case 'closed_won': return 'bg-green-100 text-green-800';
      case 'closed_lost': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleQuickAction = async (leadId: string, action: string) => {
    try {
      switch (action) {
        case 'contact':
          await leadApi.markAsContacted(leadId);
          break;
        case 'qualify':
          await leadApi.markAsQualified(leadId);
          break;
        case 'lost':
          await leadApi.markAsLost(leadId);
          break;
        case 'won':
          await leadApi.markAsWon(leadId);
          break;
      }
      
      toast({
        title: 'Success',
        description: 'Lead status updated successfully.',
      });
      
      loadData(); // Reload data
    } catch (error: any) {
      console.error('Error updating lead:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update lead. Please try again.',
      });
    }
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">Customer Relationship Management</h1>
            <p className="text-gray-600">
              Manage leads, track interactions, and convert prospects to customers
            </p>
          </div>
          <Button className="bg-nibret-blue hover:bg-nibret-blue/90">
            <Plus className="mr-2 h-4 w-4" />
            Add New Lead
          </Button>
        </div>

        {/* Stats Overview */}
        {leadStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Total Leads</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-nibret-blue">
                  {leadStats.stats.reduce((sum, stat) => sum + stat.count, 0)}
                </div>
                <p className="text-xs text-green-600">+12% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Qualified Leads</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-nibret-blue">
                  {leadStats.stats.find(s => s._id === 'qualified')?.count || 0}
                </div>
                <p className="text-xs text-green-600">+8% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Conversion Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-nibret-blue">
                  {leadStats.conversion_funnel.length > 0 ? 
                    Math.round((leadStats.conversion_funnel.find(f => f._id === 'closed_won')?.count || 0) / 
                    leadStats.conversion_funnel.reduce((sum, f) => sum + f.count, 0) * 100) : 0}%
                </div>
                <p className="text-xs text-green-600">+5% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Follow-ups Due</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-nibret-blue">
                  {leadStats.upcoming_follow_ups.length}
                </div>
                <p className="text-xs text-red-600">
                  {leadStats.overdue_follow_ups.length} overdue
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="search">Search Leads</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    placeholder="Search by name, email, or phone"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 text-gray-900 placeholder:text-gray-500 bg-white"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="sort">Sort By</Label>
                <Select value={sortOption} onValueChange={setSortOption}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sort option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest first</SelectItem>
                    <SelectItem value="oldest">Oldest first</SelectItem>
                    <SelectItem value="first_name">Name (A-Z)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button variant="outline" onClick={loadData} className="w-full">
                  <Filter className="mr-2 h-4 w-4" />
                  Refresh
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="all">All Leads ({leads.length})</TabsTrigger>
            <TabsTrigger value="new">New</TabsTrigger>
            <TabsTrigger value="contacted">Contacted</TabsTrigger>
            <TabsTrigger value="qualified">Qualified</TabsTrigger>
            <TabsTrigger value="closed_won">Won</TabsTrigger>
            <TabsTrigger value="closed_lost">Lost</TabsTrigger>
            <TabsTrigger value="customers">Customers ({customers.length})</TabsTrigger>
          </TabsList>

          {/* Lead Tabs */}
          {['all', 'new', 'contacted', 'qualified', 'closed_won', 'closed_lost'].map((tab) => (
            <TabsContent key={tab} value={tab} className="space-y-4">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
                  <span className="ml-2 text-gray-600">Loading leads...</span>
                </div>
              ) : leads.length === 0 ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No leads found</h3>
                    <p className="text-gray-600 mb-4">
                      {tab === 'all' ? 'No leads have been created yet.' : `No ${tab} leads found.`}
                    </p>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Lead
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                leads.map((lead) => (
                  <Card key={lead._id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">
                            {lead.first_name} {lead.last_name}
                          </CardTitle>
                          <CardDescription className="mt-1">
                            {lead.interested_property?.title || 'General inquiry'}
                          </CardDescription>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getStatusColor(lead.status)}>
                            {lead.status.replace('_', ' ').toUpperCase()}
                          </Badge>
                          {lead.priority === 'high' && (
                            <Badge variant="destructive">High Priority</Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-3">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-center space-x-2">
                          <Mail className="h-4 w-4 text-gray-500" />
                          <span className="text-sm">{lead.email}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4 text-gray-500" />
                          <span className="text-sm">{lead.phone}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <span className="text-sm">Created: {formatDate(lead.created_at)}</span>
                        </div>
                        {lead.last_contact_date && (
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-gray-500" />
                            <span className="text-sm">Last contact: {formatDate(lead.last_contact_date)}</span>
                          </div>
                        )}
                      </div>
                      {lead.next_follow_up && (
                        <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                          <div className="flex items-center space-x-2">
                            <AlertCircle className="h-4 w-4 text-yellow-600" />
                            <span className="text-sm text-yellow-800">
                              Follow-up due: {formatDate(lead.next_follow_up)}
                            </span>
                          </div>
                        </div>
                      )}
                    </CardContent>
                    <CardFooter className="border-t pt-4 flex justify-between items-center">
                      <div className="text-sm text-gray-500">
                        Source: {lead.source} • {lead.interactions.length} interactions
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline" asChild>
                          <Link to={`/admin/crm/leads/${lead._id}`}>
                            <Eye className="mr-1 h-4 w-4" />
                            View Details
                          </Link>
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleQuickAction(lead._id, 'contact')}
                        >
                          <PhoneCall className="mr-1 h-4 w-4" />
                          Call
                        </Button>
                        {lead.status === 'new' && (
                          <Button 
                            size="sm" 
                            onClick={() => handleQuickAction(lead._id, 'contact')}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <Check className="mr-1 h-4 w-4" />
                            Mark Contacted
                          </Button>
                        )}
                        {lead.status === 'contacted' && (
                          <Button 
                            size="sm" 
                            onClick={() => handleQuickAction(lead._id, 'qualify')}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            <UserCheck className="mr-1 h-4 w-4" />
                            Qualify
                          </Button>
                        )}
                      </div>
                    </CardFooter>
                  </Card>
                ))
              )}
            </TabsContent>
          ))}

          {/* Customers Tab */}
          <TabsContent value="customers" className="space-y-4">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
                <span className="ml-2 text-gray-600">Loading customers...</span>
              </div>
            ) : customers.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
                  <p className="text-gray-600">No registered customers yet.</p>
                </CardContent>
              </Card>
            ) : (
              customers.map((customer) => (
                <Card key={customer.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">
                          {customer.first_name} {customer.last_name}
                        </CardTitle>
                        <CardDescription>
                          Customer since {formatDate(customer.created_at)}
                        </CardDescription>
                      </div>
                      <Badge className={customer.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {customer.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{customer.email}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{customer.phone}</span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="border-t pt-4 flex justify-between items-center">
                    <div className="text-sm text-gray-500">
                      Role: {customer.role}
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Mail className="mr-1 h-4 w-4" />
                        Email
                      </Button>
                      <Button size="sm" variant="outline">
                        <Phone className="mr-1 h-4 w-4" />
                        Call
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))
            )}
          </TabsContent>
        </Tabs>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="flex justify-center items-center space-x-2 mt-8">
            <Button
              variant="outline"
              disabled={pagination.page === 1}
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
            >
              Previous
            </Button>
            <span className="text-sm text-gray-600">
              Page {pagination.page} of {pagination.pages}
            </span>
            <Button
              variant="outline"
              disabled={pagination.page === pagination.pages}
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default CRMEnhanced;
