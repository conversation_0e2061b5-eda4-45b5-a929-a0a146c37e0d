import React, { useState, useEffect, useContext } from 'react';
import { useAppContext } from '../context/AppContext';
import { AuthContext } from '../App';
import Navbar from '../components/Navbar';
import PageHeader from '../components/PageHeader';
import ExchangeRateBanner from '../components/ExchangeRateBanner';
import { Button } from '@/components/ui/button';
import { Calendar, Check, Search, Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import PropertyCard from '@/components/PropertyCard';
import PropertyListGate from '@/components/PropertyListGate';
import SignInPrompt from '@/components/SignInPrompt';
import Footer from '@/components/Footer';
import AdvancedFilter, { FilterOptions } from '@/components/AdvancedFilter';
import { Input } from '@/components/ui/input';
import { propertyApi, Property } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

const Rent = () => {
  const { translate } = useAppContext();
  const { isAuthenticated } = useContext(AuthContext);
  const { toast } = useToast();

  const [properties, setProperties] = useState<Property[]>([]);
  const [filteredProperties, setFilteredProperties] = useState<Property[]>([]);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({});
  const [moveInDate, setMoveInDate] = useState("");
  const [loading, setLoading] = useState(true);

  // Fetch rental properties from API
  useEffect(() => {
    const fetchProperties = async () => {
      try {
        setLoading(true);
        // Filter for rental properties only
        const data = await propertyApi.getProperties({ status: 'for_rent' });
        setProperties(data);
        setFilteredProperties(data);
      } catch (error) {
        console.error('Error fetching rental properties:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load rental properties. Please try again.",
        });
        // Fallback to empty array
        setProperties([]);
        setFilteredProperties([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, [toast]);

  const handleFilter = (options: FilterOptions) => {
    let filtered = [...properties];

    // Filter by search term
    if (options.searchTerm) {
      const term = options.searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        p.title.toLowerCase().includes(term) ||
        p.address.toLowerCase().includes(term) ||
        p.propertyType.toLowerCase().includes(term)
      );
    }

    // Filter by price
    if (options.minPrice) {
      filtered = filtered.filter(p => p.price >= options.minPrice!);
    }
    if (options.maxPrice) {
      filtered = filtered.filter(p => p.price <= options.maxPrice!);
    }

    // Filter by bedrooms
    if (options.minBeds) {
      filtered = filtered.filter(p => p.beds >= options.minBeds!);
    }
    if (options.maxBeds) {
      filtered = filtered.filter(p => p.beds <= options.maxBeds!);
    }

    // Filter by sqft
    if (options.minSqft) {
      filtered = filtered.filter(p => p.sqft >= options.minSqft!);
    }
    if (options.maxSqft) {
      filtered = filtered.filter(p => p.sqft <= options.maxSqft!);
    }

    // Filter by property type
    if (options.propertyType && options.propertyType !== 'any') {
      filtered = filtered.filter(p => p.propertyType === options.propertyType);
    }

    // Sort by
    if (options.sortBy) {
      switch (options.sortBy) {
        case 'price_high':
          filtered.sort((a, b) => b.price - a.price);
          break;
        case 'price_low':
          filtered.sort((a, b) => a.price - b.price);
          break;
        case 'sqft_high':
          filtered.sort((a, b) => b.sqft - a.sqft);
          break;
        case 'newest':
        default:
          // Assume the order is already newest first
          break;
      }
    }

    setFilterOptions(options);
    setFilteredProperties(filtered);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      <ExchangeRateBanner />

      <PageHeader
        title={translate("Find Your Perfect Rental", "ፍጹም የኪራይ ቤትዎን ይፈልጉ")}
        subtitle={translate("Discover high-quality rental properties across Ethiopia", "በኢትዮጵያ ውስጥ ጥራት ያላቸውን የኪራይ ንብረቶች ያግኙ")}
        backgroundImage="https://picsum.photos/id/1039/1600/800"
      >
        <Card className="max-w-4xl mx-auto mt-8 border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="col-span-1 md:col-span-3">
                <AdvancedFilter onFilter={handleFilter} initialOptions={filterOptions} />
              </div>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  type="text"
                  placeholder={translate("Move-in Date", "የማንቀሳቀሻ ቀን")}
                  className="pl-10 bg-white border border-gray-200 h-12"
                  value={moveInDate}
                  onChange={(e) => setMoveInDate(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </PageHeader>

      <div className="container mx-auto py-12 px-4 md:px-8">
        {/* Properties Section - Always visible */}
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-bold text-nibret-blue">
            {translate("Available Rentals", "ነባር ኪራዮች")}
          </h2>
          <div className="text-sm text-gray-600">
            {translate("Showing", "እያሳየ ነው")} <span className="font-medium">{filteredProperties.length}</span> {translate("properties", "ንብረቶች")}
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
            <span className="ml-2 text-gray-600">{translate("Loading rental properties...", "የኪራይ ንብረቶችን በመጫን ላይ...")}</span>
          </div>
        ) : filteredProperties.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600 text-lg">
              {translate("No rental properties found matching your criteria.", "ከመስፈርትዎ ጋር የሚዛመድ የኪራይ ንብረት አልተገኘም።")}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {filteredProperties.map((property) => (
              <PropertyCard key={property.id} property={property} showSlideshow={true} />
            ))}
          </div>
        )}

        {/* Sign-in Prompt for Non-authenticated Users - Below properties */}
        {!isAuthenticated && (
          <div className="py-12 mt-12">
            <SignInPrompt
              variant="card"
              trigger="view"
              title={translate('Sign In for Full Access', 'ሙሉ መዳረሻ ለማግኘት ይግቡ')}
              description={translate('Get complete access to rental property details, contact information, and advanced search features', 'ሙሉ የኪራይ ንብረት ዝርዝሮች፣ የመገናኛ መረጃ እና የላቀ የፍለጋ ባህሪያት ያግኙ')}
              showBenefits={true}
              showStats={true}
              className="max-w-4xl mx-auto"
            />
          </div>
        )}

        <div className="mt-16 bg-white rounded-xl shadow-lg p-8 border border-gray-100">
          <h2 className="text-2xl font-bold text-nibret-blue mb-6 text-center">
            {translate("Renting Made Easy", "ቀላል የሆነ ኪራይ")}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: translate("Find", "ይፈልጉ"),
                description: translate(
                  "Browse our curated selection of high-quality rental properties across Ethiopia.",
                  "በኢትዮጵያ ውስጥ ባሉ ጥራት ያላቸው የኪራይ ንብረቶች ዝርዝራችንን ይመልከቱ።"
                ),
                icon: <Search className="h-8 w-8 text-nibret-gold" />
              },
              {
                title: translate("Visit", "ይጎብኙ"),
                description: translate(
                  "Schedule viewings at your convenience with our responsive property agents.",
                  "ምላሽ ከሚሰጡ የንብረት ወኪሎቻችን ጋር በምቾትዎ የምልከታ ጊዜዎችን ያስይዙ።"
                ),
                icon: <Calendar className="h-8 w-8 text-nibret-gold" />
              },
              {
                title: translate("Rent", "ኪራይ"),
                description: translate(
                  "Simple application process with transparent terms and ongoing support.",
                  "ግልጽ በሆኑ ውሎች እና ቀጣይነት ባለው ድጋፍ ቀላል የማመልከቻ ሂደት።"
                ),
                icon: <Check className="h-8 w-8 text-nibret-gold" />
              }
            ].map((step, index) => (
              <Card key={index} className="border-0 shadow-md">
                <CardContent className="p-6 text-center">
                  <div className="mx-auto bg-nibret-blue/10 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                    {step.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-nibret-blue mb-2">{step.title}</h3>
                  <p className="text-gray-600">{step.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="flex justify-center mt-8">
            <Button className="bg-nibret-blue hover:bg-nibret-blue/90">
              {translate("Contact a Rental Specialist", "የኪራይ ስፔሻሊስት ያግኙ")}
            </Button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Rent;
