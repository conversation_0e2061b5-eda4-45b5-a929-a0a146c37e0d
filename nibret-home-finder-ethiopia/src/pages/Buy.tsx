import React, { useState, useEffect, useContext } from 'react';
import { useAppContext } from '../context/AppContext';
import { AuthContext } from '../App';
import Navbar from '../components/Navbar';
import PageHeader from '../components/PageHeader';
import AuthModal from '../components/AuthModal';
import ExchangeRateBanner from '../components/ExchangeRateBanner';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Check, MapPin, Loader2, Lock } from 'lucide-react';
import PropertyCard from '@/components/PropertyCard';
import PropertyListGate from '@/components/PropertyListGate';
import SignInPrompt from '@/components/SignInPrompt';
import Footer from '@/components/Footer';
import AdvancedFilter, { FilterOptions } from '@/components/AdvancedFilter';
import { propertyApi, Property } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

const Buy = () => {
  const { translate } = useAppContext();
  const { toast } = useToast();
  const { isAuthenticated } = useContext(AuthContext);

  const [properties, setProperties] = useState<Property[]>([]);
  const [filteredProperties, setFilteredProperties] = useState<Property[]>([]);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({});
  const [loading, setLoading] = useState(true);
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Fetch properties from API
  useEffect(() => {
    const fetchProperties = async () => {
      try {
        setLoading(true);
        // Filter for sale properties only
        const data = await propertyApi.getProperties({ status: 'for_sale' });
        setProperties(data);
        setFilteredProperties(data);
      } catch (error) {
        console.error('Error fetching properties:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load properties. Please try again.",
        });
        // Fallback to empty array
        setProperties([]);
        setFilteredProperties([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, [toast]);

  const handleFilter = (options: FilterOptions) => {
    // Check authentication for advanced filtering
    if (!isAuthenticated && (options.searchTerm || options.minPrice || options.maxPrice ||
        options.minBeds || options.maxBeds || options.propertyType !== 'any')) {
      setShowAuthModal(true);
      return;
    }

    let filtered = [...properties];

    // Filter by search term
    if (options.searchTerm) {
      const term = options.searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        p.title.toLowerCase().includes(term) ||
        p.address.toLowerCase().includes(term) ||
        p.propertyType.toLowerCase().includes(term)
      );
    }

    // Filter by price
    if (options.minPrice) {
      filtered = filtered.filter(p => p.price >= options.minPrice!);
    }
    if (options.maxPrice) {
      filtered = filtered.filter(p => p.price <= options.maxPrice!);
    }

    // Filter by bedrooms
    if (options.minBeds) {
      filtered = filtered.filter(p => p.beds >= options.minBeds!);
    }
    if (options.maxBeds) {
      filtered = filtered.filter(p => p.beds <= options.maxBeds!);
    }

    // Filter by sqft
    if (options.minSqft) {
      filtered = filtered.filter(p => p.sqft >= options.minSqft!);
    }
    if (options.maxSqft) {
      filtered = filtered.filter(p => p.sqft <= options.maxSqft!);
    }

    // Filter by property type
    if (options.propertyType && options.propertyType !== 'any') {
      filtered = filtered.filter(p => p.propertyType === options.propertyType);
    }

    // Sort by
    if (options.sortBy) {
      switch (options.sortBy) {
        case 'price_high':
          filtered.sort((a, b) => b.price - a.price);
          break;
        case 'price_low':
          filtered.sort((a, b) => a.price - b.price);
          break;
        case 'sqft_high':
          filtered.sort((a, b) => b.sqft - a.sqft);
          break;
        case 'newest':
        default:
          // Assume the order is already newest first
          break;
      }
    }

    // Limit results for non-authenticated users
    if (!isAuthenticated) {
      filtered = filtered.slice(0, 6); // Show only first 6 properties
    }

    setFilterOptions(options);
    setFilteredProperties(filtered);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      <ExchangeRateBanner />

      <PageHeader
        title={translate("Find Your Dream Home", "የህልምዎን ቤት ይፈልጉ")}
        subtitle={translate("Discover premium properties across Ethiopia", "በኢትዮጵያ ውስጥ ጥራት ያላቸውን ንብረቶች ያግኙ")}
        backgroundImage="https://picsum.photos/id/1048/1600/800"
      >
        <Card className="max-w-4xl mx-auto mt-8 border-0 shadow-lg">
          <CardContent className="p-6">
            <AdvancedFilter onFilter={handleFilter} initialOptions={filterOptions} />
          </CardContent>
        </Card>
      </PageHeader>

      <div className="container mx-auto py-12 px-4 md:px-8">
        {/* Properties Section - Always visible */}
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-bold text-nibret-blue">
            {translate("Properties for Sale", "ለሽያጭ ያሉ ንብረቶች")}
          </h2>
          <div className="text-sm text-gray-600">
            {translate("Showing", "እያሳየ ነው")} <span className="font-medium">{filteredProperties.length}</span> {translate("properties", "ንብረቶች")}
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
            <span className="ml-2 text-gray-600">{translate("Loading properties...", "ንብረቶችን በመጫን ላይ...")}</span>
          </div>
        ) : filteredProperties.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600 text-lg">
              {translate("No properties found matching your criteria.", "ከመስፈርትዎ ጋር የሚዛመድ ንብረት አልተገኘም።")}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {filteredProperties.map((property) => (
              <PropertyCard key={property.id} property={property} showSlideshow={true} />
            ))}
          </div>
        )}

        {/* Sign-in Prompt for Non-authenticated Users - Below properties */}
        {!isAuthenticated && (
          <div className="py-12 mt-12">
            <SignInPrompt
              variant="card"
              trigger="view"
              title={translate('Sign In for Full Access', 'ሙሉ መዳረሻ ለማግኘት ይግቡ')}
              description={translate('Get complete access to property details, contact information, and advanced search features', 'ሙሉ የንብረት ዝርዝሮች፣ የመገናኛ መረጃ እና የላቀ የፍለጋ ባህሪያት ያግኙ')}
              showBenefits={true}
              showStats={true}
              className="max-w-4xl mx-auto"
            />
          </div>
        )}

        <div className="mt-16 bg-white rounded-xl shadow-lg p-8 border border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h2 className="text-2xl font-bold text-nibret-blue mb-4">
                {translate("Why Buy With Nibret?", "ለምን በኒብረት ይገዛሉ?")}
              </h2>
              <p className="text-gray-700 mb-6">
                {translate(
                  "At Nibret, we're committed to helping you find the perfect home that meets your needs and exceeds your expectations.",
                  "በኒብረት ፣ ፍላጎቶችዎን የሚያሟላና ከፍላጎትዎ በላይ የሚሆን ፍጹም ቤት እንዲያገኙ ለመርዳት ቁርጠኞች ነን።"
                )}
              </p>

              <ul className="space-y-3">
                {[
                  translate("Extensive selection of premium properties", "ሰፊ የፕሪሚየም ንብረቶች ምርጫ"),
                  translate("Expert local real estate advisors", "የሙያ አካባቢያዊ የሪል እስቴት አማካሪዎች"),
                  translate("Streamlined buying process", "የተቀላጠፈ የግዢ ሂደት"),
                  translate("Post-purchase support and services", "ከግዢ በኋላ ድጋፍ እና አገልግሎቶች")
                ].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <div className="mt-1 mr-3 bg-nibret-gold/20 p-1 rounded-full">
                      <Check className="h-4 w-4 text-nibret-blue" />
                    </div>
                    <span>{item}</span>
                  </li>
                ))}
              </ul>

              <Button className="mt-6 bg-nibret-blue hover:bg-nibret-blue/90">
                {translate("Contact a Buying Agent", "የግዢ ወኪል ያግኙ")}
              </Button>
            </div>

            <div className="relative h-64 md:h-auto rounded-xl overflow-hidden">
              <div className="absolute inset-0 bg-nibret-blue/10 z-10 rounded-xl"></div>
              <img
                src="https://picsum.photos/id/1067/800/600"
                alt={translate("Happy homeowners", "ደስተኛ የቤት ባለቤቶች")}
                className="w-full h-full object-cover rounded-xl"
              />
              <div className="absolute bottom-6 right-6 bg-white p-3 rounded-lg shadow-lg z-20">
                <MapPin className="h-8 w-8 text-nibret-blue" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        defaultTab="login"
      />
    </div>
  );
};

export default Buy;
