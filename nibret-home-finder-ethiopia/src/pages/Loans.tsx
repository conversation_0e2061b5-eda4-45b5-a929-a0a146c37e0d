import React from 'react';
import { useAppContext } from '../context/AppContext';
import Navbar from '../components/Navbar';
import PageHeader from '../components/PageHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { Calculator, DollarSign, Clock, FileText, PercentSquare, ArrowRight, Home } from 'lucide-react';
import MortgageCalculator from '@/components/MortgageCalculator';
import Footer from '@/components/Footer';

const Loans = () => {
  const { translate } = useAppContext();
  const { toast } = useToast();
  
  // Form schema
  const formSchema = z.object({
    name: z.string().min(2, { message: "Name must be at least 2 characters." }),
    email: z.string().email({ message: "Please enter a valid email address." }),
    phone: z.string().min(10, { message: "Please enter a valid phone number." }),
    loanAmount: z.string().min(1, { message: "Please enter desired loan amount." }),
  });
  
  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      loanAmount: "",
    },
  });
  
  const onSubmit = (values: z.infer<typeof formSchema>) => {
    console.log("Form submitted:", values);
    
    toast({
      title: translate("Application Received", "ማመልከቻ ደርሷል"),
      description: translate("Our mortgage specialist will contact you soon.", "የብድር ባለሙያችን በቅርቡ ያገኝዎታል።"),
    });
    
    form.reset();
  };

  const loanOptions = [
    {
      title: translate("Home Purchase Loans", "የቤት ግዢ ብድሮች"),
      icon: <Home className="h-8 w-8 text-nibret-gold" />,
      description: translate(
        "Finance your new home purchase with competitive interest rates and flexible terms.",
        "ዕድሜዎን የሚወዳደሩ የወለድ መጠኖች እና ተለዋዋጭ ውሎች ካሉት አዲስ መኖሪያ ቤትዎን ይግዙ።"
      ),
      rate: "9.5%"
    },
    {
      title: translate("Refinance", "ዳግም ፋይናንስ"),
      icon: <PercentSquare className="h-8 w-8 text-nibret-gold" />,
      description: translate(
        "Lower your monthly payments, reduce your interest rate, or access your home equity.",
        "የወር ክፍያዎችዎን ይቀንሱ፣ የወለድ መጠንዎን ይቀንሱ፣ ወይም የቤትዎን ኢኪዊቲ ይድረሱ።"
      ),
      rate: "9.8%"
    },
    {
      title: translate("Construction Loans", "የግንባታ ብድሮች"),
      icon: <FileText className="h-8 w-8 text-nibret-gold" />,
      description: translate(
        "Fund your new home construction project with our tailored loan solutions.",
        "በተበጀው የብድር መፍትሄዎቻችን አዲስ የቤት ግንባታ ፕሮጀክትዎን ይፈንዱ።"
      ),
      rate: "10.2%"
    }
  ];
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      
      <PageHeader 
        title={translate("Mortgage & Loan Solutions", "የብድር እና የብድር መፍትሄዎች")}
        subtitle={translate("Find the right financing option for your next home", "ለቀጣዩ ቤትዎ ትክክለኛውን የፋይናንስ አማራጭ ያግኙ")}
        backgroundImage="https://picsum.photos/id/1061/1600/800"
      >
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          {[
            {
              icon: <DollarSign className="h-6 w-6 text-nibret-blue" />,
              title: translate("Competitive Rates", "ተወዳዳሪ መጠኖች")
            },
            {
              icon: <Clock className="h-6 w-6 text-nibret-blue" />,
              title: translate("Fast Approval", "ፈጣን ማፅደቅ")
            },
            {
              icon: <Calculator className="h-6 w-6 text-nibret-blue" />,
              title: translate("Flexible Terms", "ተለዋዋጭ ውሎች")
            }
          ].map((feature, index) => (
            <Card key={index} className="bg-white/80 backdrop-blur border-0 shadow-md">
              <CardContent className="flex items-center p-4">
                <div className="bg-nibret-gold/20 p-2 rounded-full mr-4">
                  {feature.icon}
                </div>
                <p className="font-medium">{feature.title}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </PageHeader>
      
      <div className="container mx-auto py-12 px-4 md:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div>
            <h2 className="text-3xl font-bold text-nibret-blue mb-6">
              {translate("Loan Options", "የብድር አማራጮች")}
            </h2>
            
            <div className="space-y-6">
              {loanOptions.map((option, index) => (
                <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow border-0 shadow-md">
                  <CardHeader className="bg-nibret-blue/5 border-b">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="bg-white p-2 rounded-full shadow-sm mr-4">
                          {option.icon}
                        </div>
                        <CardTitle className="text-nibret-blue">{option.title}</CardTitle>
                      </div>
                      <span className="bg-nibret-gold/20 text-nibret-blue font-bold py-1 px-3 rounded-full">
                        {translate("From", "ከ")} {option.rate}
                      </span>
                    </div>
                  </CardHeader>
                  <CardContent className="p-6">
                    <p className="text-gray-700 mb-4">{option.description}</p>
                    <Button variant="outline" className="text-nibret-blue border-nibret-blue hover:bg-nibret-blue/10">
                      {translate("Learn More", "ተጨማሪ ይወቁ")}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            <Card className="mt-12 bg-gradient-to-r from-nibret-blue to-nibret-blue/90 text-white border-0 shadow-xl">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold mb-4">
                  {translate("Need Help?", "እርዳታ ያስፈልጎታል?")}
                </h3>
                <p className="mb-6 opacity-90">
                  {translate(
                    "Our mortgage specialists are ready to guide you through the process and help you find the right loan solution.",
                    "የብድር ባለሙያዎቻችን ሂደቱን በመመራት እና ትክክለኛውን የብድር መፍትሄ እንዲያገኙ ለመርዳት ዝግጁ ናቸው።"
                  )}
                </p>
                <Button variant="secondary" className="bg-white text-nibret-blue hover:bg-white/90">
                  {translate("Schedule a Consultation", "የምክክር ስብሰባ ይያዙ")}
                </Button>
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-8">
            <Card className="border-0 shadow-xl">
              <CardHeader className="bg-nibret-gold/10 border-b">
                <CardTitle className="text-center text-nibret-blue">
                  {translate("Mortgage Calculator", "የብድር ማስያ")}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <MortgageCalculator />
              </CardContent>
            </Card>
            
            <Card className="border-0 shadow-xl">
              <CardHeader className="bg-nibret-blue text-white rounded-t-xl">
                <CardTitle>
                  {translate("Get Pre-Approved", "ቅድመ ማፅደቅ ያግኙ")}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate("Full Name", "ሙሉ ስም")}</FormLabel>
                          <FormControl>
                            <Input placeholder={translate("Enter your name", "ስምዎን ያስገቡ")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate("Email", "ኢሜይል")}</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder={translate("Enter your email", "ኢሜይልዎን ያስገቡ")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate("Phone", "ስልክ")}</FormLabel>
                          <FormControl>
                            <Input placeholder={translate("Enter your phone number", "የስልክ ቁጥርዎን ያስገቡ")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="loanAmount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{translate("Desired Loan Amount", "የሚፈልጉት የብድር መጠን")}</FormLabel>
                          <FormControl>
                            <Input placeholder={translate("Enter amount", "መጠኑን ያስገቡ")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <Button type="submit" className="w-full bg-nibret-blue hover:bg-nibret-blue/90">
                      {translate("Submit Application", "ማመልከቻ አስገባ")}
                    </Button>
                  </form>
                </Form>
                
                <p className="text-sm text-gray-500 mt-4">
                  {translate(
                    "By submitting, you agree to our terms of service and privacy policy.",
                    "በማስገባትዎ፣ የአገልግሎት ደንቦቻችንን እና የግላዊነት ፖሊሲያችንን ተስማምተዋል።"
                  )}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default Loans;
