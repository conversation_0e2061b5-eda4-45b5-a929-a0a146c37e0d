
import React, { useState } from 'react';
import { useAppContext } from '../context/AppContext';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Gavel, Clock, Camera } from 'lucide-react';

const AuctionUpload = () => {
  const { translate } = useAppContext();
  const [formData, setFormData] = useState({
    title: '',
    titleAmharic: '',
    startingBid: '',
    reservePrice: '',
    auctionEndDate: '',
    auctionEndTime: '',
    beds: '',
    baths: '',
    sqft: '',
    address: '',
    addressAmharic: '',
    propertyType: '',
    description: '',
    descriptionAmharic: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Auction property submitted:', formData);
    // Handle form submission here
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      
      <div className="container mx-auto py-12 px-4 md:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-nibret-blue mb-4">
              {translate("Upload Auction Property", "የመጨረሻ ንብረት ይላኩ")}
            </h1>
            <p className="text-gray-600">
              {translate("List your property for auction and let buyers compete for the best price", "ንብረትዎን ለመጨረሻ ዝርዝር ውስጥ ያስገቡ እና ገዢዎች ለተሻለ ዋጋ እንዲወዳደሩ ያድርጉ")}
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Gavel className="mr-2 h-5 w-5" />
                {translate("Auction Property Details", "የመጨረሻ ንብረት ዝርዝሮች")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="title">{translate("Property Title", "የንብረት ርዕስ")}</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder={translate("Enter property title", "የንብረት ርዕስ ያስገቡ")}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="titleAmharic">{translate("Property Title (Amharic)", "የንብረት ርዕስ (አማርኛ)")}</Label>
                    <Input
                      id="titleAmharic"
                      value={formData.titleAmharic}
                      onChange={(e) => handleInputChange('titleAmharic', e.target.value)}
                      placeholder={translate("Enter property title in Amharic", "የንብረት ርዕስ በአማርኛ ያስገቡ")}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="startingBid">{translate("Starting Bid", "የመጀመሪያ ጨረታ")}</Label>
                    <Input
                      id="startingBid"
                      type="number"
                      value={formData.startingBid}
                      onChange={(e) => handleInputChange('startingBid', e.target.value)}
                      placeholder="100000"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="reservePrice">{translate("Reserve Price", "የተጠበቀ ዋጋ")}</Label>
                    <Input
                      id="reservePrice"
                      type="number"
                      value={formData.reservePrice}
                      onChange={(e) => handleInputChange('reservePrice', e.target.value)}
                      placeholder="400000"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="auctionEndDate">{translate("Auction End Date", "የመጨረሻ ቀን")}</Label>
                    <Input
                      id="auctionEndDate"
                      type="date"
                      value={formData.auctionEndDate}
                      onChange={(e) => handleInputChange('auctionEndDate', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="auctionEndTime">{translate("Auction End Time", "የመጨረሻ ሰዓት")}</Label>
                    <Input
                      id="auctionEndTime"
                      type="time"
                      value={formData.auctionEndTime}
                      onChange={(e) => handleInputChange('auctionEndTime', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="beds">{translate("Bedrooms", "መኝታ ክፍሎች")}</Label>
                    <Input
                      id="beds"
                      type="number"
                      value={formData.beds}
                      onChange={(e) => handleInputChange('beds', e.target.value)}
                      placeholder="3"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="baths">{translate("Bathrooms", "መታጠቢያ ክፍሎች")}</Label>
                    <Input
                      id="baths"
                      type="number"
                      value={formData.baths}
                      onChange={(e) => handleInputChange('baths', e.target.value)}
                      placeholder="2"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sqft">{translate("Square Feet", "ስኩየር ፊት")}</Label>
                    <Input
                      id="sqft"
                      type="number"
                      value={formData.sqft}
                      onChange={(e) => handleInputChange('sqft', e.target.value)}
                      placeholder="2000"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="propertyType">{translate("Property Type", "የንብረት አይነት")}</Label>
                    <Select value={formData.propertyType} onValueChange={(value) => handleInputChange('propertyType', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder={translate("Select property type", "የንብረት አይነት ይምረጡ")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="house">{translate("House", "ቤት")}</SelectItem>
                        <SelectItem value="apartment">{translate("Apartment", "አፓርታማ")}</SelectItem>
                        <SelectItem value="villa">{translate("Villa", "ቪላ")}</SelectItem>
                        <SelectItem value="condo">{translate("Condo", "ኮንዶ")}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">{translate("Address", "አድራሻ")}</Label>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder={translate("Enter full address", "ሙሉ አድራሻ ያስገቡ")}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">{translate("Property Description", "የንብረት መግለጫ")}</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder={translate("Describe your auction property...", "የመጨረሻ ንብረትዎን ይግለጹ...")}
                    rows={4}
                  />
                </div>

                <div className="space-y-4">
                  <Label>{translate("Property Photos", "የንብረት ፎቶዎች")}</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <Camera className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-gray-600 mb-2">
                      {translate("Upload auction property photos", "የመጨረሻ ንብረት ፎቶዎችን ይላኩ")}
                    </p>
                    <Button variant="outline">
                      {translate("Choose Files", "ፋይሎች ይምረጡ")}
                    </Button>
                  </div>
                </div>

                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <Clock className="h-5 w-5 text-amber-600 mt-0.5 mr-2" />
                    <div>
                      <h4 className="font-medium text-amber-800">
                        {translate("Auction Terms", "የመጨረሻ ውሎች")}
                      </h4>
                      <p className="text-sm text-amber-700 mt-1">
                        {translate("By submitting this auction, you agree to sell the property to the highest bidder above the reserve price.", "ይህንን መጨረሻ በማስገባት፣ ንብረቱን ከተጠበቀው ዋጋ በላይ ለከፍተኛ ጨረታ አቅራቢ ለመሸጥ ይስማማሉ።")}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-4">
                  <Button variant="outline">
                    {translate("Save as Draft", "እንደ ረቂቅ ያስቀምጡ")}
                  </Button>
                  <Button type="submit" className="bg-nibret-blue hover:bg-nibret-blue/90">
                    {translate("Start Auction", "መጨረሻ ጀምር")}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default AuctionUpload;
