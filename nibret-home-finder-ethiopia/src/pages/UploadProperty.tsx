
import React, { useState } from 'react';
import { useAppContext } from '../context/AppContext';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Upload, MapPin, Camera } from 'lucide-react';

const UploadProperty = () => {
  const { translate } = useAppContext();
  const [formData, setFormData] = useState({
    title: '',
    titleAmharic: '',
    price: '',
    beds: '',
    baths: '',
    sqft: '',
    address: '',
    addressAmharic: '',
    propertyType: '',
    description: '',
    descriptionAmharic: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Property submitted:', formData);
    // Handle form submission here
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      
      <div className="container mx-auto py-12 px-4 md:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-nibret-blue mb-4">
              {translate("Upload Property", "ንብረት ይላኩ")}
            </h1>
            <p className="text-gray-600">
              {translate("List your property on Nibret and reach thousands of potential buyers", "ንብረትዎን በኒብረት ዝርዝር ውስጥ ያስገቡ እና በሺዎች የሚቆጠሩ ገዢዎችን ያግኙ")}
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="mr-2 h-5 w-5" />
                {translate("Property Details", "የንብረት ዝርዝሮች")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="title">{translate("Property Title", "የንብረት ርዕስ")}</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder={translate("Enter property title", "የንብረት ርዕስ ያስገቡ")}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="titleAmharic">{translate("Property Title (Amharic)", "የንብረት ርዕስ (አማርኛ)")}</Label>
                    <Input
                      id="titleAmharic"
                      value={formData.titleAmharic}
                      onChange={(e) => handleInputChange('titleAmharic', e.target.value)}
                      placeholder={translate("Enter property title in Amharic", "የንብረት ርዕስ በአማርኛ ያስገቡ")}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="price">{translate("Price", "ዋጋ")}</Label>
                    <Input
                      id="price"
                      type="number"
                      value={formData.price}
                      onChange={(e) => handleInputChange('price', e.target.value)}
                      placeholder="500000"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="beds">{translate("Bedrooms", "መኝታ ክፍሎች")}</Label>
                    <Input
                      id="beds"
                      type="number"
                      value={formData.beds}
                      onChange={(e) => handleInputChange('beds', e.target.value)}
                      placeholder="3"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="baths">{translate("Bathrooms", "መታጠቢያ ክፍሎች")}</Label>
                    <Input
                      id="baths"
                      type="number"
                      value={formData.baths}
                      onChange={(e) => handleInputChange('baths', e.target.value)}
                      placeholder="2"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="sqft">{translate("Square Feet", "ስኩየር ፊት")}</Label>
                    <Input
                      id="sqft"
                      type="number"
                      value={formData.sqft}
                      onChange={(e) => handleInputChange('sqft', e.target.value)}
                      placeholder="2000"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="propertyType">{translate("Property Type", "የንብረት አይነት")}</Label>
                    <Select value={formData.propertyType} onValueChange={(value) => handleInputChange('propertyType', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder={translate("Select property type", "የንብረት አይነት ይምረጡ")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="house">{translate("House", "ቤት")}</SelectItem>
                        <SelectItem value="apartment">{translate("Apartment", "አፓርታማ")}</SelectItem>
                        <SelectItem value="villa">{translate("Villa", "ቪላ")}</SelectItem>
                        <SelectItem value="condo">{translate("Condo", "ኮንዶ")}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="address">{translate("Address", "አድራሻ")}</Label>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder={translate("Enter full address", "ሙሉ አድራሻ ያስገቡ")}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="addressAmharic">{translate("Address (Amharic)", "አድራሻ (አማርኛ)")}</Label>
                    <Input
                      id="addressAmharic"
                      value={formData.addressAmharic}
                      onChange={(e) => handleInputChange('addressAmharic', e.target.value)}
                      placeholder={translate("Enter address in Amharic", "አድራሻ በአማርኛ ያስገቡ")}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">{translate("Description", "መግለጫ")}</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder={translate("Describe your property...", "ንብረትዎን ይግለጹ...")}
                    rows={4}
                  />
                </div>

                <div className="space-y-4">
                  <Label>{translate("Property Photos", "የንብረት ፎቶዎች")}</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <Camera className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-gray-600 mb-2">
                      {translate("Upload property photos", "የንብረት ፎቶዎችን ይላኩ")}
                    </p>
                    <Button variant="outline">
                      {translate("Choose Files", "ፋይሎች ይምረጡ")}
                    </Button>
                  </div>
                </div>

                <div className="flex justify-end space-x-4">
                  <Button variant="outline">
                    {translate("Save as Draft", "እንደ ረቂቅ ያስቀምጡ")}
                  </Button>
                  <Button type="submit" className="bg-nibret-blue hover:bg-nibret-blue/90">
                    {translate("Submit Property", "ንብረት ያስመዝግቡ")}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default UploadProperty;
