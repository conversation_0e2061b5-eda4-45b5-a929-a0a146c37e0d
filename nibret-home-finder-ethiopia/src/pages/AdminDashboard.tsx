import React, { useContext } from 'react';
import { Navigate } from 'react-router-dom';
import Navbar from '../components/Navbar';
import ExchangeRateBanner from '../components/ExchangeRateBanner';
import AdminFeaturedManager from '../components/AdminFeaturedManager';
import ExchangeRateAdmin from '../components/ExchangeRateAdmin';
import Footer from '../components/Footer';
import { AuthContext } from '../App';
import { useAppContext } from '../context/AppContext';
import { Settings, Star, BarChart3, Users, Home } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const AdminDashboard: React.FC = () => {
  const { user, isAuthenticated } = useContext(AuthContext);
  const { translate } = useAppContext();

  // Check if user is authenticated and is admin
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />;
  }

  // Check if user has admin role (assuming admin phone is 0965789832)
  const isAdmin = user.phone === '0965789832' || user.role === 'ADMIN';
  
  if (!isAdmin) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      <ExchangeRateBanner />
      
      {/* Admin Dashboard Header */}
      <div className="bg-blue-600 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center space-x-4">
            <div className="bg-blue-500 p-3 rounded-lg">
              <Settings className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">
                {translate('Admin Dashboard', 'የአስተዳዳሪ ዳሽቦርድ')}
              </h1>
              <p className="text-blue-100 mt-1">
                {translate('Manage your real estate platform', 'የእርስዎን የሪል እስቴት መድረክ ያስተዳድሩ')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Content */}
      <div className="container mx-auto px-4 py-8">
        <Tabs defaultValue="featured" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="featured" className="flex items-center space-x-2">
              <Star className="w-4 h-4" />
              <span>{translate('Featured Properties', 'ተመራጭ ንብረቶች')}</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4" />
              <span>{translate('Analytics', 'ትንታኔ')}</span>
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span>{translate('Users', 'ተጠቃሚዎች')}</span>
            </TabsTrigger>
            <TabsTrigger value="properties" className="flex items-center space-x-2">
              <Home className="w-4 h-4" />
              <span>{translate('Properties', 'ንብረቶች')}</span>
            </TabsTrigger>
          </TabsList>

          {/* Featured Properties Management */}
          <TabsContent value="featured">
            <AdminFeaturedManager />
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {translate('Total Properties', 'ጠቅላላ ንብረቶች')}
                  </CardTitle>
                  <Home className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">19</div>
                  <p className="text-xs text-muted-foreground">
                    {translate('Active listings', 'ንቁ ዝርዝሮች')}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {translate('Featured Properties', 'ተመራጭ ንብረቶች')}
                  </CardTitle>
                  <Star className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">3</div>
                  <p className="text-xs text-muted-foreground">
                    {translate('Currently featured', 'በአሁኑ ጊዜ ተመራጭ')}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {translate('Total Views', 'ጠቅላላ እይታዎች')}
                  </CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1,234</div>
                  <p className="text-xs text-muted-foreground">
                    {translate('This month', 'በዚህ ወር')}
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>{translate('Quick Actions', 'ፈጣን እርምጃዎች')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  {translate('Analytics dashboard coming soon...', 'የትንታኔ ዳሽቦርድ በቅርቡ ይመጣል...')}
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users">
            <Card>
              <CardHeader>
                <CardTitle>{translate('User Management', 'የተጠቃሚ አስተዳደር')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  {translate('User management features coming soon...', 'የተጠቃሚ አስተዳደር ባህሪዎች በቅርቡ ይመጣሉ...')}
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Properties Tab */}
          <TabsContent value="properties">
            <Card>
              <CardHeader>
                <CardTitle>{translate('Property Management', 'የንብረት አስተዳደር')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  {translate('Advanced property management features coming soon...', 'የላቀ የንብረት አስተዳደር ባህሪዎች በቅርቡ ይመጣሉ...')}
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <Footer />
    </div>
  );
};

export default AdminDashboard;
