import { useState, useEffect, useContext } from 'react';
import Navbar from '../components/Navbar';
import PropertyCard from '../components/PropertyCard';
import Map from '../components/Map';
import MortgageCalculator from '../components/MortgageCalculator';
import Footer from '../components/Footer';
import AuthModal from '../components/AuthModal';
import ExchangeRateBanner from '../components/ExchangeRateBanner';

import FeaturedProperties from '../components/FeaturedProperties';
import SignInPrompt from '../components/SignInPrompt';
import { useAppContext } from '../context/AppContext';
import { AuthContext } from '../App';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { MapPin, Filter, DollarSign, Loader2, Lock } from 'lucide-react';
import { propertyApi, Property } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from '@/components/ui/card';

const Index = () => {
  const { translate } = useAppContext();
  const { isAuthenticated } = useContext(AuthContext);
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [priceRange, setPriceRange] = useState([0, 100000000]);
  const [propertyType, setPropertyType] = useState('all');
  const [bedrooms, setBedrooms] = useState('any');
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Fetch properties from API
  useEffect(() => {
    const fetchProperties = async () => {
      try {
        setLoading(true);
        setError(null);
        const fetchedProperties = await propertyApi.getProperties();
        setProperties(fetchedProperties || []);
      } catch (err) {
        console.error('Error fetching properties:', err);
        setError('Failed to load properties. Please try again later.');
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load properties. Please try again later.",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, [toast]);

  // Handle search input with authentication check
  const handleSearchChange = (value: string) => {
    if (!isAuthenticated && value.length > 0) {
      setShowAuthModal(true);
      return;
    }
    setSearchTerm(value);
  };

  // Handle filter changes with authentication check
  const handleFilterChange = () => {
    if (!isAuthenticated) {
      setShowAuthModal(true);
      return;
    }
    setShowFilters(!showFilters);
  };

  // Sort properties with featured first, then by creation date
  const sortProperties = (properties: Property[]): Property[] => {
    return properties.sort((a, b) => {
      // Featured properties first
      const aFeatured = a.is_featured || false;
      const bFeatured = b.is_featured || false;

      if (aFeatured && !bFeatured) return -1;
      if (!aFeatured && bFeatured) return 1;

      // Then by creation date (newest first)
      return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
    });
  };

  // Filter properties based on search and filters
  let filteredProperties = properties.filter(property => {
    const matchesSearch = searchTerm === '' ||
                         property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.address.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesPrice = property.price >= priceRange[0] && property.price <= priceRange[1];

    const matchesPropertyType = propertyType === 'all' ||
                               property.propertyType === propertyType;

    const matchesBedrooms = bedrooms === 'any' ||
                           (bedrooms === '1' && property.beds === 1) ||
                           (bedrooms === '2' && property.beds === 2) ||
                           (bedrooms === '3' && property.beds === 3) ||
                           (bedrooms === '4+' && property.beds >= 4);

    return matchesSearch && matchesPrice && matchesPropertyType && matchesBedrooms;
  });

  // Apply sorting (featured first, then newest)
  const sortedProperties = sortProperties(filteredProperties);

  // Limit results for non-authenticated users
  let finalProperties = sortedProperties;
  if (!isAuthenticated) {
    finalProperties = sortedProperties.slice(0, 3); // Show only first 3 properties
  }

  // Properties are already in the correct format from the API
  const propertiesWithRequiredFields = finalProperties;

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      {/* Exchange Rate Banner */}
      <ExchangeRateBanner />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-nibret-blue to-blue-800 text-white py-16">
        <div className="container mx-auto px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {translate('Find Your Dream Home in Ethiopia', 'በኢትዮጵያ የህልምዎን ቤት ይፈልጉ')}
            </h1>
            <p className="text-xl mb-8">
              {translate(
                'Browse thousands of listings, connect with agents, and find your perfect property.',
                'በሺዎች የሚቆጠሩ ዝርዝሮችን ይመልከቱ፣ ከወኪሎች ጋር ይገናኙ እና ፍጹም የሆነ ንብረትዎን ያግኙ።'
              )}
            </p>
            <div className="flex flex-col md:flex-row bg-white rounded-lg shadow-lg overflow-hidden p-1 mx-auto max-w-2xl">
              <div className="flex-1">
                <div className="relative">
                  <MapPin className="absolute top-3 left-3 h-5 w-5 text-gray-400" />
                  <Input
                    type="text"
                    placeholder={translate('Search by city, address...', 'በከተማ፣ አድራሻ ይፈልጉ...')}
                    className="w-full pl-10 pr-4 py-3 border-0 focus:ring-0 text-gray-900 placeholder:text-gray-500 bg-white"
                    value={searchTerm}
                    onChange={(e) => handleSearchChange(e.target.value)}
                  />
                  {!isAuthenticated && (
                    <div className="absolute right-3 top-3 text-gray-400">
                      <Lock className="h-4 w-4" />
                    </div>
                  )}
                </div>
              </div>
              <Button
                className="bg-nibret-gold hover:bg-nibret-gold/90 text-white font-bold px-6"
                onClick={handleFilterChange}
              >
                <Filter className="mr-2 h-4 w-4" />
                {translate('Filters', 'ማጣሪያዎች')}
                {!isAuthenticated && <Lock className="ml-2 h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Filters Section */}
      {showFilters && (
        <div className="bg-gray-50 py-4 border-b">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {translate('Price Range', 'የዋጋ ክልል')}
                </label>
                <Slider
                  value={priceRange}
                  min={0}
                  max={100000000}
                  step={1000000}
                  onValueChange={(value) => setPriceRange(value)}
                  className="mb-2"
                />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>${priceRange[0].toLocaleString()}</span>
                  <span>${priceRange[1].toLocaleString()}</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {translate('Property Type', 'የንብረት አይነት')}
                </label>
                <Select value={propertyType} onValueChange={setPropertyType}>
                  <SelectTrigger>
                    <SelectValue placeholder={translate('All Property Types', 'ሁሉም የንብረት ዓይነቶች')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{translate('All Property Types', 'ሁሉም የንብረት ዓይነቶች')}</SelectItem>
                    <SelectItem value="house">{translate('Houses', 'ቤቶች')}</SelectItem>
                    <SelectItem value="apartment">{translate('Apartments', 'አፓርታማዎች')}</SelectItem>
                    <SelectItem value="townhouse">{translate('Townhouses', 'ታውንሃውሶች')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {translate('Bedrooms', 'መኝታ ቤቶች')}
                </label>
                <Select value={bedrooms} onValueChange={setBedrooms}>
                  <SelectTrigger>
                    <SelectValue placeholder={translate('Any', 'ማንኛውም')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">{translate('Any', 'ማንኛውም')}</SelectItem>
                    <SelectItem value="1">1</SelectItem>
                    <SelectItem value="2">2</SelectItem>
                    <SelectItem value="3">3</SelectItem>
                    <SelectItem value="4+">4+</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button className="w-full bg-nibret-blue hover:bg-nibret-blue/90">
                  {translate('Apply Filters', 'ማጣሪያዎችን ተግብር')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Featured Properties Section - Only for authenticated users */}
      {isAuthenticated ? (
        <FeaturedProperties limit={6} showHeader={true} />
      ) : (
        /* Sign-in Prompt for Non-authenticated Users */
        <div className="container mx-auto px-4 py-12">
          <SignInPrompt
            variant="card"
            trigger="general"
            title={translate('Discover Premium Properties', 'ፕሪሚየም ንብረቶችን ያግኙ')}
            description={translate('Sign in to access our exclusive collection of featured properties and find your dream home', 'ልዩ የተመረጡ ንብረቶቻችንን ለማግኘት እና የህልም ቤትዎን ለማግኘት ይግቡ')}
            showBenefits={true}
            showStats={true}
            className="max-w-4xl mx-auto"
          />
        </div>
      )}

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8 flex-grow">
        <Tabs defaultValue="grid" className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold">
              {translate('Properties for Sale', 'ለሽያጭ የቀረቡ ንብረቶች')}
            </h2>
            <TabsList>
              <TabsTrigger value="grid">
                {translate('Grid View', 'በሰንጠረዥ')}
              </TabsTrigger>
              <TabsTrigger value="map">
                {translate('Map View', 'በካርታ')}
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="grid" className="mt-0">
            {!isAuthenticated && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Lock className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <h3 className="text-sm font-medium text-blue-800">
                        {translate('Limited Preview', 'የተወሰነ ቅድመ እይታ')}
                      </h3>
                      <p className="text-sm text-blue-600">
                        {translate(
                          'Showing 3 of many properties. Sign in to see all listings and full details.',
                          'ከብዙ ንብረቶች 3ቱን እያሳየ ነው። ሁሉንም ዝርዝሮች እና ሙሉ ዝርዝሮችን ለማየት ይግቡ።'
                        )}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => setShowAuthModal(true)}
                    size="sm"
                    className="bg-nibret-blue hover:bg-nibret-blue/90"
                  >
                    {translate('Sign In', 'ይግቡ')}
                  </Button>
                </div>
              </div>
            )}

            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
                <span className="ml-2 text-gray-600">{translate("Loading properties...", "ንብረቶችን በመጫን ላይ...")}</span>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <p className="text-red-600 text-lg mb-4">{error}</p>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                >
                  {translate("Try Again", "እንደገና ሞክር")}
                </Button>
              </div>
            ) : propertiesWithRequiredFields.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-600 text-lg">
                  {translate("No properties found matching your criteria.", "ከመስፈርትዎ ጋር የሚዛመድ ንብረት አልተገኘም።")}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {propertiesWithRequiredFields.map((property) => (
                  <PropertyCard key={property.id} property={property} showSlideshow={true} />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="map" className="mt-0">
            {!isAuthenticated && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Lock className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <h3 className="text-sm font-medium text-blue-800">
                        {translate('Limited Map View', 'የተወሰነ ካርታ እይታ')}
                      </h3>
                      <p className="text-sm text-blue-600">
                        {translate(
                          'Sign in to see all property locations and detailed map features.',
                          'ሁሉንም የንብረት አካባቢዎች እና ዝርዝር የካርታ ባህሪያትን ለማየት ይግቡ።'
                        )}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => setShowAuthModal(true)}
                    size="sm"
                    className="bg-nibret-blue hover:bg-nibret-blue/90"
                  >
                    {translate('Sign In', 'ይግቡ')}
                  </Button>
                </div>
              </div>
            )}
            <div className="h-[600px]">
              <Map properties={propertiesWithRequiredFields
                .filter(property => property.lat && property.lng) // Only show properties with coordinates
                .map(property => ({
                  id: property.id,
                  title: property.title,
                  price: property.price,
                  address: property.address,
                  lat: property.lat!,
                  lng: property.lng!,
                  image: property.images && property.images.length > 0 ? property.images[0] : 'https://via.placeholder.com/400x300?text=No+Image'
                }))} />
            </div>
          </TabsContent>
        </Tabs>

        {/* Mortgage Calculator Section */}
        <div className="my-16">
          <h2 className="text-2xl font-bold mb-6 text-center">
            {translate('Plan Your Investment with Our Mortgage Calculator', 'ከእኛ ብድር ማስያ ጋር ኢንቨስትመንትዎን ያቅዱ')}
          </h2>
          <div className="max-w-xl mx-auto">
            <MortgageCalculator />
          </div>
        </div>

        {/* Why Choose Us Section */}
        <div className="my-16">
          <h2 className="text-2xl font-bold mb-6 text-center">
            {translate('Why Choose Nibret?', 'ለምን ኒብረትን ይመርጣሉ?')}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="bg-nibret-blue/10 rounded-full p-4 w-16 h-16 flex items-center justify-center mb-4 mx-auto">
                  <MapPin className="h-8 w-8 text-nibret-blue" />
                </div>
                <h3 className="text-xl font-semibold text-center mb-2">
                  {translate('Extensive Listings', 'ብዙ ዝርዝሮች')}
                </h3>
                <p className="text-center text-gray-600">
                  {translate(
                    'Explore thousands of property listings across Ethiopia with rich details and high-quality images.',
                    'ከፍተኛ ዝርዝሮች እና ጥራት ያላቸው ምስሎች ያሉት በሺዎች የሚቆጠሩ ንብረት ዝርዝሮች በኢትዮጵያ አቀፍ ይመርምሩ።'
                  )}
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="bg-nibret-blue/10 rounded-full p-4 w-16 h-16 flex items-center justify-center mb-4 mx-auto">
                  <Filter className="h-8 w-8 text-nibret-blue" />
                </div>
                <h3 className="text-xl font-semibold text-center mb-2">
                  {translate('Smart Search Tools', 'ፈጣን የፍለጋ መሳሪያዎች')}
                </h3>
                <p className="text-center text-gray-600">
                  {translate(
                    'Find exactly what you\'re looking for with our advanced filters and sorting options.',
                    'ከእኛ የላቁ ማጣሪያዎች እና ማመሳሰያ አማራጮች ጋር በትክክል የሚፈልጉትን ያግኙ።'
                  )}
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="bg-nibret-blue/10 rounded-full p-4 w-16 h-16 flex items-center justify-center mb-4 mx-auto">
                  <DollarSign className="h-8 w-8 text-nibret-blue" />
                </div>
                <h3 className="text-xl font-semibold text-center mb-2">
                  {translate('Financial Tools', 'የፋይናንስ መሳሪያዎች')}
                </h3>
                <p className="text-center text-gray-600">
                  {translate(
                    'Calculate mortgage options, compare prices and plan your investment with ease.',
                    'የብድር አማራጮችን ያስሉ, ዋጋዎችን ያነጻጽሩ እና በቀላሉ ኢንቨስትመንትዎን ያቅዱ።'
                  )}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        defaultTab="login"
      />
    </div>
  );
};

export default Index;
