import React from 'react';
import Navbar from '../components/Navbar';
import ExchangeRateBanner from '../components/ExchangeRateBanner';
import Map from '../components/Map';
import Footer from '../components/Footer';
import { useAppContext } from '../context/AppContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Star, Home, DollarSign } from 'lucide-react';

const MapDemo: React.FC = () => {
  const { translate } = useAppContext();

  // Sample properties for demo
  const sampleProperties = [
    {
      id: '1',
      lat: 8.9806,
      lng: 38.7578,
      price: 12500000,
      title: 'Luxury Villa in Bole',
      titleAmharic: 'በቦሌ ውስጥ የቅንጦት ቪላ',
      address: 'Bole, Addis Ababa, Ethiopia',
      addressAmharic: 'ቦሌ፣ አዲስ አበባ፣ ኢትዮጵያ',
      image: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=400&h=300&fit=crop'
    },
    {
      id: '2',
      lat: 9.0227,
      lng: 38.7468,
      price: 8500000,
      title: 'Modern Apartment in Kazanchis',
      titleAmharic: 'በካዛንቺስ ውስጥ ዘመናዊ አፓርትመንት',
      address: 'Kazanchis, Addis Ababa, Ethiopia',
      addressAmharic: 'ካዛንቺስ፣ አዲስ አበባ፣ ኢትዮጵያ',
      image: 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=400&h=300&fit=crop'
    },
    {
      id: '3',
      lat: 9.0157,
      lng: 38.7614,
      price: 6200000,
      title: 'Family House in Megenagna',
      titleAmharic: 'በመገናኛ ውስጥ የቤተሰብ ቤት',
      address: 'Megenagna, Addis Ababa, Ethiopia',
      addressAmharic: 'መገናኛ፣ አዲስ አበባ፣ ኢትዮጵያ',
      image: 'https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=400&h=300&fit=crop'
    },
    {
      id: '4',
      lat: 8.9950,
      lng: 38.7900,
      price: 15000000,
      title: 'Penthouse in CMC',
      titleAmharic: 'በሲኤምሲ ውስጥ ፔንትሃውስ',
      address: 'CMC, Addis Ababa, Ethiopia',
      addressAmharic: 'ሲኤምሲ፣ አዲስ አበባ፣ ኢትዮጵያ',
      image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=400&h=300&fit=crop'
    },
    {
      id: '5',
      lat: 8.9600,
      lng: 38.7200,
      price: 4500000,
      title: 'Cozy Studio in Piassa',
      titleAmharic: 'በፒያሳ ውስጥ ምቹ ስቱዲዮ',
      address: 'Piassa, Addis Ababa, Ethiopia',
      addressAmharic: 'ፒያሳ፣ አዲስ አበባ፣ ኢትዮጵያ',
      image: 'https://images.unsplash.com/photo-1600585154526-990dced4db0d?w=400&h=300&fit=crop'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      <ExchangeRateBanner />
      
      {/* Header */}
      <div className="bg-blue-600 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-blue-500 p-4 rounded-full">
                <MapPin className="w-8 h-8" />
              </div>
            </div>
            <h1 className="text-4xl font-bold mb-4">
              {translate('Interactive Property Map', 'በይነተገናኝ የንብረት ካርታ')}
            </h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              {translate(
                'Explore properties across Addis Ababa with our free, interactive map powered by OpenStreetMap',
                'በOpenStreetMap የሚንቀሳቀስ ነፃ፣ በይነተገናኝ ካርታችን በአዲስ አበባ ዙሪያ ያሉ ንብረቶችን ያስሱ'
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Map Demo Section */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Map */}
          <div className="lg:col-span-2">
            <Card className="h-96 sm:h-[500px] lg:h-[600px]">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="w-5 h-5 mr-2 text-blue-600" />
                  {translate('Live Property Map', 'የቀጥታ ንብረት ካርታ')}
                </CardTitle>
                <p className="text-gray-600">
                  {translate(
                    'Click on any marker to see property details. Zoom and pan to explore different areas.',
                    'የንብረት ዝርዝሮችን ለማየት በማንኛውም ምልክት ላይ ይጫኑ። የተለያዩ አካባቢዎችን ለማሰስ ያጉሉ እና ይንቀሳቀሱ።'
                  )}
                </p>
              </CardHeader>
              <CardContent className="h-[calc(100%-120px)] p-0">
                <Map 
                  properties={sampleProperties}
                  center={[8.9806, 38.7578]}
                  zoom={12}
                  className="rounded-b-lg"
                />
              </CardContent>
            </Card>
          </div>

          {/* Property List */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Home className="w-5 h-5 mr-2 text-blue-600" />
                  {translate('Featured Properties', 'ተመራጭ ንብረቶች')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {sampleProperties.map((property) => (
                  <div key={property.id} className="border rounded-lg p-3 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start space-x-3">
                      <img
                        src={property.image}
                        alt={property.title}
                        className="w-16 h-16 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate">
                          {translate(property.title, property.titleAmharic)}
                        </h4>
                        <p className="text-xs text-gray-500 truncate">
                          {translate(property.address, property.addressAmharic)}
                        </p>
                        <div className="flex items-center mt-1">
                          <DollarSign className="w-3 h-3 text-green-600 mr-1" />
                          <span className="text-sm font-semibold text-green-600">
                            {property.price.toLocaleString()} ETB
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Map Features */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Star className="w-5 h-5 mr-2 text-yellow-500" />
                  {translate('Map Features', 'የካርታ ባህሪያት')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">{translate('Free to use - no API keys required', 'ለመጠቀም ነፃ - ምንም API ቁልፎች አያስፈልጉም')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">{translate('Interactive property markers', 'በይነተገናኝ የንብረት ምልክቶች')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">{translate('Detailed property popups', 'ዝርዝር የንብረት ፖፕ-አፕስ')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">{translate('Zoom and pan controls', 'የማጉያ እና የመንቀሳቀሻ መቆጣጠሪያዎች')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">{translate('Mobile responsive design', 'ለሞባይል ምላሽ ሰጪ ዲዛይን')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">{translate('OpenStreetMap powered', 'በOpenStreetMap የሚንቀሳቀስ')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">{translate('Privacy-protected locations (150-200m radius)', 'ግላዊነት የተጠበቁ ቦታዎች (150-200 ሜትር ራዲየስ)')}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Technical Details */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>{translate('Technical Implementation', 'ቴክኒካል ትግበራ')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <span className="text-2xl">🗺️</span>
                  </div>
                  <h3 className="font-semibold mb-2">{translate('OpenStreetMap', 'OpenStreetMap')}</h3>
                  <p className="text-sm text-gray-600">
                    {translate('Free, open-source mapping platform with global coverage', 'ዓለም አቀፍ ሽፋን ያለው ነፃ፣ ክፍት ምንጭ የካርታ መድረክ')}
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <span className="text-2xl">🍃</span>
                  </div>
                  <h3 className="font-semibold mb-2">{translate('Leaflet.js', 'Leaflet.js')}</h3>
                  <p className="text-sm text-gray-600">
                    {translate('Lightweight, mobile-friendly interactive mapping library', 'ቀላል፣ ለሞባይል ተስማሚ በይነተገናኝ የካርታ ቤተ-መጽሐፍት')}
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-purple-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <span className="text-2xl">⚛️</span>
                  </div>
                  <h3 className="font-semibold mb-2">{translate('React Leaflet', 'React Leaflet')}</h3>
                  <p className="text-sm text-gray-600">
                    {translate('React components for Leaflet maps with full TypeScript support', 'ሙሉ TypeScript ድጋፍ ያላቸው ለLeaflet ካርታዎች React አካላት')}
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-orange-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <span className="text-2xl">🔒</span>
                  </div>
                  <h3 className="font-semibold mb-2">{translate('Privacy Protection', 'የግላዊነት ጥበቃ')}</h3>
                  <p className="text-sm text-gray-600">
                    {translate('Location obfuscation within 150-200m radius for security', 'ለደህንነት በ150-200 ሜትር ራዲየስ ውስጥ የቦታ መደበቅ')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default MapDemo;
