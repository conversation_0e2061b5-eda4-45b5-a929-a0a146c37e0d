import React, { useState, useEffect, useContext } from 'react';
import { useAppContext } from '../context/AppContext';
import { AuthContext } from '../App';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { 
  Gavel, 
  Clock, 
  DollarSign, 
  MapPin, 
  Calendar, 
  Users, 
  TrendingUp,
  Eye,
  Heart,
  Search,
  Filter,
  Loader2
} from 'lucide-react';

interface Auction {
  id: string;
  title: string;
  description: string;
  property: {
    id: string;
    title: string;
    address: string;
    images: string[];
    beds: number;
    baths: number;
    sqft: number;
  };
  startingBid: number;
  currentBid: number;
  bidCount: number;
  startDate: string;
  endDate: string;
  status: 'upcoming' | 'active' | 'ended';
  reservePrice?: number;
  auctioneer: string;
}

const Auctions = () => {
  const { translate, currency, convertCurrency } = useAppContext();
  const { isAuthenticated } = useContext(AuthContext);
  const { toast } = useToast();
  
  const [auctions, setAuctions] = useState<Auction[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('endDate');

  // Mock auction data for now
  useEffect(() => {
    const fetchAuctions = async () => {
      setLoading(true);
      
      // Simulate API call
      setTimeout(() => {
        const mockAuctions: Auction[] = [
          {
            id: '1',
            title: 'Luxury Villa Auction',
            description: 'Beautiful 4-bedroom villa in prime location',
            property: {
              id: 'prop1',
              title: 'Modern Villa with Garden',
              address: 'Bole, Addis Ababa',
              images: ['https://picsum.photos/id/1031/600/400'],
              beds: 4,
              baths: 3,
              sqft: 2800
            },
            startingBid: 500000,
            currentBid: 650000,
            bidCount: 12,
            startDate: '2024-01-15T10:00:00Z',
            endDate: '2024-01-20T18:00:00Z',
            status: 'active',
            reservePrice: 700000,
            auctioneer: 'Nibret Auctions'
          },
          {
            id: '2',
            title: 'Commercial Property Auction',
            description: 'Prime commercial space in city center',
            property: {
              id: 'prop2',
              title: 'Commercial Building',
              address: 'Piazza, Addis Ababa',
              images: ['https://picsum.photos/id/1040/600/400'],
              beds: 0,
              baths: 4,
              sqft: 5000
            },
            startingBid: 1200000,
            currentBid: 1200000,
            bidCount: 0,
            startDate: '2024-01-25T09:00:00Z',
            endDate: '2024-01-30T17:00:00Z',
            status: 'upcoming',
            auctioneer: 'Nibret Auctions'
          },
          {
            id: '3',
            title: 'Apartment Complex Auction',
            description: 'Investment opportunity - 8-unit apartment complex',
            property: {
              id: 'prop3',
              title: 'Apartment Complex',
              address: 'CMC, Addis Ababa',
              images: ['https://picsum.photos/id/1050/600/400'],
              beds: 24,
              baths: 16,
              sqft: 12000
            },
            startingBid: 2000000,
            currentBid: 2350000,
            bidCount: 8,
            startDate: '2024-01-01T10:00:00Z',
            endDate: '2024-01-10T18:00:00Z',
            status: 'ended',
            auctioneer: 'Nibret Auctions'
          }
        ];
        
        setAuctions(mockAuctions);
        setLoading(false);
      }, 1000);
    };

    fetchAuctions();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-600">Live Auction</Badge>;
      case 'upcoming':
        return <Badge className="bg-blue-600">Upcoming</Badge>;
      case 'ended':
        return <Badge className="bg-gray-600">Ended</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getTimeRemaining = (endDate: string) => {
    const now = new Date();
    const end = new Date(endDate);
    const diff = end.getTime() - now.getTime();
    
    if (diff <= 0) return 'Ended';
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const filteredAuctions = auctions.filter(auction => {
    const matchesSearch = searchTerm === '' ||
                         auction.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         auction.property.address.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || auction.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const sortedAuctions = [...filteredAuctions].sort((a, b) => {
    switch (sortBy) {
      case 'endDate':
        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();
      case 'currentBid':
        return b.currentBid - a.currentBid;
      case 'bidCount':
        return b.bidCount - a.bidCount;
      default:
        return 0;
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      
      <div className="container mx-auto py-12 px-4 md:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-nibret-blue mb-4 flex items-center justify-center">
            <Gavel className="mr-3 h-10 w-10" />
            {translate("Property Auctions", "የንብረት ጨረታዎች")}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {translate(
              "Discover exclusive property auctions and bid on your dream home or investment property",
              "ልዩ የንብረት ጨረታዎችን ያግኙ እና በህልም ቤትዎ ወይም የኢንቨስትመንት ንብረትዎ ላይ ጨረታ ያድርጉ"
            )}
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={translate("Search auctions...", "ጨረታዎችን ይፈልጉ...")}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 text-gray-900 placeholder:text-gray-500 bg-white"
                  />
                </div>
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Auctions</SelectItem>
                  <SelectItem value="active">Live Auctions</SelectItem>
                  <SelectItem value="upcoming">Upcoming</SelectItem>
                  <SelectItem value="ended">Ended</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="endDate">End Date</SelectItem>
                  <SelectItem value="currentBid">Current Bid</SelectItem>
                  <SelectItem value="bidCount">Bid Count</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Auction Tabs */}
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All ({auctions.length})</TabsTrigger>
            <TabsTrigger value="active">
              Live ({auctions.filter(a => a.status === 'active').length})
            </TabsTrigger>
            <TabsTrigger value="upcoming">
              Upcoming ({auctions.filter(a => a.status === 'upcoming').length})
            </TabsTrigger>
            <TabsTrigger value="ended">
              Ended ({auctions.filter(a => a.status === 'ended').length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-8">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-nibret-blue" />
                <span className="ml-2 text-gray-600">
                  {translate("Loading auctions...", "ጨረታዎችን በመጫን ላይ...")}
                </span>
              </div>
            ) : sortedAuctions.length === 0 ? (
              <div className="text-center py-12">
                <Gavel className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {translate("No auctions found", "ምንም ጨረታዎች አልተገኙም")}
                </h3>
                <p className="text-gray-600">
                  {translate("Try adjusting your search or filters", "ፍለጋዎን ወይም ማጣሪያዎችዎን ማስተካከል ይሞክሩ")}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {sortedAuctions.map((auction) => (
                  <Card key={auction.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative h-64">
                      <img
                        src={auction.property.images[0]}
                        alt={auction.property.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-4 left-4">
                        {getStatusBadge(auction.status)}
                      </div>
                      <div className="absolute top-4 right-4 flex gap-2">
                        <Button size="sm" variant="secondary">
                          <Heart className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="secondary">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                      {auction.status === 'active' && (
                        <div className="absolute bottom-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                          <Clock className="inline h-4 w-4 mr-1" />
                          {getTimeRemaining(auction.endDate)}
                        </div>
                      )}
                    </div>
                    
                    <CardContent className="p-6">
                      <h3 className="text-xl font-bold text-nibret-blue mb-2">
                        {auction.title}
                      </h3>
                      
                      <div className="flex items-center text-gray-600 mb-3">
                        <MapPin className="h-4 w-4 mr-1" />
                        {auction.property.address}
                      </div>
                      
                      <div className="grid grid-cols-3 gap-4 mb-4 text-sm text-gray-600">
                        <div>{auction.property.beds} beds</div>
                        <div>{auction.property.baths} baths</div>
                        <div>{auction.property.sqft.toLocaleString()} sqft</div>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Current Bid:</span>
                          <span className="text-lg font-bold text-green-600">
                            {currency === 'USD' ? '$' : 'BR'}{convertCurrency(auction.currentBid).toLocaleString()}
                          </span>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Bids:</span>
                          <span className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            {auction.bidCount}
                          </span>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Ends:</span>
                          <span className="text-sm">
                            {new Date(auction.endDate).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      
                      <div className="mt-6 flex gap-3">
                        {auction.status === 'active' ? (
                          <Button className="flex-1 bg-nibret-blue hover:bg-nibret-blue/90">
                            <Gavel className="mr-2 h-4 w-4" />
                            {translate("Place Bid", "ጨረታ ያድርጉ")}
                          </Button>
                        ) : auction.status === 'upcoming' ? (
                          <Button variant="outline" className="flex-1">
                            <Calendar className="mr-2 h-4 w-4" />
                            {translate("Set Reminder", "ማስታወሻ ያዘጋጁ")}
                          </Button>
                        ) : (
                          <Button variant="outline" className="flex-1" disabled>
                            {translate("Auction Ended", "ጨረታ ተጠናቋል")}
                          </Button>
                        )}
                        
                        <Button variant="outline">
                          {translate("View Details", "ዝርዝሮችን ይመልከቱ")}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
          
          {/* Other tab contents would filter the auctions by status */}
          <TabsContent value="active">
            {/* Same grid but filtered for active auctions */}
          </TabsContent>
          
          <TabsContent value="upcoming">
            {/* Same grid but filtered for upcoming auctions */}
          </TabsContent>
          
          <TabsContent value="ended">
            {/* Same grid but filtered for ended auctions */}
          </TabsContent>
        </Tabs>

        {/* Call to Action */}
        <Card className="mt-12 bg-nibret-blue text-white">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">
              {translate("Want to auction your property?", "ንብረትዎን ለጨረታ ማቅረብ ይፈልጋሉ?")}
            </h2>
            <p className="text-blue-100 mb-6">
              {translate(
                "Reach more buyers and get the best price for your property through our auction platform",
                "በጨረታ መድረካችን በኩል ብዙ ገዢዎችን ያግኙ እና ለንብረትዎ ምርጡን ዋጋ ያግኙ"
              )}
            </p>
            <Button variant="secondary" size="lg">
              <Gavel className="mr-2 h-5 w-5" />
              {translate("List for Auction", "ለጨረታ ዝርዝር")}
            </Button>
          </CardContent>
        </Card>
      </div>
      
      <Footer />
    </div>
  );
};

export default Auctions;
