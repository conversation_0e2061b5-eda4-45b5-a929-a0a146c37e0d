
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AppProvider } from "./context/AppContext";
import React, { createContext, useState } from "react";

import Index from "./pages/Index";
import Buy from "./pages/Buy";
import Rent from "./pages/Rent";
import Sell from "./pages/Sell";
import Loans from "./pages/Loans";
import NotFound from "./pages/NotFound";
import PropertyDetails from "./pages/PropertyDetails";
import Login from "./pages/Login";
import Register from "./pages/Register";
import AdminDashboard from "./pages/AdminDashboard";
import Analytics from "./pages/admin/Analytics";
import CRM from "./pages/admin/CRM";
// import LeadDetail from "./pages/admin/LeadDetail";
import Settings from "./pages/admin/Settings";
import UploadProperty from "./pages/UploadProperty";
import PropertyUpload from "./pages/PropertyUpload";
import EditProperty from "./pages/EditProperty";
import PropertyManagement from "./pages/PropertyManagement";
import AuctionUpload from "./pages/AuctionUpload";
import Auctions from "./pages/Auctions";
import CustomerDashboard from "./pages/CustomerDashboard";
import MapDemo from "./pages/MapDemo";
import AuthTest from "./pages/AuthTest";


const queryClient = new QueryClient();

// Auth context type
interface AuthContextType {
  isAuthenticated: boolean;
  currentUser: User | null;
  token: string | null;
  login: (user: User, token: string) => void;
  logout: () => void;
  setUser: (user: User | null) => void;
}

// Create and export the Auth context
export const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  currentUser: null,
  token: null,
  login: () => {},
  logout: () => {},
  setUser: () => {},
});

// Protected Route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = React.useContext(AuthContext);
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" />;
};

const App = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);

  // Check for stored authentication on app load and listen for storage changes
  React.useEffect(() => {
    const checkAuthStatus = () => {
      const storedToken = localStorage.getItem('auth_token');
      const storedUser = localStorage.getItem('user');

      if (storedToken && storedUser) {
        try {
          const parsedUser = JSON.parse(storedUser);
          setIsAuthenticated(true);
          setCurrentUser(parsedUser);
          setToken(storedToken);
        } catch (error) {
          console.error('Error parsing user data:', error);
          // Clear invalid data
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          setIsAuthenticated(false);
          setCurrentUser(null);
          setToken(null);
        }
      } else {
        setIsAuthenticated(false);
        setCurrentUser(null);
        setToken(null);
      }
    };

    // Check on mount
    checkAuthStatus();

    // Listen for storage changes (for cross-tab synchronization)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth_token' || e.key === 'user') {
        checkAuthStatus();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const login = (user: User, authToken: string) => {
    // Store authentication data
    localStorage.setItem('auth_token', authToken);
    localStorage.setItem('user', JSON.stringify(user));

    // Update state
    setIsAuthenticated(true);
    setCurrentUser(user);
    setToken(authToken);
  };

  const logout = () => {
    // Clear stored authentication data
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');

    // Update state
    setIsAuthenticated(false);
    setCurrentUser(null);
    setToken(null);
  };

  const setUser = (user: User | null) => {
    setCurrentUser(user);
    if (user) {
      localStorage.setItem('user', JSON.stringify(user));
    }
  };

  return (
    <QueryClientProvider client={queryClient}>
      <AppProvider>
        <AuthContext.Provider value={{ isAuthenticated, currentUser, token, login, logout, setUser }}>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/buy" element={<Buy />} />
                <Route path="/rent" element={<Rent />} />
                <Route path="/sell" element={<Sell />} />
                <Route path="/loans" element={<Loans />} />
                <Route path="/upload-property" element={<PropertyUpload />} />
                <Route path="/upload-property-old" element={<UploadProperty />} />
                <Route path="/my-properties" element={<PropertyManagement />} />
                <Route
                  path="/edit-property/:id"
                  element={
                    <ProtectedRoute>
                      <EditProperty />
                    </ProtectedRoute>
                  }
                />
                <Route path="/auction-upload" element={<AuctionUpload />} />
                <Route path="/auctions" element={<Auctions />} />
                <Route path="/dashboard" element={<CustomerDashboard />} />
                <Route path="/map-demo" element={<MapDemo />} />

                <Route path="/property/:id" element={<PropertyDetails />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route
                  path="/admin"
                  element={
                    <ProtectedRoute>
                      <AdminDashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin/analytics"
                  element={
                    <ProtectedRoute>
                      <Analytics />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin/crm"
                  element={
                    <ProtectedRoute>
                      <CRM />
                    </ProtectedRoute>
                  }
                />
                {/* <Route
                  path="/admin/crm/leads/:id"
                  element={
                    <ProtectedRoute>
                      <LeadDetail />
                    </ProtectedRoute>
                  }
                /> */}
                <Route
                  path="/admin/settings"
                  element={
                    <ProtectedRoute>
                      <Settings />
                    </ProtectedRoute>
                  }
                />
                <Route path="/auth-test" element={<AuthTest />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </AuthContext.Provider>
      </AppProvider>
    </QueryClientProvider>
  );
};

export default App;
