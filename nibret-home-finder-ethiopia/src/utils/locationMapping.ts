// Ethiopian Location Mapping with Accurate Coordinates
// Coordinates are in [latitude, longitude] format for consistency

export interface LocationCoordinates {
  lat: number;
  lng: number;
  name: string;
  nameAmharic?: string;
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
}

// Major Ethiopian locations with accurate coordinates
export const ETHIOPIAN_LOCATIONS: Record<string, LocationCoordinates> = {
  // Addis Ababa Sub-cities and Areas
  'bole': {
    lat: 8.9806,
    lng: 38.7578,
    name: '<PERSON><PERSON>',
    nameAmharic: 'ቦሌ',
    bounds: {
      north: 9.0200,
      south: 8.9400,
      east: 38.8000,
      west: 38.7200
    }
  },
  'bole atlas': {
    lat: 8.9865,
    lng: 38.7924,
    name: 'Bole Atlas',
    nameAmharic: 'ቦሌ አትላስ'
  },
  'bole medhanialem': {
    lat: 8.9956,
    lng: 38.7742,
    name: '<PERSON><PERSON> Medhanialem',
    nameAmharic: 'ቦሌ መድሃኒዓለም'
  },
  'kirkos': {
    lat: 9.0320,
    lng: 38.7469,
    name: '<PERSON><PERSON>',
    nameAmharic: 'ቂርቆስ',
    bounds: {
      north: 9.0500,
      south: 9.0100,
      east: 38.7700,
      west: 38.7200
    }
  },
  'yeka': {
    lat: 9.0500,
    lng: 38.7800,
    name: 'Yeka',
    nameAmharic: 'የካ',
    bounds: {
      north: 9.0800,
      south: 9.0200,
      east: 38.8200,
      west: 38.7400
    }
  },
  'arada': {
    lat: 9.0350,
    lng: 38.7400,
    name: 'Arada',
    nameAmharic: 'አራዳ',
    bounds: {
      north: 9.0500,
      south: 9.0200,
      east: 38.7600,
      west: 38.7200
    }
  },
  'lideta': {
    lat: 9.0200,
    lng: 38.7300,
    name: 'Lideta',
    nameAmharic: 'ልደታ',
    bounds: {
      north: 9.0400,
      south: 9.0000,
      east: 38.7500,
      west: 38.7100
    }
  },
  'kolfe keranio': {
    lat: 8.9800,
    lng: 38.7000,
    name: 'Kolfe Keranio',
    nameAmharic: 'ኮልፌ ቀራኒዮ',
    bounds: {
      north: 9.0000,
      south: 8.9600,
      east: 38.7300,
      west: 38.6700
    }
  },
  'gulele': {
    lat: 9.0600,
    lng: 38.7200,
    name: 'Gulele',
    nameAmharic: 'ጉለሌ',
    bounds: {
      north: 9.0800,
      south: 9.0400,
      east: 38.7500,
      west: 38.6900
    }
  },
  'nifas silk-lafto': {
    lat: 8.9500,
    lng: 38.7600,
    name: 'Nifas Silk-Lafto',
    nameAmharic: 'ንፋስ ስልክ ላፍቶ',
    bounds: {
      north: 8.9700,
      south: 8.9300,
      east: 38.7800,
      west: 38.7400
    }
  },
  'akaky kaliti': {
    lat: 8.8800,
    lng: 38.7400,
    name: 'Akaky Kaliti',
    nameAmharic: 'አቃቂ ካሊቲ',
    bounds: {
      north: 8.9000,
      south: 8.8600,
      east: 38.7600,
      west: 38.7200
    }
  },
  'addis ketema': {
    lat: 9.0400,
    lng: 38.7350,
    name: 'Addis Ketema',
    nameAmharic: 'አዲስ ከተማ',
    bounds: {
      north: 9.0600,
      south: 9.0200,
      east: 38.7550,
      west: 38.7150
    }
  },

  // Popular Areas and Landmarks
  'piassa': {
    lat: 9.0320,
    lng: 38.7469,
    name: 'Piassa',
    nameAmharic: 'ፒያሳ'
  },
  'kazanchis': {
    lat: 9.0227,
    lng: 38.7468,
    name: 'Kazanchis',
    nameAmharic: 'ካዛንቺስ'
  },
  'cmc': {
    lat: 9.0100,
    lng: 38.7600,
    name: 'CMC',
    nameAmharic: 'ሲ.ኤም.ሲ'
  },
  'megenagna': {
    lat: 9.0150,
    lng: 38.7850,
    name: 'Megenagna',
    nameAmharic: 'መገናኛ'
  },
  'mexico': {
    lat: 9.0050,
    lng: 38.7650,
    name: 'Mexico',
    nameAmharic: 'ሜክሲኮ'
  },
  'sarbet': {
    lat: 9.0400,
    lng: 38.7600,
    name: 'Sarbet',
    nameAmharic: 'ሳርቤት'
  },
  'gerji': {
    lat: 9.0650,
    lng: 38.7950,
    name: 'Gerji',
    nameAmharic: 'ገርጂ'
  },
  'hayat': {
    lat: 8.9750,
    lng: 38.7450,
    name: 'Hayat',
    nameAmharic: 'ሃያት'
  },

  // Default Addis Ababa center
  'addis ababa': {
    lat: 9.0320,
    lng: 38.7469,
    name: 'Addis Ababa',
    nameAmharic: 'አዲስ አበባ',
    bounds: {
      north: 9.1000,
      south: 8.8500,
      east: 38.8500,
      west: 38.6500
    }
  },

  // Other major Ethiopian cities
  'bahir dar': {
    lat: 11.5942,
    lng: 37.3906,
    name: 'Bahir Dar',
    nameAmharic: 'ባሕር ዳር'
  },
  'dire dawa': {
    lat: 9.5931,
    lng: 41.8661,
    name: 'Dire Dawa',
    nameAmharic: 'ድሬ ዳዋ'
  },
  'mekelle': {
    lat: 13.4967,
    lng: 39.4753,
    name: 'Mekelle',
    nameAmharic: 'መቀሌ'
  },
  'hawassa': {
    lat: 7.0621,
    lng: 38.4776,
    name: 'Hawassa',
    nameAmharic: 'ሐዋሳ'
  },
  'adama': {
    lat: 8.5400,
    lng: 39.2675,
    name: 'Adama',
    nameAmharic: 'አዳማ'
  },
  'jimma': {
    lat: 7.6667,
    lng: 36.8333,
    name: 'Jimma',
    nameAmharic: 'ጅማ'
  }
};

/**
 * Get coordinates for a location by name
 * Supports fuzzy matching and multiple name formats
 */
export function getLocationCoordinates(locationName: string): LocationCoordinates | null {
  if (!locationName) return null;

  const normalizedName = locationName.toLowerCase().trim();
  
  // Direct match
  if (ETHIOPIAN_LOCATIONS[normalizedName]) {
    return ETHIOPIAN_LOCATIONS[normalizedName];
  }

  // Fuzzy matching for common variations
  const fuzzyMatches: Record<string, string> = {
    'bole atlas': 'bole atlas',
    'bole medhanialem': 'bole medhanialem',
    'bole medhane alem': 'bole medhanialem',
    'atlas': 'bole atlas',
    'medhanialem': 'bole medhanialem',
    'medhane alem': 'bole medhanialem'
  };

  if (fuzzyMatches[normalizedName]) {
    return ETHIOPIAN_LOCATIONS[fuzzyMatches[normalizedName]];
  }

  // Check if location name contains a known location
  for (const [key, location] of Object.entries(ETHIOPIAN_LOCATIONS)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return location;
    }
  }

  return null;
}

/**
 * Generate random coordinates within a location's bounds
 * Used for privacy while keeping markers in the correct area
 */
export function getRandomCoordinatesInArea(location: LocationCoordinates, propertyId: string): [number, number] {
  // Use property ID as seed for consistent positioning
  const seed = propertyId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  
  const random = (seed: number) => {
    const x = Math.sin(seed) * 10000;
    return x - Math.floor(x);
  };

  if (location.bounds) {
    // Generate coordinates within the defined bounds
    const lat = location.bounds.south + random(seed) * (location.bounds.north - location.bounds.south);
    const lng = location.bounds.west + random(seed + 1) * (location.bounds.east - location.bounds.west);
    return [lat, lng];
  } else {
    // Generate coordinates within a small radius of the center point
    const radius = 0.005; // Approximately 500 meters
    const angle = random(seed) * 2 * Math.PI;
    const distance = random(seed + 1) * radius;
    
    const lat = location.lat + distance * Math.cos(angle);
    const lng = location.lng + distance * Math.sin(angle);
    return [lat, lng];
  }
}
