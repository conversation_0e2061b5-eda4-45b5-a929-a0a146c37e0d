// Test file for Fixer.io API integration
import { fixerAPI, fixerCurrencyAPI } from '../lib/api/fixer';

// Mock environment variable for testing
const originalEnv = import.meta.env.VITE_FIXER_API_KEY;

describe('Fixer.io API Integration', () => {
  beforeAll(() => {
    // Set a test API key (you should replace this with a real test key)
    (import.meta.env as any).VITE_FIXER_API_KEY = 'test-api-key';
  });

  afterAll(() => {
    // Restore original environment
    (import.meta.env as any).VITE_FIXER_API_KEY = originalEnv;
  });

  describe('Fixer.io Service', () => {
    test('should initialize without errors', () => {
      expect(fixerAPI).toBeDefined();
      expect(fixerCurrencyAPI).toBeDefined();
    });

    test('should have correct cache methods', () => {
      expect(typeof fixerAPI.clearCache).toBe('function');
      expect(typeof fixerAPI.getCacheStats).toBe('function');
    });

    test('should return cache stats', () => {
      const stats = fixerAPI.getCacheStats();
      expect(stats).toHaveProperty('size');
      expect(stats).toHaveProperty('keys');
      expect(Array.isArray(stats.keys)).toBe(true);
    });

    test('should clear cache properly', () => {
      fixerAPI.clearCache();
      const stats = fixerAPI.getCacheStats();
      expect(stats.size).toBe(0);
      expect(stats.keys.length).toBe(0);
    });
  });

  describe('Currency API Compatibility', () => {
    test('should have backward compatible methods', () => {
      expect(typeof fixerCurrencyAPI.getExchangeRate).toBe('function');
      expect(typeof fixerCurrencyAPI.convertUSDToETB).toBe('function');
      expect(typeof fixerCurrencyAPI.convertETBToUSD).toBe('function');
      expect(typeof fixerCurrencyAPI.checkHealth).toBe('function');
      expect(typeof fixerCurrencyAPI.clearCache).toBe('function');
    });
  });

  describe('Health Check', () => {
    test('should return health status object', async () => {
      const health = await fixerAPI.healthCheck();
      expect(health).toHaveProperty('available');
      expect(typeof health.available).toBe('boolean');

      if (!health.available) {
        expect(health).toHaveProperty('error');
      } else {
        expect(health).toHaveProperty('responseTime');
      }
    });
  });

  describe('API Key Validation', () => {
    test('should detect unconfigured API key', () => {
      (import.meta.env as any).VITE_FIXER_API_KEY = 'your_fixer_api_key_here';

      // This should not throw, but should handle gracefully
      expect(() => {
        const service = new (fixerAPI.constructor as any)();
      }).not.toThrow();
    });

    test('should detect missing API key', () => {
      (import.meta.env as any).VITE_FIXER_API_KEY = undefined;

      // This should not throw, but should handle gracefully
      expect(() => {
        const service = new (fixerAPI.constructor as any)();
      }).not.toThrow();
    });
  });
});

// Integration test with actual API (only run if API key is configured)
describe('Fixer.io API Integration (Live)', () => {
  const isApiConfigured = originalEnv && originalEnv !== 'your_fixer_api_key_here';

  // Skip these tests if API key is not configured
  const testIf = isApiConfigured ? test : test.skip;

  testIf('should fetch real exchange rate', async () => {
    try {
      const rate = await fixerAPI.getExchangeRate('USD', 'ETB');
      expect(rate).toHaveProperty('rate');
      expect(rate).toHaveProperty('lastUpdated');
      expect(rate).toHaveProperty('source');
      expect(rate).toHaveProperty('responseTime');
      expect(typeof rate.rate).toBe('number');
      expect(rate.rate).toBeGreaterThan(0);
      expect(rate.source).toBe('Fixer.io API');
    } catch (error) {
      // If API fails, that's also a valid test result
      console.warn('Fixer.io API test failed (this may be expected):', error);
    }
  }, 10000); // 10 second timeout for API calls

  testIf('should convert currency amounts', async () => {
    try {
      const result = await fixerAPI.convertCurrency(100, 'USD', 'ETB');
      expect(result).toHaveProperty('convertedAmount');
      expect(result).toHaveProperty('rate');
      expect(result).toHaveProperty('info');
      expect(typeof result.convertedAmount).toBe('number');
      expect(result.convertedAmount).toBeGreaterThan(0);
    } catch (error) {
      console.warn('Fixer.io conversion test failed (this may be expected):', error);
    }
  }, 10000);

  testIf('should fetch multiple rates', async () => {
    try {
      const rates = await fixerAPI.getMultipleRates('USD', ['ETB', 'EUR', 'GBP']);
      expect(typeof rates).toBe('object');
      // At least one rate should be returned
      expect(Object.keys(rates).length).toBeGreaterThan(0);
    } catch (error) {
      console.warn('Fixer.io multi-rate test failed (this may be expected):', error);
    }
  }, 10000);
});

// Performance tests
describe('Fixer.io Performance', () => {
  test('should cache results properly', async () => {
    // Clear cache first
    fixerAPI.clearCache();

    const startStats = fixerAPI.getCacheStats();
    expect(startStats.size).toBe(0);

    // This test doesn't make actual API calls, just tests caching logic
    // In a real scenario, after a successful API call, cache should be populated
  });

  test('should handle concurrent requests', async () => {
    // Test that multiple simultaneous requests don't cause issues
    const promises = Array(5).fill(null).map(() =>
      fixerAPI.healthCheck()
    );

    const results = await Promise.allSettled(promises);
    expect(results.length).toBe(5);

    // All should either succeed or fail gracefully
    results.forEach(result => {
      if (result.status === 'fulfilled') {
        expect(result.value).toHaveProperty('available');
      }
    });
  });
});

export {};
