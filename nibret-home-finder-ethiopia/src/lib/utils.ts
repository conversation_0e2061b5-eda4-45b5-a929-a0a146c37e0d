import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
export const API_TIMEOUT = parseInt(import.meta.env.VITE_API_TIMEOUT || '10000')
export const NODE_ENV = import.meta.env.VITE_NODE_ENV || 'development'

// Log API configuration in development
if (NODE_ENV === 'development') {
  console.log('🔗 API Configuration:', {
    baseUrl: API_BASE_URL,
    timeout: API_TIMEOUT,
    environment: NODE_ENV
  })
}

// Retry utility function
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const apiRequestWithRetry = async (
  endpoint: string,
  options: RequestInit = {},
  maxRetries: number = 5
): Promise<any> => {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 API Request attempt ${attempt}/${maxRetries} for ${endpoint}`)

      const result = await apiRequest(endpoint, options)

      // If successful, return the result
      console.log(`✅ API Request successful on attempt ${attempt} for ${endpoint}`)
      return result

    } catch (error) {
      lastError = error as Error
      console.warn(`❌ API Request failed on attempt ${attempt}/${maxRetries} for ${endpoint}:`, error.message)

      // If this is the last attempt, don't wait
      if (attempt === maxRetries) {
        break
      }

      // Calculate delay: exponential backoff with jitter
      // 1st retry: 1s, 2nd: 2s, 3rd: 4s, 4th: 8s, 5th: 16s
      const baseDelay = Math.pow(2, attempt - 1) * 1000
      const jitter = Math.random() * 500 // Add up to 500ms random jitter
      const delayTime = baseDelay + jitter

      console.log(`⏳ Waiting ${Math.round(delayTime)}ms before retry ${attempt + 1}...`)
      await delay(delayTime)
    }
  }

  console.error(`💥 All ${maxRetries} API request attempts failed for ${endpoint}`)
  throw lastError
}

// API utility functions
export const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const url = `${API_BASE_URL}${endpoint}`

  const defaultHeaders: Record<string, string> = {}

  // Don't set Content-Type for FormData - let browser set it with boundary
  if (!(options.body instanceof FormData)) {
    defaultHeaders['Content-Type'] = 'application/json'
  }

  // Get token from localStorage if it exists
  const token = localStorage.getItem('auth_token')
  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`
    console.log('🔑 apiRequest - Adding Authorization header with token:', token.substring(0, 20) + '...')
  } else {
    console.log('⚠️ apiRequest - No token found in localStorage')
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  }

  console.log('📡 apiRequest - Making request:', {
    url,
    method: config.method || 'GET',
    hasAuthHeader: !!defaultHeaders['Authorization'],
    headers: Object.keys(config.headers || {})
  })

  try {
    // Create timeout promise
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Request timeout')), API_TIMEOUT)
    )

    // Race between fetch and timeout
    const response = await Promise.race([
      fetch(url, config),
      timeoutPromise
    ]) as Response

    console.log('📨 apiRequest - Response received:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    })

    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`

      // Try to get error details from response
      try {
        const errorData = await response.json()
        console.log('❌ apiRequest - Error response data:', errorData)
        if (errorData.error) {
          errorMessage = errorData.error
        } else if (errorData.message) {
          errorMessage = errorData.message
        }
      } catch {
        // If we can't parse the error response, use the default message
      }

      console.error('❌ apiRequest - Request failed:', {
        url,
        status: response.status,
        statusText: response.statusText,
        errorMessage
      })

      throw new Error(errorMessage)
    }

    const data = await response.json()
    console.log('✅ apiRequest - Success:', { url, dataKeys: Object.keys(data || {}) })
    return data
  } catch (error) {
    console.error('💥 apiRequest - Exception caught:', {
      url,
      error: error.message,
      endpoint,
      stack: error.stack
    })
    throw error
  }
}

// Auth utilities
export const setAuthToken = (token: string) => {
  localStorage.setItem('auth_token', token)
}

export const getAuthToken = () => {
  return localStorage.getItem('auth_token')
}

export const removeAuthToken = () => {
  localStorage.removeItem('auth_token')
}
