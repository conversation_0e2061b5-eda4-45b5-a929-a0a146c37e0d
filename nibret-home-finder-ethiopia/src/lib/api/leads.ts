import { apiRequest } from '../utils'

export interface Lead {
  _id: string
  first_name: string
  last_name: string
  email: string
  phone: string
  status: 'new' | 'contacted' | 'qualified' | 'lost' | 'converted'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  source: 'website' | 'phone_call' | 'email' | 'referral' | 'social_media' | 'advertisement' | 'walk_in' | 'other'
  interested_property?: {
    _id: string
    title: string
    address: string
    price: number
    images: string[]
  }
  property_preferences?: {
    budget_min?: number
    budget_max?: number
    property_type?: string[]
    bedrooms?: number
    bathrooms?: number
    location_preferences?: string[]
    special_requirements?: string
  }
  interactions: Interaction[]
  assigned_to?: {
    _id: string
    first_name: string
    last_name: string
    email: string
  }
  conversion_value: number
  tags: string[]
  notes?: string
  last_contact_date?: string
  next_follow_up?: string
  converted_date?: string
  utm_source?: string
  utm_medium?: string
  utm_campaign?: string
  referrer_url?: string
  ip_address?: string
  user_agent?: string
  created_at: string
  updated_at: string
}

export interface Interaction {
  _id: string
  type: 'call' | 'email' | 'meeting' | 'property_viewing' | 'follow_up' | 'note'
  date: string
  description: string
  outcome?: 'positive' | 'neutral' | 'negative' | 'no_response'
  next_action?: string
  next_action_date?: string
  created_by: {
    _id: string
    first_name: string
    last_name: string
  }
}

export interface LeadCreate {
  first_name: string
  last_name: string
  email: string
  phone: string
  source?: string
  interested_property?: string
  property_preferences?: {
    budget_min?: number
    budget_max?: number
    property_type?: string[]
    bedrooms?: number
    bathrooms?: number
    location_preferences?: string[]
    special_requirements?: string
  }
  notes?: string
  utm_source?: string
  utm_medium?: string
  utm_campaign?: string
}

export interface InteractionCreate {
  type: 'call' | 'email' | 'meeting' | 'property_viewing' | 'follow_up' | 'note'
  description: string
  outcome?: 'positive' | 'neutral' | 'negative' | 'no_response'
  next_action?: string
  next_action_date?: string
}

export interface LeadFilters {
  status?: string
  priority?: string
  source?: string
  assigned_to?: string
  page?: number
  limit?: number
  search?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface LeadStats {
  data: {
    stats: {
      total: number
      new: number
      contacted: number
      qualified: number
      lost: number
      converted: number
      conversion_rate: string
    }
    conversion_funnel?: Array<{
      stage: string
      count: number
    }>
    leads_by_source?: Array<{
      _id: string
      count: number
    }>
    upcoming_follow_ups?: Lead[]
    overdue_follow_ups?: Lead[]
  }
}

export const leadApi = {
  // Get all leads with filtering
  getLeads: async (filters?: LeadFilters): Promise<{ data: Lead[]; pagination: any }> => {
    const queryParams = new URLSearchParams()
    if (filters?.status) queryParams.append('status', filters.status)
    if (filters?.priority) queryParams.append('priority', filters.priority)
    if (filters?.source) queryParams.append('source', filters.source)
    if (filters?.assigned_to) queryParams.append('assigned_to', filters.assigned_to)
    if (filters?.page) queryParams.append('page', filters.page.toString())
    if (filters?.limit) queryParams.append('limit', filters.limit.toString())
    if (filters?.search) queryParams.append('search', filters.search)
    if (filters?.sort_by) queryParams.append('sort_by', filters.sort_by)
    if (filters?.sort_order) queryParams.append('sort_order', filters.sort_order)

    return apiRequest(`/leads?${queryParams.toString()}`)
  },

  // Get lead statistics
  getLeadStats: async (): Promise<LeadStats> => {
    return apiRequest('/leads/stats')
  },

  // Get single lead with interaction history
  getLead: async (id: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}`)
    return response.data
  },

  // Create new lead
  createLead: async (lead: LeadCreate): Promise<Lead> => {
    const response = await apiRequest('/leads', {
      method: 'POST',
      body: JSON.stringify(lead),
    })
    return response.data
  },

  // Update lead
  updateLead: async (id: string, lead: Partial<LeadCreate>): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}`, {
      method: 'PUT',
      body: JSON.stringify(lead),
    })
    return response.data
  },

  // Update lead status
  updateLeadStatus: async (id: string, status: string, notes?: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status, notes }),
    })
    return response.data
  },

  // Add interaction to lead
  addInteraction: async (id: string, interaction: InteractionCreate): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/interactions`, {
      method: 'POST',
      body: JSON.stringify(interaction),
    })
    return response.data
  },

  // Schedule follow-up
  scheduleFollowUp: async (id: string, date: string, description: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/follow-up`, {
      method: 'POST',
      body: JSON.stringify({ date, description }),
    })
    return response.data
  },

  // Delete lead
  deleteLead: async (id: string): Promise<void> => {
    return apiRequest(`/leads/${id}`, {
      method: 'DELETE',
    })
  },

  // Quick actions
  markAsContacted: async (id: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status: 'contacted', notes: 'Lead contacted' }),
    })
    return response.data
  },

  markAsQualified: async (id: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status: 'qualified', notes: 'Lead qualified' }),
    })
    return response.data
  },

  markAsLost: async (id: string, reason?: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status: 'closed_lost', notes: reason || 'Lead lost' }),
    })
    return response.data
  },

  markAsWon: async (id: string, value?: number): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({
        status: 'closed_won',
        notes: 'Lead converted to customer',
        conversion_value: value
      }),
    })
    return response.data
  },

  // Log phone call
  logCall: async (id: string, description: string, outcome?: string, nextAction?: string, nextActionDate?: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/interactions`, {
      method: 'POST',
      body: JSON.stringify({
        type: 'call',
        description,
        outcome: outcome as any,
        next_action: nextAction,
        next_action_date: nextActionDate
      }),
    })
    return response.data
  },

  // Log email
  logEmail: async (id: string, description: string, outcome?: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/interactions`, {
      method: 'POST',
      body: JSON.stringify({
        type: 'email',
        description,
        outcome: outcome as any
      }),
    })
    return response.data
  },

  // Log meeting
  logMeeting: async (id: string, description: string, outcome?: string, nextAction?: string, nextActionDate?: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/interactions`, {
      method: 'POST',
      body: JSON.stringify({
        type: 'meeting',
        description,
        outcome: outcome as any,
        next_action: nextAction,
        next_action_date: nextActionDate
      }),
    })
    return response.data
  },

  // Log property viewing
  logPropertyViewing: async (id: string, description: string, outcome?: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/interactions`, {
      method: 'POST',
      body: JSON.stringify({
        type: 'property_viewing',
        description,
        outcome: outcome as any
      }),
    })
    return response.data
  },

  // Add note
  addNote: async (id: string, description: string): Promise<Lead> => {
    const response = await apiRequest(`/leads/${id}/interactions`, {
      method: 'POST',
      body: JSON.stringify({
        type: 'note',
        description
      }),
    })
    return response.data
  }
}
