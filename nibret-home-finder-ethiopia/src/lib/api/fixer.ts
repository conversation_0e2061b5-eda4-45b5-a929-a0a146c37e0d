// Fixer.io API service for currency exchange rates
// Documentation: https://fixer.io/documentation

export interface FixerLatestResponse {
  success: boolean;
  timestamp: number;
  base: string;
  date: string;
  rates: {
    [key: string]: number;
  };
}

export interface FixerConvertResponse {
  success: boolean;
  query: {
    from: string;
    to: string;
    amount: number;
  };
  info: {
    timestamp: number;
    rate: number;
  };
  historical?: boolean;
  date: string;
  result: number;
}

export interface FixerSymbolsResponse {
  success: boolean;
  symbols: {
    [key: string]: string;
  };
}

export interface ExchangeRateInfo {
  rate: number;
  lastUpdated: string;
  source: string;
  responseTime: number;
  timestamp?: number;
}

class FixerService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://data.fixer.io/api';
  private cache: Map<string, { data: ExchangeRateInfo; timestamp: number }> = new Map();
  private readonly cacheTimeout = 30 * 60 * 1000; // 30 minutes

  constructor() {
    this.apiKey = import.meta.env.VITE_FIXER_API_KEY;
    if (!this.apiKey || this.apiKey === 'your_fixer_api_key_here') {
      console.warn('⚠️ Fixer.io API key not configured. Please set VITE_FIXER_API_KEY in your .env file');
    }
  }

  private isApiKeyConfigured(): boolean {
    return !!(this.apiKey && this.apiKey !== 'your_fixer_api_key_here');
  }

  private getCacheKey(from: string, to: string): string {
    return `${from}-${to}`;
  }

  private isValidCacheEntry(timestamp: number): boolean {
    return Date.now() - timestamp < this.cacheTimeout;
  }

  private async makeRequest<T>(endpoint: string, params: Record<string, string> = {}): Promise<T> {
    if (!this.isApiKeyConfigured()) {
      throw new Error('Fixer.io API key not configured');
    }

    const url = new URL(`${this.baseUrl}/${endpoint}`);
    url.searchParams.append('access_key', this.apiKey);
    
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });

    console.log(`🔄 Fixer.io API request: ${endpoint}`);
    const startTime = Date.now();

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Nibret-Real-Estate/1.0'
        }
      });

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        throw new Error(`Fixer.io API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // Check if API returned an error
      if (!data.success) {
        const errorMsg = data.error ? `${data.error.code}: ${data.error.info}` : 'Unknown API error';
        throw new Error(`Fixer.io API error: ${errorMsg}`);
      }

      console.log(`✅ Fixer.io API response received in ${responseTime}ms`);
      
      return data;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.error(`❌ Fixer.io API request failed after ${responseTime}ms:`, error);
      throw error;
    }
  }

  /**
   * Get exchange rate from one currency to another
   */
  async getExchangeRate(from: string = 'USD', to: string = 'ETB'): Promise<ExchangeRateInfo> {
    const cacheKey = this.getCacheKey(from, to);
    const cached = this.cache.get(cacheKey);

    // Return cached data if valid
    if (cached && this.isValidCacheEntry(cached.timestamp)) {
      console.log(`💰 Using cached Fixer.io rate: ${from}/${to} = ${cached.data.rate}`);
      return cached.data;
    }

    try {
      const startTime = Date.now();
      const response = await this.makeRequest<FixerLatestResponse>('latest', {
        base: from,
        symbols: to
      });

      const responseTime = Date.now() - startTime;
      const rate = response.rates[to];

      if (!rate || rate <= 0) {
        throw new Error(`Invalid exchange rate received: ${rate}`);
      }

      const exchangeRateInfo: ExchangeRateInfo = {
        rate,
        lastUpdated: response.date,
        source: 'Fixer.io API',
        responseTime,
        timestamp: response.timestamp
      };

      // Cache the result
      this.cache.set(cacheKey, {
        data: exchangeRateInfo,
        timestamp: Date.now()
      });

      console.log(`✅ Fixer.io rate: ${from}/${to} = ${rate} (${responseTime}ms)`);
      return exchangeRateInfo;

    } catch (error) {
      console.error(`💥 Fixer.io getExchangeRate failed:`, error);
      
      // Return stale cache if available
      if (cached) {
        console.log(`⚠️ Using stale cached rate due to API error`);
        return cached.data;
      }
      
      throw error;
    }
  }

  /**
   * Convert amount from one currency to another
   */
  async convertCurrency(
    amount: number, 
    from: string = 'USD', 
    to: string = 'ETB'
  ): Promise<{ convertedAmount: number; rate: number; info: ExchangeRateInfo }> {
    try {
      const startTime = Date.now();
      const response = await this.makeRequest<FixerConvertResponse>('convert', {
        from,
        to,
        amount: amount.toString()
      });

      const responseTime = Date.now() - startTime;
      const convertedAmount = response.result;
      const rate = response.info.rate;

      const info: ExchangeRateInfo = {
        rate,
        lastUpdated: response.date,
        source: 'Fixer.io API (Convert)',
        responseTime,
        timestamp: response.info.timestamp
      };

      console.log(`💱 Fixer.io convert: ${amount} ${from} = ${convertedAmount} ${to}`);

      return {
        convertedAmount,
        rate,
        info
      };

    } catch (error) {
      console.error(`💥 Fixer.io convertCurrency failed:`, error);
      
      // Fallback to rate-based conversion
      const rateInfo = await this.getExchangeRate(from, to);
      const convertedAmount = amount * rateInfo.rate;

      return {
        convertedAmount,
        rate: rateInfo.rate,
        info: rateInfo
      };
    }
  }

  /**
   * Get multiple exchange rates at once
   */
  async getMultipleRates(from: string, currencies: string[]): Promise<Record<string, ExchangeRateInfo>> {
    try {
      const startTime = Date.now();
      const response = await this.makeRequest<FixerLatestResponse>('latest', {
        base: from,
        symbols: currencies.join(',')
      });

      const responseTime = Date.now() - startTime;
      const results: Record<string, ExchangeRateInfo> = {};

      currencies.forEach(currency => {
        const rate = response.rates[currency];
        if (rate && rate > 0) {
          results[currency] = {
            rate,
            lastUpdated: response.date,
            source: 'Fixer.io API (Multi)',
            responseTime,
            timestamp: response.timestamp
          };

          // Cache individual rates
          const cacheKey = this.getCacheKey(from, currency);
          this.cache.set(cacheKey, {
            data: results[currency],
            timestamp: Date.now()
          });
        }
      });

      console.log(`✅ Fixer.io multi-rate fetch: ${Object.keys(results).length} rates`);
      return results;

    } catch (error) {
      console.error(`💥 Fixer.io getMultipleRates failed:`, error);
      throw error;
    }
  }

  /**
   * Get all supported currencies
   */
  async getSupportedCurrencies(): Promise<Record<string, string>> {
    try {
      const response = await this.makeRequest<FixerSymbolsResponse>('symbols');
      console.log(`✅ Fixer.io symbols: ${Object.keys(response.symbols).length} currencies`);
      return response.symbols;
    } catch (error) {
      console.error(`💥 Fixer.io getSupportedCurrencies failed:`, error);
      throw error;
    }
  }

  /**
   * Check if Fixer.io API is available and working
   */
  async healthCheck(): Promise<{ available: boolean; responseTime?: number; error?: string }> {
    if (!this.isApiKeyConfigured()) {
      return {
        available: false,
        error: 'API key not configured'
      };
    }

    try {
      const startTime = Date.now();
      await this.getExchangeRate('USD', 'EUR'); // Simple test conversion
      const responseTime = Date.now() - startTime;

      return {
        available: true,
        responseTime
      };
    } catch (error) {
      return {
        available: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Clear all cached rates
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🗑️ Fixer.io cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// Export singleton instance
export const fixerAPI = new FixerService();

// Export for backward compatibility and easy integration
export const fixerCurrencyAPI = {
  // Get USD to ETB exchange rate (compatible with existing currency API)
  getExchangeRate: async (): Promise<ExchangeRateInfo> => {
    return fixerAPI.getExchangeRate('USD', 'ETB');
  },

  // Convert USD to ETB
  convertUSDToETB: async (usdAmount: number): Promise<number> => {
    const result = await fixerAPI.convertCurrency(usdAmount, 'USD', 'ETB');
    return result.convertedAmount;
  },

  // Convert ETB to USD
  convertETBToUSD: async (etbAmount: number): Promise<number> => {
    const result = await fixerAPI.convertCurrency(etbAmount, 'ETB', 'USD');
    return result.convertedAmount;
  },

  // Health check
  checkHealth: async (): Promise<boolean> => {
    const health = await fixerAPI.healthCheck();
    return health.available;
  },

  // Clear cache
  clearCache: (): void => {
    fixerAPI.clearCache();
  }
};
