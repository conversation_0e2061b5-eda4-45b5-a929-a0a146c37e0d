// Currency exchange rate API service
import { fixerCurrencyAPI } from './fixer';

export interface ExchangeRate {
  rate: number;
  lastUpdated: string;
  source: string;
}

export interface CurrencyResponse {
  success: boolean;
  rates: {
    USD: number;
    ETB: number;
  };
  timestamp: number;
  base: string;
}

// Multiple reliable exchange rate APIs with cross-checking
const EXCHANGE_RATE_APIS = [
  // Primary: ExchangeRate-API (free, reliable)
  {
    name: 'ExchangeRate-API',
    url: 'https://api.exchangerate-api.com/v4/latest/USD',
    parser: (data: any) => ({
      rate: data.rates?.ETB,
      lastUpdated: data.time_last_updated ? new Date(data.time_last_updated * 1000).toISOString() : new Date().toISOString(),
      source: 'ExchangeRate-API'
    })
  },
  // Secondary: Exchange Rates API (free, reliable)
  {
    name: 'Exchange Rates API',
    url: 'https://api.exchangerate.host/latest?base=USD&symbols=ETB',
    parser: (data: any) => ({
      rate: data.rates?.ETB,
      lastUpdated: data.date ? new Date(data.date).toISOString() : new Date().toISOString(),
      source: 'Exchange Rates API'
    })
  },
  // Tertiary: Fixer.io (backup - requires API key for HTTPS)
  {
    name: 'Fixer.io',
    url: `http://data.fixer.io/api/latest?access_key=${import.meta.env.VITE_FIXER_API_KEY}&base=USD&symbols=ETB`,
    parser: (data: any) => ({
      rate: data.rates?.ETB,
      lastUpdated: data.date ? new Date(data.date).toISOString() : new Date().toISOString(),
      source: 'Fixer.io'
    })
  },
  // Quaternary: CurrencyLayer (backup)
  {
    name: 'CurrencyLayer',
    url: 'http://api.currencylayer.com/live?access_key=YOUR_API_KEY&currencies=ETB&source=USD&format=1',
    parser: (data: any) => ({
      rate: data.quotes?.USDETB,
      lastUpdated: data.timestamp ? new Date(data.timestamp * 1000).toISOString() : new Date().toISOString(),
      source: 'CurrencyLayer'
    })
  },
  // Fifth: CurrencyLayer (additional backup)
  {
    name: 'CurrencyLayer',
    url: 'https://api.currencylayer.com/live?access_key=free&currencies=ETB&source=USD&format=1',
    parser: (data: any) => ({
      rate: data.quotes?.USDETB,
      lastUpdated: new Date(data.timestamp * 1000).toISOString(),
      source: 'CurrencyLayer'
    })
  },
  // Final fallback: CurrencyLayer (backup)
  {
    name: 'CurrencyLayer',
    url: 'http://api.currencylayer.com/live?access_key=free&currencies=ETB&source=USD&format=1',
    parser: (data: any) => ({
      rate: data.quotes?.USDETB,
      lastUpdated: new Date(data.timestamp * 1000).toISOString(),
      source: 'CurrencyLayer'
    })
  }
];

// Cache for exchange rate
let cachedRate: ExchangeRate | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes cache duration

// Manual override for exchange rate (can be set by admin)
let manualOverrideRate: ExchangeRate | null = null;

// Reasonable ETB/USD exchange rate bounds for validation (updated for 2024-2025)
const MIN_REASONABLE_RATE = 50; // Minimum reasonable ETB per USD
const MAX_REASONABLE_RATE = 70; // Maximum reasonable ETB per USD (updated for current market)
const FALLBACK_RATE = 58; // Reasonable fallback rate based on current market conditions

// Validate if exchange rate is reasonable
const isValidRate = (rate: number): boolean => {
  return rate && rate >= MIN_REASONABLE_RATE && rate <= MAX_REASONABLE_RATE;
};

// Cross-check rates from multiple sources
const crossCheckRates = async (): Promise<ExchangeRate[]> => {
  const validRates: ExchangeRate[] = [];

  // Try to get rates from multiple APIs simultaneously
  const promises = EXCHANGE_RATE_APIS.slice(0, 2).map(async (api) => { // Only use first 2 reliable APIs
    try {
      console.log(`🔄 Fetching from ${api.name}...`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout

      const response = await fetch(api.url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Nibret-Real-Estate/1.0',
          'Cache-Control': 'no-cache'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`📊 Raw data from ${api.name}:`, data);

      const exchangeRate = api.parser(data);

      // Validate the rate
      if (exchangeRate.rate && isValidRate(exchangeRate.rate)) {
        console.log(`✅ Valid rate from ${api.name}: ${exchangeRate.rate} ETB/USD`);
        return exchangeRate;
      } else {
        console.warn(`❌ Invalid rate from ${api.name}: ${exchangeRate.rate}`);
        return null;
      }
    } catch (error) {
      console.warn(`❌ ${api.name} failed:`, error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  });

  // Wait for all promises to settle
  const results = await Promise.allSettled(promises);

  // Collect valid rates
  results.forEach((result) => {
    if (result.status === 'fulfilled' && result.value) {
      validRates.push(result.value);
    }
  });

  return validRates;
};

export const currencyAPI = {
  // Get current USD to ETB exchange rate
  getExchangeRate: async (): Promise<ExchangeRate> => {
    try {
      // Check manual override first
      if (manualOverrideRate) {
        console.log('🔧 Using manual override rate:', manualOverrideRate.rate);
        return manualOverrideRate;
      }

      // Check cache first
      const now = Date.now();
      if (cachedRate && (now - cacheTimestamp) < CACHE_DURATION) {
        console.log('💰 Using cached exchange rate:', cachedRate.rate);
        return cachedRate;
      }

      console.log('🔄 Fetching fresh exchange rate...');

      // Try Fixer.io API first (primary source)
      try {
        console.log('🚀 Trying Fixer.io API first...');
        const fixerRate = await fixerCurrencyAPI.getExchangeRate();

        if (fixerRate && isValidRate(fixerRate.rate)) {
          console.log(`✅ Fixer.io rate: ${fixerRate.rate} ETB/USD`);
          cachedRate = fixerRate;
          cacheTimestamp = now;
          return fixerRate;
        } else {
          console.warn('❌ Fixer.io returned invalid rate, falling back to other APIs');
        }
      } catch (error) {
        console.warn('⚠️ Fixer.io API failed, falling back to other APIs:', error instanceof Error ? error.message : 'Unknown error');
      }

      console.log('🔄 Falling back to cross-checking with multiple APIs...');

      // Get rates from multiple sources
      const validRates = await crossCheckRates();

      if (validRates.length === 0) {
        console.warn('⚠️ No valid rates found from any API, using fallback');
        const fallbackRate: ExchangeRate = {
          rate: FALLBACK_RATE,
          lastUpdated: new Date().toISOString(),
          source: 'Fallback Rate'
        };

        cachedRate = fallbackRate;
        cacheTimestamp = now;
        return fallbackRate;
      }

      // If we have multiple rates, cross-check them
      if (validRates.length > 1) {
        const rates = validRates.map(r => r.rate);
        const avgRate = rates.reduce((sum, rate) => sum + rate, 0) / rates.length;
        const maxDeviation = Math.max(...rates.map(rate => Math.abs(rate - avgRate)));

        console.log(`📊 Cross-check results: ${validRates.length} sources`);
        console.log(`📊 Rates: ${rates.join(', ')}`);
        console.log(`📊 Average: ${avgRate.toFixed(2)}, Max deviation: ${maxDeviation.toFixed(2)}`);

        // If rates are too different (more than 3 ETB difference), use average or fallback
        if (maxDeviation > 3) {
          console.warn('⚠️ Exchange rates differ significantly');

          // If deviation is reasonable (3-5 ETB), use average
          if (maxDeviation <= 5) {
            const averageRate: ExchangeRate = {
              rate: Math.round(avgRate * 100) / 100, // Round to 2 decimal places
              lastUpdated: new Date().toISOString(),
              source: `Average of ${validRates.length} sources`
            };

            console.log(`✅ Using averaged rate: ${averageRate.rate} ETB/USD`);
            cachedRate = averageRate;
            cacheTimestamp = now;
            return averageRate;
          } else {
            // Too much deviation, use fallback
            const fallbackRate: ExchangeRate = {
              rate: FALLBACK_RATE,
              lastUpdated: new Date().toISOString(),
              source: 'Cross-check Fallback (High Deviation)'
            };

            cachedRate = fallbackRate;
            cacheTimestamp = now;
            return fallbackRate;
          }
        }

        // Use the most recent rate from the most reliable source
        const bestRate = validRates[0]; // First API is most reliable
        console.log(`✅ Using cross-checked rate: ${bestRate.rate} ETB/USD from ${bestRate.source}`);

        cachedRate = bestRate;
        cacheTimestamp = now;
        return bestRate;
      }

      // Single valid rate
      const singleRate = validRates[0];
      console.log(`✅ Using single valid rate: ${singleRate.rate} ETB/USD from ${singleRate.source}`);

      cachedRate = singleRate;
      cacheTimestamp = now;
      return singleRate;

    } catch (error) {
      console.error('💥 Error fetching exchange rate:', error);
      
      // Return cached rate if available, otherwise fallback
      if (cachedRate) {
        return cachedRate;
      }

      return {
        rate: FALLBACK_RATE,
        lastUpdated: new Date().toISOString(),
        source: 'Emergency Fallback'
      };
    }
  },

  // Convert USD to ETB
  convertUSDToETB: async (usdAmount: number): Promise<number> => {
    const exchangeRate = await currencyAPI.getExchangeRate();
    return usdAmount * exchangeRate.rate;
  },

  // Convert ETB to USD
  convertETBToUSD: async (etbAmount: number): Promise<number> => {
    const exchangeRate = await currencyAPI.getExchangeRate();
    return etbAmount / exchangeRate.rate;
  },

  // Format currency with proper symbols
  formatCurrency: (amount: number, currency: 'USD' | 'ETB'): string => {
    if (currency === 'USD') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    } else {
      return new Intl.NumberFormat('en-ET', {
        style: 'currency',
        currency: 'ETB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    }
  },

  // Clear cache (useful for testing)
  clearCache: (): void => {
    cachedRate = null;
    cacheTimestamp = 0;
  },

  // Health check for APIs
  checkAPIHealth: async (): Promise<{ [key: string]: boolean }> => {
    const healthStatus: { [key: string]: boolean } = {};

    // Check Fixer.io API first
    try {
      const fixerHealth = await fixerCurrencyAPI.checkHealth();
      healthStatus['Fixer.io API'] = fixerHealth;
    } catch (error) {
      healthStatus['Fixer.io API'] = false;
    }

    // Check other APIs
    const healthChecks = EXCHANGE_RATE_APIS.map(async (api) => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout for health check

        const response = await fetch(api.url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Nibret-Real-Estate/1.0'
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        healthStatus[api.name] = response.ok;
      } catch (error) {
        healthStatus[api.name] = false;
      }
    });

    await Promise.allSettled(healthChecks);
    return healthStatus;
  },

  // Get exchange rate with detailed source information
  getExchangeRateWithDetails: async (): Promise<{
    rate: ExchangeRate;
    sources: ExchangeRate[];
    health: { [key: string]: boolean };
  }> => {
    const [rate, health] = await Promise.all([
      currencyAPI.getExchangeRate(),
      currencyAPI.checkAPIHealth()
    ]);

    const sources = await crossCheckRates();

    return {
      rate,
      sources,
      health
    };
  },

  // Manual override functions (for admin use)
  setManualRate: (rate: number, source: string = 'Manual Override'): void => {
    if (isValidRate(rate)) {
      manualOverrideRate = {
        rate,
        lastUpdated: new Date().toISOString(),
        source
      };
      console.log(`🔧 Manual exchange rate set: ${rate} ETB/USD`);
    } else {
      console.error(`❌ Invalid manual rate: ${rate}`);
    }
  },

  clearManualRate: (): void => {
    manualOverrideRate = null;
    console.log('🔧 Manual exchange rate override cleared');
  },

  getManualRate: (): ExchangeRate | null => {
    return manualOverrideRate;
  },

  // Get current rate info (cached, manual, or fresh)
  getCurrentRateInfo: (): {
    isManual: boolean;
    isCached: boolean;
    cacheAge: number;
    rate: ExchangeRate | null;
  } => {
    const now = Date.now();
    return {
      isManual: !!manualOverrideRate,
      isCached: !!cachedRate && (now - cacheTimestamp) < CACHE_DURATION,
      cacheAge: cachedRate ? now - cacheTimestamp : 0,
      rate: manualOverrideRate || cachedRate
    };
  }
};
