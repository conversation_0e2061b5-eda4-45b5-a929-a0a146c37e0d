import { apiRequest, apiRequestWithRetry } from '../utils'

export interface UploadResponse {
  success: boolean
  data: {
    public_id: string
    secure_url: string
    width: number | null
    height: number | null
    format: string | null
    bytes: number | null
    urls: {
      original: string
      thumbnail: string
      medium: string
      large: string
      hero: string
    }
  }
  error?: string
}

export interface MultipleUploadResponse {
  success: boolean
  data: UploadResponse['data'][]
  count: number
  error?: string
}

// Upload API functions
export const uploadApi = {
  // Upload single image
  uploadImage: async (file: File): Promise<UploadResponse> => {
    try {
      console.log('📤 Uploading image:', file.name)

      const formData = new FormData()
      formData.append('image', file)

      // Use retry logic for image upload (3 attempts)
      const response = await apiRequestWithRetry('/upload/image', {
        method: 'POST',
        body: formData,
        // Don't set Content-Type header for FormData - let browser set it
        headers: {}
      }, 3)

      console.log('✅ Image uploaded successfully:', file.name)
      return response
    } catch (error) {
      console.error('💥 Image upload failed:', error)
      throw error
    }
  },

  // Upload multiple images
  uploadMultipleImages: async (files: File[]): Promise<MultipleUploadResponse> => {
    try {
      console.log('📤 Uploading multiple images:', files.length)

      const formData = new FormData()
      files.forEach(file => {
        formData.append('images', file)
      })

      // Use retry logic for multiple image upload (3 attempts)
      const response = await apiRequestWithRetry('/upload/images', {
        method: 'POST',
        body: formData,
        // Don't set Content-Type header for FormData - let browser set it
        headers: {}
      }, 3)

      console.log('✅ Multiple images uploaded successfully:', files.length)
      return response
    } catch (error) {
      console.error('💥 Multiple image upload failed:', error)
      throw error
    }
  },

  // Upload image from URL
  uploadFromUrl: async (url: string, filename?: string): Promise<UploadResponse> => {
    try {
      console.log('📤 Uploading image from URL:', url)

      // Use retry logic for URL upload (3 attempts)
      const response = await apiRequestWithRetry('/upload/url', {
        method: 'POST',
        body: JSON.stringify({ url, filename }),
      }, 3)

      console.log('✅ Image uploaded from URL successfully')
      return response
    } catch (error) {
      console.error('💥 URL image upload failed:', error)
      throw error
    }
  },

  // Upload base64 image
  uploadBase64: async (base64Data: string, filename?: string): Promise<UploadResponse> => {
    try {
      console.log('📤 Uploading base64 image')

      // Use retry logic for base64 upload (3 attempts)
      const response = await apiRequestWithRetry('/upload/base64', {
        method: 'POST',
        body: JSON.stringify({ image: base64Data, filename }),
      }, 3)

      console.log('✅ Base64 image uploaded successfully')
      return response
    } catch (error) {
      console.error('💥 Base64 image upload failed:', error)
      throw error
    }
  },

  // Delete image
  deleteImage: async (publicId: string): Promise<{ success: boolean; message?: string; error?: string }> => {
    try {
      console.log('🗑️ Deleting image:', publicId)

      // Use retry logic for image deletion (3 attempts)
      const response = await apiRequestWithRetry('/upload/delete', {
        method: 'DELETE',
        body: JSON.stringify({ public_id: publicId }),
      }, 3)

      console.log('✅ Image deleted successfully:', publicId)
      return response
    } catch (error) {
      console.error('💥 Image deletion failed:', error)
      throw error
    }
  },

  // Get image info
  getImageInfo: async (publicId: string): Promise<{
    success: boolean
    data: {
      public_id: string
      urls: {
        original: string
        thumbnail: string
        medium: string
        large: string
        hero: string
      }
    }
    error?: string
  }> => {
    try {
      console.log('ℹ️ Getting image info:', publicId)

      const response = await apiRequest(`/upload/info/${publicId}`)

      console.log('✅ Image info retrieved successfully:', publicId)
      return response
    } catch (error) {
      console.error('💥 Get image info failed:', error)
      throw error
    }
  },
}
