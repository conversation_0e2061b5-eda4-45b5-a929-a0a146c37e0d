import { apiRequest, apiRequestWithRetry, setAuthToken, removeAuthToken } from '../utils'

export interface User {
  id: string
  first_name: string
  last_name: string
  email: string
  phone: string
  role: 'ADMIN' | 'CUSTOMER'
  created_at: string
  is_active: boolean
}

export interface LoginRequest {
  username: string // This is actually email in the backend
  password: string
}

export interface RegisterRequest {
  first_name: string
  last_name: string
  email: string
  phone: string
  password: string
}

export interface AuthResponse {
  success: boolean
  access_token: string
  token_type: string
  user: User
}

export interface UserResponse {
  success: boolean
  user: User
}

// Auth API functions
export const authApi = {
  // Login user with retry logic
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    try {
      console.log('🔐 Attempting login with:', credentials.username)

      // Use JSON format like the properties endpoint
      const requestBody = {
        username: credentials.username,
        password: credentials.password
      }

      // Use retry logic for login (6 attempts with exponential backoff)
      const response = await apiRequestWithRetry('/accounts/login/', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      }, 6)

      console.log('✅ Login successful:', response)

      // Store the token
      if (response.access_token) {
        setAuthToken(response.access_token)
      }

      return response
    } catch (error) {
      console.error('💥 Login failed after all retries:', error)
      throw error
    }
  },

  // Register user with retry logic
  register: async (userData: RegisterRequest): Promise<AuthResponse> => {
    try {
      console.log('📝 Attempting registration with:', userData.email)

      // Use retry logic for registration (6 attempts with exponential backoff)
      const response = await apiRequestWithRetry('/accounts/registration', {
        method: 'POST',
        body: JSON.stringify(userData),
      }, 6)

      console.log('✅ Registration successful:', response)

      // Store the token
      if (response.access_token) {
        setAuthToken(response.access_token)
      }

      return response
    } catch (error) {
      console.error('💥 Registration failed after all retries:', error)
      throw error
    }
  },

  // Get current user with retry logic
  getCurrentUser: async (): Promise<User> => {
    try {
      // Use retry logic for getting current user (5 attempts)
      const response: UserResponse = await apiRequestWithRetry('/accounts/users/me', {}, 5)
      return response.user
    } catch (error) {
      console.error('💥 Get current user failed after all retries:', error)
      throw error
    }
  },

  // Get all users (admin only) with retry logic
  getAllUsers: async (): Promise<User[]> => {
    try {
      // Use retry logic for getting all users (5 attempts)
      const response: { success: boolean; users: User[] } = await apiRequestWithRetry('/accounts/users', {}, 5)
      return response.users
    } catch (error) {
      console.error('💥 Get all users failed after all retries:', error)
      throw error
    }
  },

  // Logout user
  logout: () => {
    removeAuthToken()
  },
}
