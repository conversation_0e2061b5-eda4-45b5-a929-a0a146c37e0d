import { apiRequest, apiRequestWithRetry } from '../utils'

export interface Property {
  id: string
  title: string
  price: number
  currency?: 'ETB' | 'USD'
  beds: number
  baths: number
  sqm: number
  address: string
  lat?: number
  lng?: number
  propertyType: string
  status: string
  publish_status?: 'draft' | 'published' | 'archived' | 'pending_review'
  listing_type?: 'sale' | 'rent' | 'both'
  images: string[]
  description?: string
  yearBuilt?: number
  lotSize?: number
  features?: string[]
  owner?: string
  views?: number
  is_featured?: boolean
  is_negotiable?: boolean
  discount_percentage?: number
  original_price?: number
  published_at?: string
  archived_at?: string
  contact_info?: {
    phone?: string
    email?: string
    agent_name?: string
  }
  created_at?: string
  updated_at?: string
  admin_tags?: string[]
  admin_notes?: string
  priority_level?: 'low' | 'normal' | 'high' | 'urgent'
}

export interface PropertyFilters {
  search?: string
  type?: string[]
  min_price?: number
  max_price?: number
  bedroom?: number
  bathroom?: number
  status?: string
}

export interface PropertyCreate {
  title: string
  price: number
  currency?: 'ETB' | 'USD'
  beds: number
  baths: number
  sqm: number
  address: string
  lat: number
  lng: number
  propertyType: string
  status: string
  images: string[]
  description?: string
  yearBuilt?: number
  lotSize?: number
}

// Property API functions
export const propertyApi = {
  // Get all properties with optional filters (with retry logic)
  getProperties: async (filters?: PropertyFilters): Promise<Property[]> => {
    try {
      console.log('🏠 Fetching properties with filters:', filters)

      // Based on the curl command, this endpoint expects a POST request with credentials
      const requestBody = {
        username: "0965789832",
        password: "nibretadmin",
        ...filters // Include any filters in the request body
      }

      // Use retry logic for fetching properties (5 attempts)
      const response = await apiRequestWithRetry('/properties/list', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      }, 5)

      // Handle different response formats
      if (Array.isArray(response)) {
        console.log(`✅ Fetched ${response.length} properties`)
        return response
      } else if (response.properties && Array.isArray(response.properties)) {
        console.log(`✅ Fetched ${response.properties.length} properties`)
        return response.properties
      } else if (response.data && Array.isArray(response.data)) {
        console.log(`✅ Fetched ${response.data.length} properties`)
        return response.data
      } else {
        console.warn('⚠️ Unexpected response format:', response)
        return []
      }
    } catch (error) {
      console.error('💥 Error fetching properties after all retries:', error)
      throw error
    }
  },

  // Get a single property by ID (with retry logic)
  getProperty: async (id: string): Promise<Property> => {
    try {
      console.log('🏠 Fetching property:', id)

      // Use GET request for fetching single property (5 attempts)
      const response = await apiRequestWithRetry(`/properties/${id}`, {
        method: 'GET',
      }, 5)

      // Handle different response formats from backend
      if (response.success && response.data) {
        console.log('✅ Property fetched successfully:', response.data.title || response.data.id)
        return response.data
      } else if (response.id) {
        // Direct property object response
        console.log('✅ Property fetched successfully:', response.title || response.id)
        return response
      } else {
        throw new Error('Property not found or invalid response format')
      }
    } catch (error) {
      console.error('💥 Error fetching property after all retries:', error)
      throw error
    }
  },

  // Create a new property (with retry logic)
  createProperty: async (property: PropertyCreate): Promise<Property> => {
    try {
      console.log('🏗️ Creating new property:', property.title)

      // Include embedded credentials for compatibility
      const requestBody = {
        username: "0965789832",
        password: "nibretadmin",
        ...property
      };

      // Use retry logic for creating property (6 attempts - important operation)
      const response = await apiRequestWithRetry('/properties', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      }, 6);

      console.log('✅ Property created successfully:', property.title)

      // Handle different response formats
      if (response.data) {
        return response.data;
      } else if (response.success && response.data) {
        return response.data;
      } else {
        return response;
      }
    } catch (error) {
      console.error('💥 Error creating property after all retries:', error)
      throw error
    }
  },

  // Update property status
  updatePropertyStatus: async (id: string): Promise<Property> => {
    return apiRequest(`/properties/${id}/status`, {
      method: 'PATCH',
    })
  },

  // Get nearby properties
  getNearbyProperties: async (lat: number, lng: number, radius: number = 5.0): Promise<Property[]> => {
    const queryParams = new URLSearchParams({
      lat: lat.toString(),
      lng: lng.toString(),
      radius: radius.toString(),
    })

    return apiRequest(`/properties/nearby?${queryParams.toString()}`)
  },

  // Get property statistics
  getPropertyStats: async () => {
    return apiRequest('/properties/stats/breakdown')
  },

  // Get monthly property data
  getMonthlyStats: async () => {
    return apiRequest('/properties/stats/monthly')
  },

  // Get all property owners
  getPropertyOwners: async () => {
    return apiRequest('/properties/owner')
  },

  // Create property owner
  createPropertyOwner: async (ownerData: any) => {
    return apiRequest('/properties/owner', {
      method: 'POST',
      body: JSON.stringify(ownerData),
    })
  },



  // Get current user's properties
  getMyProperties: async (filters?: { publish_status?: string; status?: string; page?: number; limit?: number }): Promise<{ data: Property[]; pagination: any }> => {
    const queryParams = new URLSearchParams()
    if (filters?.publish_status) queryParams.append('publish_status', filters.publish_status)
    if (filters?.status) queryParams.append('status', filters.status)
    if (filters?.page) queryParams.append('page', filters.page.toString())
    if (filters?.limit) queryParams.append('limit', filters.limit.toString())

    return apiRequest(`/properties/my-properties?${queryParams.toString()}`, {
      method: 'GET',
    })
  },

  // Publish a property
  publishProperty: async (id: string): Promise<Property> => {
    return apiRequest(`/properties/${id}/publish`, {
      method: 'PATCH',
    })
  },

  // Archive a property
  archiveProperty: async (id: string): Promise<Property> => {
    return apiRequest(`/properties/${id}/archive`, {
      method: 'PATCH',
    })
  },

  // Set property as draft
  setPropertyAsDraft: async (id: string): Promise<Property> => {
    return apiRequest(`/properties/${id}/draft`, {
      method: 'PATCH',
    })
  },

  // Update a property
  updateProperty: async (id: string, property: Partial<PropertyCreate>): Promise<Property> => {
    try {
      console.log('🔄 Updating property:', id)

      // Include embedded credentials for compatibility
      const requestBody = {
        username: "0965789832",
        password: "nibretadmin",
        ...property
      };

      // Use retry logic for updating property (5 attempts)
      const response = await apiRequestWithRetry(`/properties/${id}`, {
        method: 'PUT',
        body: JSON.stringify(requestBody),
      }, 5);

      console.log('✅ Property updated successfully:', id)
      return response.data || response;
    } catch (error) {
      console.error('💥 Error updating property:', error)
      throw error
    }
  },

  // Delete a property
  deleteProperty: async (id: string): Promise<void> => {
    return apiRequest(`/properties/${id}`, {
      method: 'DELETE',
    })
  },

  // Admin: Toggle featured status of a property
  toggleFeatured: async (propertyId: string): Promise<Property> => {
    try {
      console.log('⭐ Toggling featured status for property:', propertyId)

      const response = await apiRequestWithRetry(`/properties/${propertyId}/feature`, {
        method: 'PATCH',
      }, 3)

      console.log('✅ Featured status toggled successfully')
      return response.data
    } catch (error) {
      console.error('💥 Error toggling featured status:', error)
      throw error
    }
  },

  // Admin: Bulk toggle featured status for multiple properties
  bulkToggleFeatured: async (propertyIds: string[], isFeatured: boolean): Promise<Property[]> => {
    try {
      console.log('⭐ Bulk toggling featured status for properties:', propertyIds.length)

      const response = await apiRequestWithRetry('/properties/bulk-feature', {
        method: 'POST',
        body: JSON.stringify({
          property_ids: propertyIds,
          is_featured: isFeatured
        }),
      }, 3)

      console.log(`✅ ${response.modified_count} properties featured status updated`)
      return response.data
    } catch (error) {
      console.error('💥 Error bulk toggling featured status:', error)
      throw error
    }
  },

  // Get featured properties only
  getFeaturedProperties: async (page: number = 1, limit: number = 10): Promise<{
    properties: Property[]
    pagination: {
      current_page: number
      total_pages: number
      total_properties: number
      per_page: number
    }
  }> => {
    try {
      console.log('⭐ Fetching featured properties')

      const response = await apiRequestWithRetry(`/properties/featured?page=${page}&limit=${limit}`, {
        method: 'GET',
      }, 3)

      console.log(`✅ Fetched ${response.data.length} featured properties`)
      return {
        properties: response.data,
        pagination: response.pagination
      }
    } catch (error) {
      console.error('💥 Error fetching featured properties:', error)
      throw error
    }
  },
}
