import { apiRequest } from '../utils'

export interface Customer {
  id: string
  first_name: string
  last_name: string
  email?: string
  phone: string
}

// Lead interface moved to leads.ts file

// Customer API functions
export const customerApi = {
  // Get all customers
  getCustomers: async (): Promise<{ customers: Customer[] }> => {
    return apiRequest('/customers')
  },

  // Create a new customer
  createCustomer: async (customerData: Omit<Customer, 'id'>): Promise<Customer> => {
    return apiRequest('/customers', {
      method: 'POST',
      body: JSON.stringify(customerData),
    })
  },

  // Update customer
  updateCustomer: async (id: string, customerData: Partial<Customer>): Promise<Customer> => {
    return apiRequest(`/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(customerData),
    })
  },

  // Delete customer
  deleteCustomer: async (id: string): Promise<boolean> => {
    return apiRequest(`/customers/${id}`, {
      method: 'DELETE',
    })
  },

  // Get customer by ID
  getCustomer: async (id: string): Promise<Customer> => {
    return apiRequest(`/customers/${id}`)
  },
}

// Lead management functions moved to leads.ts file
