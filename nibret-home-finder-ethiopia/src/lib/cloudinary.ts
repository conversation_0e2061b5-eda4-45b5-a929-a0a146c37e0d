import { Cloudinary } from '@cloudinary/url-gen';
import { auto } from '@cloudinary/url-gen/actions/resize';
import { autoGravity } from '@cloudinary/url-gen/qualifiers/gravity';
import { format, quality } from '@cloudinary/url-gen/actions/delivery';

// Initialize Cloudinary instance
const cld = new Cloudinary({
  cloud: {
    cloudName: import.meta.env.VITE_CLOUDINARY_CLOUD_NAME || 'your-cloud-name'
  }
});

// Image transformation presets
export const imageTransforms = {
  thumbnail: {
    width: 300,
    height: 200,
    crop: 'fill',
    gravity: 'center',
    quality: 'auto:good'
  },
  medium: {
    width: 600,
    height: 400,
    crop: 'fill',
    gravity: 'center',
    quality: 'auto:good'
  },
  large: {
    width: 1200,
    height: 800,
    crop: 'limit',
    quality: 'auto:good'
  },
  hero: {
    width: 1920,
    height: 1080,
    crop: 'fill',
    gravity: 'center',
    quality: 'auto:best'
  }
};

// Generate optimized image URL
export const getOptimizedImageUrl = (
  publicId: string,
  transform: keyof typeof imageTransforms = 'medium'
) => {
  if (!publicId) return '';

  // Check if this is a local storage URL (contains localhost or file extension)
  if (publicId.includes('localhost') || publicId.includes('/uploads/') || publicId.match(/\.(jpg|jpeg|png|webp|gif)$/i)) {
    // For local storage, return the URL as-is since we can't transform it
    return publicId;
  }

  // For Cloudinary URLs, apply transformations
  try {
    const img = cld.image(publicId);
    const transformConfig = imageTransforms[transform];

    return img
      .resize(auto().width(transformConfig.width).height(transformConfig.height))
      .gravity(autoGravity())
      .delivery(format('auto'))
      .delivery(quality('auto:good'))
      .toURL();
  } catch (error) {
    // If transformation fails, return the original publicId
    console.warn('Failed to generate optimized URL, using original:', error);
    return publicId;
  }
};

// Generate multiple image URLs for different sizes
export const getImageUrls = (publicId: string) => {
  if (!publicId) {
    return {
      original: '',
      thumbnail: '',
      medium: '',
      large: '',
      hero: ''
    };
  }

  return {
    original: cld.image(publicId).toURL(),
    thumbnail: getOptimizedImageUrl(publicId, 'thumbnail'),
    medium: getOptimizedImageUrl(publicId, 'medium'),
    large: getOptimizedImageUrl(publicId, 'large'),
    hero: getOptimizedImageUrl(publicId, 'hero')
  };
};

// Upload image to Cloudinary via backend
export const uploadImage = async (file: File): Promise<{
  public_id: string;
  secure_url: string;
  width: number;
  height: number;
}> => {
  const formData = new FormData();
  formData.append('image', file);

  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';
  const response = await fetch(`${API_BASE_URL}/upload/image`, {
    method: 'POST',
    body: formData,
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to upload image');
  }

  const result = await response.json();
  return result.data;
};

// Upload multiple images
export const uploadMultipleImages = async (files: File[]): Promise<Array<{
  public_id: string;
  secure_url: string;
  width: number;
  height: number;
}>> => {
  const uploadPromises = files.map(file => uploadImage(file));
  return Promise.all(uploadPromises);
};

// Delete image from Cloudinary via backend
export const deleteImage = async (publicId: string): Promise<void> => {
  const response = await fetch('/api/upload/delete', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ public_id: publicId }),
  });

  if (!response.ok) {
    throw new Error('Failed to delete image');
  }
};

// Validate image file
export const validateImageFile = (file: File): { valid: boolean; error?: string } => {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return { valid: false, error: 'File must be an image' };
  }

  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return { valid: false, error: 'Image must be less than 10MB' };
  }

  // Check file format
  const allowedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
  if (!allowedFormats.includes(file.type)) {
    return { valid: false, error: 'Image must be JPEG, PNG, WebP, or GIF format' };
  }

  return { valid: true };
};

// Extract public ID from Cloudinary URL or return local URL for local storage
export const extractPublicId = (url: string): string => {
  if (!url) return '';

  // Handle local storage URLs (return the full URL as the "public ID")
  if (url.includes('localhost') || url.includes('/uploads/')) {
    return url;
  }

  // Handle Cloudinary URLs
  const cloudinaryRegex = /\/v\d+\/(.+)\.(jpg|jpeg|png|webp|gif)$/i;
  const match = url.match(cloudinaryRegex);

  if (match) {
    return match[1];
  }

  // For other URLs, return the URL itself
  return url;
};

// Generate responsive image srcSet
export const generateSrcSet = (publicId: string): string => {
  if (!publicId) return '';

  const sizes = [
    { width: 400, suffix: '400w' },
    { width: 600, suffix: '600w' },
    { width: 800, suffix: '800w' },
    { width: 1200, suffix: '1200w' },
    { width: 1600, suffix: '1600w' }
  ];

  return sizes
    .map(size => {
      const img = cld.image(publicId)
        .resize(auto().width(size.width))
        .gravity(autoGravity())
        .delivery(format('auto'))
        .delivery(quality('auto:good'));
      return `${img.toURL()} ${size.suffix}`;
    })
    .join(', ');
};

export { cld };
