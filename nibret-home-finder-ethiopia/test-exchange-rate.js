// Test script to check exchange rate functionality
import { currencyAPI } from './src/lib/api/currency.js';

async function testExchangeRate() {
  console.log('🧪 Testing Exchange Rate Functionality...\n');
  
  try {
    // Test 1: Get current exchange rate
    console.log('📊 Test 1: Getting current exchange rate...');
    const rate = await currencyAPI.getExchangeRate();
    console.log('✅ Exchange Rate:', rate);
    console.log('   Rate:', rate.rate, 'ETB/USD');
    console.log('   Source:', rate.source);
    console.log('   Last Updated:', rate.lastUpdated);
    console.log('');

    // Test 2: Convert USD to ETB
    console.log('💱 Test 2: Converting $100 USD to ETB...');
    const etbAmount = await currencyAPI.convertUSDToETB(100);
    console.log('✅ $100 USD =', etbAmount.toFixed(2), 'ETB');
    console.log('');

    // Test 3: Convert ETB to USD
    console.log('💱 Test 3: Converting 10,000 ETB to USD...');
    const usdAmount = await currencyAPI.convertETBToUSD(10000);
    console.log('✅ 10,000 ETB = $', usdAmount.toFixed(2), 'USD');
    console.log('');

    // Test 4: Check API health
    console.log('🏥 Test 4: Checking API health...');
    const health = await currencyAPI.checkAPIHealth();
    console.log('✅ API Health Status:');
    Object.entries(health).forEach(([api, status]) => {
      console.log(`   ${api}: ${status ? '✅ Healthy' : '❌ Down'}`);
    });
    console.log('');

    // Test 5: Get detailed exchange rate info
    console.log('📋 Test 5: Getting detailed exchange rate info...');
    const details = await currencyAPI.getExchangeRateWithDetails();
    console.log('✅ Detailed Info:');
    console.log('   Current Rate:', details.rate.rate, 'ETB/USD');
    console.log('   Sources Available:', details.sources.length);
    console.log('   API Health:', Object.keys(details.health).length, 'APIs checked');
    console.log('');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testExchangeRate().then(() => {
  console.log('🎉 Exchange rate test completed!');
}).catch((error) => {
  console.error('💥 Test script failed:', error);
});
