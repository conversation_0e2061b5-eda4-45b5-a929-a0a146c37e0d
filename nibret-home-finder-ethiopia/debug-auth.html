<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Authentication</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Authentication Debug Tool</h1>
    
    <div class="section">
        <h2>Current Storage State</h2>
        <button onclick="checkStorage()">Check localStorage</button>
        <div id="storageInfo"></div>
    </div>
    
    <div class="section">
        <h2>Test Login</h2>
        <button onclick="testLogin()">Login as Admin</button>
        <div id="loginResult"></div>
    </div>
    
    <div class="section">
        <h2>Test API Call</h2>
        <button onclick="testMyProperties()">Test /my-properties</button>
        <div id="apiResult"></div>
    </div>
    
    <div class="section">
        <h2>Clear Storage</h2>
        <button onclick="clearStorage()">Clear All Auth Data</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001';
        
        function checkStorage() {
            const token = localStorage.getItem('auth_token');
            const user = localStorage.getItem('user');
            
            let html = '<h3>localStorage Contents:</h3>';
            html += `<p><strong>auth_token:</strong> ${token ? token.substring(0, 30) + '...' : 'Not found'}</p>`;
            html += `<p><strong>user:</strong> ${user ? 'Found' : 'Not found'}</p>`;
            
            if (user) {
                try {
                    const parsedUser = JSON.parse(user);
                    html += `<pre>${JSON.stringify(parsedUser, null, 2)}</pre>`;
                } catch (e) {
                    html += `<p class="error">Error parsing user: ${e.message}</p>`;
                }
            }
            
            document.getElementById('storageInfo').innerHTML = html;
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<p class="info">Logging in...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/accounts/login/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: '**********',
                        password: 'nibretadmin'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.access_token) {
                    localStorage.setItem('auth_token', data.access_token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    
                    resultDiv.innerHTML = `
                        <p class="success">✅ Login successful!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <p class="error">❌ Login failed</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ Error: ${error.message}</p>`;
            }
        }
        
        async function testMyProperties() {
            const resultDiv = document.getElementById('apiResult');
            const token = localStorage.getItem('auth_token');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="error">❌ No token found. Please login first.</p>';
                return;
            }
            
            resultDiv.innerHTML = '<p class="info">Testing API call...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/properties/my-properties`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ API call successful!</p>
                        <p>Status: ${response.status}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <p class="error">❌ API call failed</p>
                        <p>Status: ${response.status}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ Error: ${error.message}</p>`;
            }
        }
        
        function clearStorage() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user');
            alert('Storage cleared!');
            checkStorage();
        }
        
        // Check storage on page load
        window.onload = checkStorage;
    </script>
</body>
</html>
