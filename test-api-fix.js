// Test script to verify the API fix for PropertyDetails
const API_BASE_URL = 'https://nibret-api.onrender.com';

async function testPropertyAPI() {
  console.log('🧪 Testing Property Details API Fix...\n');

  const testCases = [
    {
      id: '684698c27a749cbf92bf8119',
      name: 'Platinum Plaza - Bole'
    },
    {
      id: '684698c57a749cbf92bf811d', 
      name: 'Property from error message'
    },
    {
      id: '683ebd66857bc953e9d088b5',
      name: 'Villa in Megenagna'
    }
  ];

  for (const testCase of testCases) {
    console.log(`🔍 Testing Property: ${testCase.name}`);
    console.log(`   ID: ${testCase.id}`);
    
    try {
      // Test GET request (correct method)
      console.log('   Making GET request...');
      const getResponse = await fetch(`${API_BASE_URL}/properties/${testCase.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (getResponse.ok) {
        const data = await getResponse.json();
        console.log(`   ✅ GET Success: ${data.data?.title || 'Property found'}`);
        console.log(`   📊 Price: ${data.data?.price} ${data.data?.currency || 'ETB'}`);
        console.log(`   🏠 Type: ${data.data?.propertyType}`);
        console.log(`   📍 Address: ${data.data?.address}`);
      } else {
        console.log(`   ❌ GET Failed: ${getResponse.status} ${getResponse.statusText}`);
        const errorData = await getResponse.text();
        console.log(`   Error: ${errorData}`);
      }

      // Test POST request (should fail - this was the bug)
      console.log('   Testing POST request (should fail)...');
      const postResponse = await fetch(`${API_BASE_URL}/properties/${testCase.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: "0965789832",
          password: "nibretadmin",
          id: testCase.id
        })
      });

      if (postResponse.ok) {
        console.log(`   ⚠️  POST Unexpectedly succeeded (this should fail)`);
      } else {
        const errorData = await postResponse.json();
        console.log(`   ✅ POST correctly failed: ${errorData.error || errorData.message}`);
      }

    } catch (error) {
      console.log(`   💥 Network Error: ${error.message}`);
    }

    console.log(''); // Empty line for readability
  }

  console.log('🎯 Test Summary:');
  console.log('   - GET requests should succeed for valid property IDs');
  console.log('   - POST requests should fail with "Route not found"');
  console.log('   - Frontend should now work with PropertyDetails pages');
  console.log('\n✅ API Fix Test Complete!');
}

// Run the test
testPropertyAPI().catch(console.error);
