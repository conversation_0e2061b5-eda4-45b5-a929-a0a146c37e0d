{"name": "nibret-express-api", "version": "1.0.0", "description": "Express.js backend for Nibret Real Estate platform", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "seed": "node src/scripts/seed.js", "create-super-admin": "node src/scripts/create-super-admin.js", "setup": "npm run seed && npm run create-super-admin"}, "keywords": ["real-estate", "api", "express", "nodejs"], "author": "Nibret Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}