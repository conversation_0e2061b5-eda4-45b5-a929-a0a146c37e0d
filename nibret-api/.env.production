# Server Configuration - Production
PORT=3000
NODE_ENV=production

# Database Configuration - Production
MONGODB_URI=mongodb+srv://username:<EMAIL>/nibret_production

# JWT Configuration - Production
JWT_SECRET=your-super-secure-production-jwt-secret-here
JWT_EXPIRES_IN=24h

# Cloudinary Configuration - Production
CLOUDINARY_CLOUD_NAME=nibret-production
CLOUDINARY_API_KEY=your-production-api-key
CLOUDINARY_API_SECRET=your-production-api-secret

# CORS Configuration - Production
ALLOWED_ORIGINS=https://nibret.com,https://www.nibret.com,https://app.nibret.com

# Rate Limiting - Production (stricter)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# File Upload Limits - Production
MAX_FILE_SIZE=10485760
MAX_FILES_PER_REQUEST=10

# Email Configuration - Production
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-production-email-password

# API Configuration - Production
API_VERSION=v1
API_PREFIX=/api

# Security - Production
BCRYPT_ROUNDS=14
SESSION_SECRET=your-production-session-secret-here

# Logging - Production
LOG_LEVEL=warn
LOG_FILE=logs/production.log

# Frontend Configuration - Production
FRONTEND_URL=https://nibret.com
