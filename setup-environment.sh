#!/bin/bash

# Nibret Platform Environment Setup Script
echo "🏠 Nibret Real Estate Platform - Environment Setup"
echo "=================================================="

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    read -p "$prompt [$default]: " input
    if [ -z "$input" ]; then
        eval "$var_name='$default'"
    else
        eval "$var_name='$input'"
    fi
}

# Function to prompt for sensitive input
prompt_sensitive() {
    local prompt="$1"
    local var_name="$2"
    
    read -s -p "$prompt: " input
    echo
    eval "$var_name='$input'"
}

echo
echo "🔧 Setting up environment variables..."
echo

# Backend Configuration
echo "📡 Backend Configuration"
echo "----------------------"

prompt_with_default "Backend Port" "3000" "BACKEND_PORT"
prompt_with_default "Environment (development/production)" "development" "NODE_ENV"

if [ "$NODE_ENV" = "production" ]; then
    prompt_with_default "Production API Domain" "api.nibret.com" "API_DOMAIN"
    BACKEND_URL="https://$API_DOMAIN"
else
    BACKEND_URL="http://localhost:$BACKEND_PORT"
fi

echo
echo "🗄️  Database Configuration"
echo "-------------------------"

if [ "$NODE_ENV" = "production" ]; then
    echo "For production, please provide MongoDB Atlas connection string:"
    prompt_sensitive "MongoDB URI" "MONGODB_URI"
else
    prompt_with_default "MongoDB URI" "mongodb://localhost:27017/nibret_db" "MONGODB_URI"
fi

echo
echo "🔐 Security Configuration"
echo "------------------------"

if [ "$NODE_ENV" = "production" ]; then
    echo "Generating secure JWT secret for production..."
    JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
    echo "✅ JWT Secret generated"
else
    prompt_with_default "JWT Secret" "your-super-secret-jwt-key-here" "JWT_SECRET"
fi

echo
echo "☁️  Cloudinary Configuration"
echo "---------------------------"

prompt_with_default "Cloudinary Cloud Name" "your-cloud-name" "CLOUDINARY_CLOUD_NAME"
prompt_sensitive "Cloudinary API Key" "CLOUDINARY_API_KEY"
prompt_sensitive "Cloudinary API Secret" "CLOUDINARY_API_SECRET"

echo
echo "🌐 CORS Configuration"
echo "--------------------"

if [ "$NODE_ENV" = "production" ]; then
    prompt_with_default "Frontend Domain" "nibret.com" "FRONTEND_DOMAIN"
    ALLOWED_ORIGINS="https://$FRONTEND_DOMAIN,https://www.$FRONTEND_DOMAIN"
    FRONTEND_URL="https://$FRONTEND_DOMAIN"
else
    ALLOWED_ORIGINS="*"
    FRONTEND_URL="http://localhost:5173"
fi

echo
echo "📝 Creating environment files..."

# Create backend .env file
cat > nibret-api/.env << EOF
# Server Configuration
PORT=$BACKEND_PORT
NODE_ENV=$NODE_ENV

# Database Configuration
MONGODB_URI=$MONGODB_URI

# JWT Configuration
JWT_SECRET=$JWT_SECRET
JWT_EXPIRES_IN=24h

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=$CLOUDINARY_CLOUD_NAME
CLOUDINARY_API_KEY=$CLOUDINARY_API_KEY
CLOUDINARY_API_SECRET=$CLOUDINARY_API_SECRET

# CORS Configuration
ALLOWED_ORIGINS=$ALLOWED_ORIGINS

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILE_SIZE=10485760
MAX_FILES_PER_REQUEST=10

# Frontend Configuration
FRONTEND_URL=$FRONTEND_URL
EOF

# Create frontend .env file
cat > nibret-home-finder-ethiopia/.env.local << EOF
# API Configuration
VITE_API_BASE_URL=$BACKEND_URL
VITE_API_TIMEOUT=10000

# Cloudinary Configuration
VITE_CLOUDINARY_CLOUD_NAME=$CLOUDINARY_CLOUD_NAME
VITE_CLOUDINARY_API_KEY=$CLOUDINARY_API_KEY

# Environment
VITE_NODE_ENV=$NODE_ENV

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_CHAT=false

# App Configuration
VITE_APP_NAME=Nibret
VITE_APP_VERSION=1.0.0
EOF

echo "✅ Backend environment file created: nibret-api/.env"
echo "✅ Frontend environment file created: nibret-home-finder-ethiopia/.env.local"

echo
echo "🚀 Installation and Setup"
echo "========================"

# Install backend dependencies
echo "📦 Installing backend dependencies..."
cd nibret-api
npm install

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd ../nibret-home-finder-ethiopia
npm install

echo
echo "🎉 Setup Complete!"
echo "=================="
echo
echo "📋 Configuration Summary:"
echo "  Environment: $NODE_ENV"
echo "  Backend URL: $BACKEND_URL"
echo "  Frontend URL: $FRONTEND_URL"
echo "  Database: $MONGODB_URI"
echo "  Cloudinary: $CLOUDINARY_CLOUD_NAME"
echo
echo "🚀 To start the application:"
echo
echo "  Backend:"
echo "    cd nibret-api"
echo "    npm start"
echo
echo "  Frontend:"
echo "    cd nibret-home-finder-ethiopia"
echo "    npm run dev"
echo
echo "🌐 URLs:"
echo "  Frontend: $FRONTEND_URL"
echo "  Backend Health: $BACKEND_URL/health"
echo "  API Docs: $BACKEND_URL/api-docs (if available)"
echo
echo "📚 For deployment instructions, see DEPLOYMENT.md"

if [ "$NODE_ENV" = "production" ]; then
    echo
    echo "⚠️  Production Notes:"
    echo "  - Ensure your domain DNS is configured"
    echo "  - Set up SSL certificates"
    echo "  - Configure your web server (nginx/apache)"
    echo "  - Set up monitoring and backups"
    echo "  - Review security settings"
fi

echo
echo "✨ Happy coding with Nibret! 🏠"
